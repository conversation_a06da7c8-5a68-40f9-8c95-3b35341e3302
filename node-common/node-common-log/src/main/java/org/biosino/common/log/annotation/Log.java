package org.biosino.common.log.annotation;

import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.log.enums.SytemEnum;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 *
 * <AUTHOR>
 */
@Documented
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Log {
    /**
     * 系统
     */
    public SytemEnum system() default SytemEnum.ADMIN;

    /**
     * 一级模块
     */
    public String module1() default "";

    /**
     * 二级模块
     */
    public String module2() default "";

    /**
     * 三级模块
     */
    public String module3() default "";

    /**
     * 其他描述信息
     */
    String desc() default "";

    /**
     * 功能
     */
    public BusinessType businessType() default BusinessType.OTHER;

    /**
     * 是否保存请求的参数
     */
    public boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    public boolean isSaveResponseData() default true;

    /**
     * 排除指定的请求参数
     */
    public String[] excludeParamNames() default {};
}
