package org.biosino.common.es.entity;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.AnnotationConstants;
import org.dromara.easyes.annotation.rely.FieldType;

@Data
@IndexName(value = MicroarrayPlatformEs.MP_INDEX_NAME)
@Settings(maxResultWindow = 100 * AnnotationConstants.DEFAULT_MAX_RESULT_WINDOW)
public class MicroarrayPlatformEs {
    public static final String MP_INDEX_NAME = "node_microarray_platform";

    @IndexId
    private String id;

    // es的索引忽略大小写,以便在term查询时不区分大小写,仅对keyword类型字段生效
    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = false)
    private String accession;

    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = false)
    private String title;

}
