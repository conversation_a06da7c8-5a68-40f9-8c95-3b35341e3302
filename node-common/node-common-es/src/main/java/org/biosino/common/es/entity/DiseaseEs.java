package org.biosino.common.es.entity;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.AnnotationConstants;
import org.dromara.easyes.annotation.rely.FieldType;

@Data
@IndexName(value = DiseaseEs.DISEASE_INDEX_NAME)
@Settings(maxResultWindow = 100 * AnnotationConstants.DEFAULT_MAX_RESULT_WINDOW)
public class DiseaseEs {
    public static final String DISEASE_INDEX_NAME = "node_disease";


    @IndexId
    private String id;

    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = false)
    private String lbl;
}
