package org.biosino.common.es.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.AnnotationConstants;
import org.dromara.easyes.annotation.rely.FieldStrategy;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * node关联数据es索引
 *
 * <AUTHOR>
 */
@Data
@IndexName(value = NodeRelatedEs.NODE_RELATED_ES_NAME)
@Settings(maxResultWindow = NodeRelatedEs.NODE_RELATED_MAX_COUNT)
public class NodeRelatedEs implements Serializable {
    public static final String NODE_RELATED_ES_NAME = "node_related_es";
    public static final int NODE_RELATED_MAX_COUNT = 800 * AnnotationConstants.DEFAULT_MAX_RESULT_WINDOW;

    /**
     * es主键
     */
    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String projNo;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String projName;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String projVisible;
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String projDesc;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String expNo;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String expVisible;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String expName;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String expType;
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String expDesc;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String sapNo;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String sapVisible;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String sapName;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String sapType;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String organism;
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String sapDesc;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String runNo;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String runName;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String analNo;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String analVisible;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String analName;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String analType;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String customAnalysisType;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String datNo;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String name;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String dataType;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String security;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String md5;
    @IndexField(fieldType = FieldType.LONG, strategy = FieldStrategy.IGNORED)
    private Long fileSize;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String readableFileSize;
    @IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
    private Date uploadTime;
    @IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
    private Date submissionDate;
    @IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
    private Date publicDate;

    /*@IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
    private Date expireDate;
    @IndexField(fieldType = FieldType.BOOLEAN, strategy = FieldStrategy.IGNORED)
    private Boolean accessible = false;
    @IndexField(fieldType = FieldType.BOOLEAN, strategy = FieldStrategy.IGNORED)
    private Boolean isShared = false;
    @IndexField(fieldType = FieldType.BOOLEAN, strategy = FieldStrategy.IGNORED)
    private Boolean isRequested = false;*/

    /**
     * 对应的qc分析是否完成
     */
    @IndexField(fieldType = FieldType.BOOLEAN, strategy = FieldStrategy.IGNORED)
    private Boolean fastqcFinished;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
    private String creator;

    @IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
    private Date updateDate;

    // 是否存在数据
    @IndexField(exist = false)
    private Boolean hasData = false;


    public void setId(String id) {
        this.id = StrUtil.trimToNull(id);
    }

    public void setProjNo(String projNo) {
        this.projNo = StrUtil.trimToNull(projNo);
    }

    public void setExpNo(String expNo) {
        this.expNo = StrUtil.trimToNull(expNo);
    }

    public void setSapNo(String sapNo) {
        this.sapNo = StrUtil.trimToNull(sapNo);
    }

    public void setRunNo(String runNo) {
        this.runNo = StrUtil.trimToNull(runNo);
    }

    public void setAnalNo(String analNo) {
        this.analNo = StrUtil.trimToNull(analNo);
    }

    public void setDatNo(String datNo) {
        this.datNo = StrUtil.trimToNull(datNo);
    }
}
