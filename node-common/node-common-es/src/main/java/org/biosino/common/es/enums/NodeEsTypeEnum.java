package org.biosino.common.es.enums;

import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * node索引类型
 *
 * <AUTHOR>
 */
@Getter
public enum NodeEsTypeEnum {
    project("PROJ"),
    experiment("EXPR"),
    sample("SAMP"),
    analysis("ANAL");

    private final String desc;

    NodeEsTypeEnum(String desc) {
        this.desc = desc;
    }

    public static Map<String, String> nameIgnoreCaseAndDesc() {
        final NodeEsTypeEnum[] values = NodeEsTypeEnum.values();
        final Map<String, String> map = new LinkedHashMap<>();
        for (NodeEsTypeEnum value : values) {
            map.put(value.name().toLowerCase(), value.getDesc());
        }
        return map;
    }

    public static Optional<NodeEsTypeEnum> findByName(String name) {
        if (name == null) {
            return Optional.empty();
        }
        final NodeEsTypeEnum[] values = NodeEsTypeEnum.values();
        for (NodeEsTypeEnum value : values) {
            if (value.name().equals(name)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

    /**
     * 获取type名称集合，browse统计数据将按照该名称顺序排序
     */
    public static List<String> findNameList() {
        NodeEsTypeEnum[] values = NodeEsTypeEnum.values();
        return Arrays.stream(values).map(NodeEsTypeEnum::name).collect(Collectors.toList());
    }

}
