package org.biosino.common.es.entity;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.AnnotationConstants;
import org.dromara.easyes.annotation.rely.FieldType;

@Data
@IndexName(value = TaxonomyEs.TAXONOMY_INDEX_NAME)
@Settings(maxResultWindow = 600 * AnnotationConstants.DEFAULT_MAX_RESULT_WINDOW)
public class TaxonomyEs {
    public static final String TAXONOMY_INDEX_NAME = "node_taxonomy";

    @IndexId
    private String id;

    // es的索引忽略大小写,以便在term查询时不区分大小写,仅对keyword类型字段生效
    @IndexField(fieldType = FieldType.KEYWORD)
    private String taxId;

    // 用KEYWORD是因为，这个用途是字典，需要实现like一样的模糊匹配而不是分词，wildcard模式不支持text
    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = false)
    private String scientificName;

    @IndexField(fieldType = FieldType.KEYWORD)
    private String lineage;

    /**
     * 雪花算法生成id,用于searchAfter排序
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long sid;

}
