package org.biosino.common.es.config;

import org.dromara.easyes.starter.register.EsMapperScan;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration;
import org.springframework.context.annotation.Configuration;

@Configuration
@AutoConfigureBefore(ElasticsearchRestClientAutoConfiguration.class)
@EsMapperScan("org.biosino.common.es.mapper")
public class EsConfig {

}
