package org.biosino.common.es.entity.base;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.FieldStrategy;
import org.dromara.easyes.annotation.rely.FieldType;

import java.io.Serializable;
import java.util.LinkedHashSet;

/**
 * node索引数据基本信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode
public class BaseEsInfo implements Serializable {

    // ============== 提交者信息 start ==============
    /**
     * 提交者名
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String subFirstName;

    /**
     * 提交者中间名
     */
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String subMiddleName;

    /**
     * 提交者姓
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String subLastName;

    /**
     * 提交者姓名(firstName + lastName，空格连接)
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String subName;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaSubName;

    /**
     * 提交者组织
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String subOrg;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaSubOrg;

    /**
     * 提交者国家
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String subCountry;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaSubCountry;
    // ============== 提交者信息 end ==============


    // ============== 出版信息 start ==============
    /**
     * publish表中是否存在doi
     */
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private String published;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> doi;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaDoi;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> pmid;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaPmid;

    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> articleName;
    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
    private LinkedHashSet<String> relaArticleName;
    // ============== 出版信息 end ==============

}
