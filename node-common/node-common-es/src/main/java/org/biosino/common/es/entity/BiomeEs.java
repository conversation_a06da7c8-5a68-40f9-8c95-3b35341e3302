package org.biosino.common.es.entity;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.AnnotationConstants;
import org.dromara.easyes.annotation.rely.FieldType;

/**
 * <AUTHOR>
 * @date 2024/10/11
 */

@Data
@IndexName(value = BiomeEs.BIOME_INDEX_NAME)
@Settings(maxResultWindow = 100 * AnnotationConstants.DEFAULT_MAX_RESULT_WINDOW)
public class BiomeEs {

    public static final String BIOME_INDEX_NAME = "node_biome";

    @IndexId
    private String id;

    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = false)
    private String type;

    @IndexField(fieldType = FieldType.KEYWORD, ignoreCase = false)
    private String value;

}
