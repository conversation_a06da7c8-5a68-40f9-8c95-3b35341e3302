package org.biosino.common.mongo.entity.sequence;


import org.biosino.common.core.constant.ConfigConstants;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface GenerateValue {

    SequenceType prefix();

    int digitsLength() default ConfigConstants.ID_LENGTH;

}

