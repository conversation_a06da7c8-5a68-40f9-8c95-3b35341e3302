package org.biosino.common.mongo.entity;

import lombok.Data;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.validator.ValidEnum;
import org.biosino.common.mongo.entity.other.OtherIds;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

import static org.biosino.common.mongo.authorize.IJudgeAuthorize.TEMP_DATA_FIELD;

@Data
@Document
public class Project {

    @Id
    private String id;

    @NotBlank(message = "proj_no不能为空")
    @Field("proj_no")
    @GenerateValue(prefix = SequenceType.PROJECT)
    private String projectNo;

    /**
     * Submission表NO
     */
    @Field("sub_no")
    private String subNo;

    @NotBlank(message = "name不能为空")
    private String name;
    private String description;

    @Field("related_links")
    private List<String> relatedLinks;

    @ValidEnum(enumClass = AuditEnum.class, allowNull = false, message = "audited不在合法范围内")
    private String audited;

    @NotBlank(message = "creator不能为空")
    private String creator;

    @NotNull(message = "submission_date不能为空")
    @Field("submission_date")
    private Date createDate;

    // Node2.0 废弃，因为updater就是creator
    // private String updater;

    @Field("update_date")
    private Date updateDate;

    @Field("public_date")
    private Date publicDate;

    @Field("hit_num")
    private Long hitNum;

    @Field("submitter")
    private Submitter submitter;

    @Field("other_ids")
    private List<OtherIds> otherIds;

    @Field("operator")
    private String operator;

    @Field("operation_date")
    private Date operationDate;

    @Field("export_num")
    private Long exportNum;

    /**
     * 项目暂存数据
     */
    @Field(TEMP_DATA_FIELD)
    private Project tempData;

    @Field("used_ids")
    private List<String> usedIds;

    private String ownership;

    @Field("source_project")
    private List<String> sourceProject;

    @ValidEnum(enumClass = VisibleStatusEnum.class, allowNull = false, message = "visible_status不在合法范围内")
    @Field("visible_status")
    private String visibleStatus;

    @Transient
    private List<Publish> publishInfos;
}
