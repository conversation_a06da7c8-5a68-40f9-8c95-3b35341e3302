package org.biosino.common.mongo.entity;


import lombok.Data;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.validator.ValidEnum;
import org.biosino.common.mongo.authorize.IJudgeAuthorize;
import org.biosino.common.mongo.authorize.Judge;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.mongo.entity.other.CustomTarget;
import org.biosino.common.mongo.entity.other.Pipeline;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;

@Data
@Document
public class Analysis implements IJudgeAuthorize {

    @Id
    private String id;

    @NotBlank(message = "anal_no不能为空")
    @GenerateValue(prefix = SequenceType.ANALYSIS)
    @Field("anal_no")
    private String analysisNo;

    /**
     * Submission表NO
     */
    @Field("sub_no")
    private String subNo;

    @Field("temp_data")
    private Analysis tempData;

    @NotBlank(message = "name不能为空")
    private String name;

    private String description;

    @Field("analysis_type")
    private String analysisType;

    @Field("custom_analysis_type")
    private String customAnalysisType;

    private List<Pipeline> pipeline;

    private List<AnalysisTarget> target;

    @Field("custom_target")
    private List<CustomTarget> customTarget;

    private Submitter submitter;

    @NotBlank(message = "creator不能为空")
    private String creator;

    @NotNull(message = "submission_date不能为空")
    @Field("submission_date")
    private Date createDate;

    // Node2.0 废弃，因为updater就是creator
    // private String updater;

    @Field("update_date")
    private Date updateDate;

    @Field("hit_num")
    private Long hitNum;

    @Field("export_num")
    private Long exportNum;

    @Field("public_date")
    private Date publicDate;

    private String operator;

    @Field("operation_date")
    private Date operationDate;

    @Field("used_ids")
    private List<String> usedIds;
    /**
     * 是否已经审核
     */
    @ValidEnum(enumClass = AuditEnum.class, allowNull = false, message = "audited不在合法范围内")
    private String audited;

    private String ownership;

    @Field("source_project")
    private List<String> sourceProject;

    // VisibleStatusEnum
    @ValidEnum(enumClass = VisibleStatusEnum.class, allowNull = false, message = "visible_status不在合法范围内")
    @Field("visible_status")
    private String visibleStatus;

    // 非数据库字段

    @Transient
    private Long dataNum;


    @Override
    public Judge getJudge() {
        Judge judge = new Judge();
        judge.setObjNo(getAnalysisNo());
        judge.setCreator(getCreator());
        judge.setPubDate(getPublicDate());
        judge.setVisibleStatus(getVisibleStatus());
        return judge;
    }

    @Override
    public String getObjType() {
        return "analysis";
    }

    @Transient
    private List<Publish> publishInfos;
    @Transient
    private LinkedHashSet<String> dataTypes;
    @Transient
    private LinkedHashSet<String> securities;

    @Transient
    private Integer numOfFile;
}
