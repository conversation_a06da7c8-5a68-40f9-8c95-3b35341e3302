package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 多组学样本统计
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_sap_multi_exp")
public class StatisticsSapMultiExp extends BaseStatistics {
    @Id
    private String id;

    // 样本类型（Human）
    private String type;

    // 存量 MULTI_OMICS_SAMPLE_BASE_001，查询node_related_es索引库中，expType类别超过1种、sapType为Human的sapNo的个数
    private Long multi = 0L;

    // 存量 MULTI_OMICS_SAMPLE_BASE_002，查询node_related_es索引库中，expType类别只有1种、sapType为Human的sapNo的个数
    private Long single = 0L;

    // 存量 MULTI_OMICS_SAMPLE_BASE_007，查询node_related_es索引库中，expType类别超过1种、sapType为Human、且sapVisible为Accessible的sapNo的个数
    private Long accessibleMulti = 0L;

    // 存量 MULTI_OMICS_SAMPLE_BASE_008，查询node_related_es索引库中，MULTI_OMICS_SAMPLE_BASE_001对应sapNo所属data文件大小总和，单位：GB
    private Long multiFileSize = 0L;

    // 存量 MULTI_OMICS_SAMPLE_BASE_009，查询node_related_es索引库中，MULTI_OMICS_SAMPLE_BASE_007对应sapNo所属data文件大小总和，单位：GB
    private Long accessibleMultiFileSize = 0L;

}
