package org.biosino.common.mongo.entity.other;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
public class Submitter {

    @Field("member_id")
    private String memberId;

    @Field("first_name")
    private String firstName;

    @Field("middle_name")
    private String middleName;

    @Field("last_name")
    private String lastName;

    @Field("org_name")
    private String orgName;

    @Field("dept_name")
    private String deptName;

    @Field("pi_name")
    private String piName;

    @Field("email")
    private String email;

    @Field("phone")
    private String phone;

    @Field("fax")
    private String fax;

    @Field("street")
    private String street;

    @Field("city")
    private String city;

    @Field("state_province")
    private String stateProvince;

    @Field("postal_code")
    private String postalCode;

    @Field("country_region")
    private String countryRegion;
}
