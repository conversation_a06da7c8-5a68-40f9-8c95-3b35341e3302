package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

@Data
@Document(collection = "security_update_log")
public class SecurityUpdateLog {

    @Id
    private String id;

    @Field("operator")
    private String operator;

    @Field("operate_time")
    private Date operateTime;

    @Field("ip")
    private String ip;

    @Field("resource_type")
    private String resourceType;

    @Field("resource_id")
    private String resouceId;

    @Field("source_security")
    private String sourceSecurity;

    @Field("source_valid_date")
    private Date sourceValidDate;

    @Field("target_security")
    private String targetSecurity;

    @Field("target_valid_date")
    private Date targetValidDate;

}
