package org.biosino.common.mongo.entity;

import lombok.Builder;
import lombok.Data;
import org.biosino.common.mongo.entity.other.GsaExportParam;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/15
 */
@Data
@Document("gsa_export_log")
@Builder
public class GsaExportLog {
    @Id
    private String id;

    private GsaExportParam params;

    private String creator;

    private String owner;

    @Field("data_nos")
    private List<String> dataNos;

    @Field("total_file_size")
    private Long totalFileSize;

    @Field("create_time")
    private Date createTime;
}
