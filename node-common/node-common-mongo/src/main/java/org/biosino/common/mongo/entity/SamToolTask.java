package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Data
@Document(collection = "samtool_task")
public class SamToolTask {
    @Id
    private String id;

    @Field("data_no")
    private String dataNo;

    /**
     * data的安全等级
     */
    @Transient
    private String dataSecurity;

    private String status;

    @Field("data_file_name")
    private String dataFileName;

    @Field("data_file_path")
    private String dataFilePath;

    @Field("data_file_size")
    private Long dataFileSize;

    @Field("exp_no")
    private String expNo;

    @Field("sap_no")
    private String sapNo;

    @Field("anal_no")
    private String analNo;

    @Field("sub_no")
    private String subNo;

    @Field("data_create_date")
    private Date dataCreateDate;

    @Field("create_date")
    private Date createDate;

    @Field("update_date")
    private Date updateDate;

    @Field
    private Integer priority;

    @Field("use_time")
    private String useTime;

    @Field("fail_cause")
    private String failCause;

    @Field("exit_code")
    private Integer exitCode;

    @Field("error_log_path")
    private String errorLogPath;
}
