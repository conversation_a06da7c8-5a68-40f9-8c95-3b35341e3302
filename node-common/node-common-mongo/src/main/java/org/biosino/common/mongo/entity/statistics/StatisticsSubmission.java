package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_submission")
public class StatisticsSubmission {

    @Id
    private String id;

    @Excel(name = "Month")
    private String month;

    // submission记录数 新增Submission数
    @Excel(name = "New Submission", cellType = Excel.ColumnType.NUMERIC)
    private Long submission = 0L;

    // submission审核通过的记录数
    @Excel(name = "Approved Submission", cellType = Excel.ColumnType.NUMERIC)
    private Long approvedSubmission = 0L;

    // 审核通过去重后的project数
    @Excel(name = "Approved Project", cellType = Excel.ColumnType.NUMERIC)
    private Long project = 0L;

    @Excel(name = "Approved Experiment", cellType = Excel.ColumnType.NUMERIC)
    private Long experiment = 0L;

    @Excel(name = "Approved Sample", cellType = Excel.ColumnType.NUMERIC)
    private Long sample = 0L;

    @Excel(name = "Approved Run", cellType = Excel.ColumnType.NUMERIC)
    private Long run = 0L;

    @Excel(name = "Approved Analysis", cellType = Excel.ColumnType.NUMERIC)
    private Long analysis = 0L;

    @Excel(name = "Approved Data", cellType = Excel.ColumnType.NUMERIC)
    private Long data = 0L;

    private Long submissionDataSize = 0L;

    @Transient
    @Excel(name = "Approved Data Size (TB)")
    private Double submissionDataSizeExport = 0.0;
}
