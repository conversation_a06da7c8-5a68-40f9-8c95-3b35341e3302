package org.biosino.common.mongo.entity.sequence;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@Document(collection = "sequence")
public class SequenceId {

    @Id
    private String id;

    @Field("seq_id")
    private long seqId;

    @Field("field_name")
    private String fieldName;
}
