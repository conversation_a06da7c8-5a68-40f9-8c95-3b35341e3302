package org.biosino.common.mongo.entity.other;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <AUTHOR>
 * @date 2024/7/11
 */
@Data
@ApiModel("同步文件项")
public class SyncFile {

    @ApiModelProperty("Data ID")
    @Field("data_no")
    private String dataNo;

    @ApiModelProperty("用户填写的Run No")
    @Field("run_no")
    private String runNo;

    @ApiModelProperty("用户填写的OtherTargetName")
    @Field("target_name")
    private String otherTargetName;

    @ApiModelProperty("用户填写的OtherTargetLink")
    @Field("target_link")
    private String otherTargetLink;

    @ApiModelProperty("文件在ViPMAP中的路径")
    @Field("file_path")
    private String filePath;

    @ApiModelProperty("当前文件同步状态")
    @Field("status")
    private String status;

    @ApiModelProperty("同步失败原因")
    @Field("fail_cause")
    private String failCause;
}
