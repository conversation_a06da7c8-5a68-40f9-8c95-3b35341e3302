package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 用户组织统计
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_user_org")
public class StatisticsUserOrg extends BaseStatistics {
    @Id
    private String id;

    // org： 组织 (国家)
    private String type;

    private Long prjTotal = 0L;
    private Long expTotal = 0L;
    private Long sapTotal = 0L;
    private Long analysisTotal = 0L;

    private Long unAccessiblePrjTotal = 0L;
    private Long unAccessibleAnalysisTotal = 0L;

    private Long dataTotal = 0L;

    private Long rawFileSize = 0L;
    private Long rawPrivateFileSize = 0L;

    private Long analysisFileSize = 0L;
    private Long analysisPrivateFileSize = 0L;

}
