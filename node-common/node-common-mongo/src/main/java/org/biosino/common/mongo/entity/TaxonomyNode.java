package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "taxonomy_node")
public class TaxonomyNode {
    @Id
    private String id;

    @Field("tax_id")
    @Indexed(name = "uni_tax_id", unique = true)
    private String taxId;

    @Field("names_scientific_name")
    private String scientificName;

    private List<String> alias;

    private String lineage;

}
