package org.biosino.common.mongo.entity;

import lombok.Data;
import org.biosino.common.mongo.entity.other.*;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

@Data
@Document
public class Review {
    @Id
    private String id;

    @Field("review_id")
    @GenerateValue(prefix = SequenceType.REVIEW)
    private String reviewId;

    @Field("review_to_name")
    private String reviewToName;

    @Field("review_to_email")
    private String reviewToEmail;

    @Field("projects")
    private List<ShareProject> projects;

    @Field("experiments")
    private List<ShareExperiment> experiments;

    @Field("samples")
    private List<ShareSample> samples;

    @Field("runs")
    private List<ShareRun> runs;

    @Field("analysis")
    private List<ShareAnalysis> analysis;

    @Field("datas")
    private List<ReviewData> datas;

    @Field("creator")
    private String creator;

    @Field("review_date")
    private Date reviewDate;

    @Field("expire_date")
    private Date expireDate;

    @Field("code")
    private String code;

    @Field("status")
    private String status;

    @Field("see")
    private String see;

    @Field("used_ids")
    private List<String> usedIds;
}
