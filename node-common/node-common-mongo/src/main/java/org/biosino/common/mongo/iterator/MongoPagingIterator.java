package org.biosino.common.mongo.iterator;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR> @date 2024/5/10
 */
public class MongoPagingIterator<T> implements Iterator<List<T>> {

    private final MongoTemplate mongoTemplate;
    private final Class<T> entityClass;
    private Query baseQuery;
    private final int pageSize;
    private int currentPage = 0;
    private boolean hasNext = true;

    public MongoPagingIterator(MongoTemplate mongoTemplate, Class<T> entityClass, Query baseQuery, int pageSize) {
        this.mongoTemplate = mongoTemplate;
        this.entityClass = entityClass;
        this.baseQuery = baseQuery != null ? baseQuery : null;
        this.pageSize = pageSize;
        // 判断集合中是否有数据
        this.hasNext = mongoTemplate.count(new Query(), entityClass) > 0;
    }

    public MongoPagingIterator(MongoTemplate mongoTemplate, Class<T> entityClass, int pageSize) {
        this(mongoTemplate, entityClass, null, pageSize);
    }

    @Override
    public boolean hasNext() {
        return hasNext;
    }

    @Override
    public List<T> next() {
        if (!hasNext()) {
            throw new RuntimeException("No more elements in the Mongo collection");
        }

        Query query;
        if (baseQuery == null) {
            query = new Query();
        } else {
            query = baseQuery;
        }
        // 跳过查询行
        query.skip((long) currentPage * pageSize);
        // 限制查询行
        query.limit(pageSize);
        List<T> pageData = mongoTemplate.find(query, entityClass);

        currentPage++;
        // 查询是否还有下一页
        hasNext = mongoTemplate.count(query.skip((long) currentPage * pageSize), entityClass) > 0;

        // 返回数据
        return pageData;
    }
}
