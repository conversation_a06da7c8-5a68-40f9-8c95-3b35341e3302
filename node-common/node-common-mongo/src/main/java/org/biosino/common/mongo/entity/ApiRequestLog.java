package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/19
 */
@Data
@Document("api_request_log")
public class ApiRequestLog {
    @Id
    private String id;
    /**
     * controller的方法
     */
    private String method;

    /**
     * 请求的参数
     */
    @Field("method_args")
    private String methodArgs;

    /**
     * 请求来源的ip
     */
    @Field("request_ip")
    private String requestIp;

    /**
     * 请求的方法
     */
    @Field("request_method")
    private String requestMethod;

    /**
     * 请求的url
     */
    @Field("request_url")
    private String requestUrl;

    /**
     * 请求的参数
     */
    @Field("request_param")
    private String requestParam;

    /**
     * 返回的结果
     */
    @Field("json_result")
    private String jsonResult;

    /**
     * 状态
     */
    @Field("status")
    private Integer status;

    /**
     * 错误信息
     */
    @Field("error_msg")
    private String errorMsg;

    /**
     * 请求时间
     */
    @Field("request_time")
    private Date requestTime;

    /**
     * 耗时
     */
    @Field("cost_time")
    private Long costTime;

}
