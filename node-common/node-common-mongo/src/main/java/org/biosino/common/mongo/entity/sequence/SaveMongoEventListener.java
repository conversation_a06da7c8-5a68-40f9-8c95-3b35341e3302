package org.biosino.common.mongo.entity.sequence;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.event.AbstractMongoEventListener;
import org.springframework.data.mongodb.core.mapping.event.BeforeConvertEvent;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.text.NumberFormat;

@Component
public class SaveMongoEventListener extends AbstractMongoEventListener<Object> {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void onBeforeConvert(BeforeConvertEvent event) {
        final Object source = event.getSource();
        ReflectionUtils.doWithFields(source.getClass(), field -> {
            ReflectionUtils.makeAccessible(field);
            if (field.isAnnotationPresent(GenerateValue.class) && field.get(source) == null) {
                GenerateValue annotation = field.getAnnotation(GenerateValue.class);
                //设置自增ID
                field.set(source, getNextId(annotation.prefix(), annotation.digitsLength()));
            }
        });
    }

    private Object getNextId(final SequenceType prefix, final int digitsLength) {
        Query query = new Query(Criteria.where("field_name").is(prefix.name().toLowerCase()));
        Update update = new Update();
        update.inc("seqId", 1);
        FindAndModifyOptions options = new FindAndModifyOptions();
        options.upsert(true);
        options.returnNew(true);
        SequenceId sequenceId = mongoTemplate.findAndModify(query, update, options, SequenceId.class);
        long seqId = 0;
        if (sequenceId != null) {
            seqId = sequenceId.getSeqId();
        }
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        nf.setMaximumIntegerDigits(digitsLength);
        nf.setMinimumIntegerDigits(digitsLength);
        return prefix.getPrefix() + nf.format(seqId);
    }

}
