package org.biosino.common.mongo.entity;

import lombok.Data;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.validator.ValidEnum;
import org.biosino.common.mongo.authorize.IJudgeAuthorize;
import org.biosino.common.mongo.authorize.Judge;
import org.biosino.common.mongo.entity.other.OtherIds;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
@Document
public class Experiment implements IJudgeAuthorize {
    @Id
    private String id;

    @NotBlank(message = "exp_no不能为空")
    @GenerateValue(prefix = SequenceType.EXPERIMENT)
    @Field("exp_no")
    private String expNo;

    /**
     * Submission表NO
     */
    @Field("sub_no")
    private String subNo;

    @NotBlank(message = "proj_no不能为空")
    @Field("proj_no")
    private String projectNo;

    @NotBlank(message = "name不能为空")
    private String name;
    private String description;
    private String protocol;

    @Field("related_links")
    private List<String> relatedLinks;

    @NotBlank(message = "creator不能为空")
    private String creator;

    private String audited;

    @NotNull(message = "submission_date不能为空")
    @Field("submission_date")
    private Date createDate;

    // Node2.0 废弃，因为updater就是creator
    // private String updater;

    @Field("update_date")
    private Date updateDate;

    @Field("public_date")
    private Date publicDate;

    @Field("hit_num")
    private Long hitNum;

    @Field("export_num")
    private Long exportNum;

    private Submitter submitter;

    @Field("other_ids")
    private List<OtherIds> otherIds;

    @Field("operator")
    private String operator;

    @Field("operation_date")
    private Date operationDate;

    /**
     * 实验暂存数据
     */
    @Field(TEMP_DATA_FIELD)
    private Experiment tempData;

    private Map<String, Object> attributes = new LinkedHashMap<>();

    @Field("used_ids")
    private List<String> usedIds;

    private String ownership;

    @Field("source_project")
    private List<String> sourceProject;

    // ExperimentTypeEnum
    @NotEmpty(message = "exp_type不能为空")
    @Field("exp_type")
    private String expType;

    // VisibleStatusEnum
    @ValidEnum(enumClass = VisibleStatusEnum.class, allowNull = false, message = "visible_status不能为空")
    @Field("visible_status")
    private String visibleStatus;

    @Override
    public Judge getJudge() {
        Judge judge = new Judge();
        judge.setObjNo(getExpNo());
        judge.setCreator(getCreator());
        judge.setPubDate(getPublicDate());
        judge.setVisibleStatus(getVisibleStatus());
        return judge;
    }

    @Override
    public String getObjType() {
        return "experiment";
    }

    @Transient
    private List<Publish> publishInfos;


    // 用于前端browse明细界面展示Other类型数据
    @Transient
    private Map<String, String> customAttrOther = new LinkedHashMap<>();

}
