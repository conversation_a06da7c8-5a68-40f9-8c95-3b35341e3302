package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10
 */
@Data
@Document("user_center_delete_log")
public class UserCenterDeleteLog {
    @Id
    private String id;

    private String creator;

    @Field("delete_time")
    private Date deleteTime;

    private String ip;

    private String type;

    private String no;

    @Field("proj_nos")
    private List<String> projNos;

    @Field("exp_nos")
    private List<String> expNos;

    @Field("run_nos")
    private List<String> runNos;

    @Field("sap_nos")
    private List<String> sapNos;

    @Field("data_nos")
    private List<String> dataNos;

    @Field("anal_nos")
    private List<String> analNos;
}
