package org.biosino.common.mongo.entity.other;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@Document
public class ShareData {

    @Field("data_no")
    private String datNo;
    @Field("run_no")
    private String runNo;
    @Field("anal_no")
    private String analNo;
    @Field("name")
    private String name;
    @Field("file_name")
    private String fileName;
    @Field("type")
    private String type;
    @Field("size")
    private String size;

}
