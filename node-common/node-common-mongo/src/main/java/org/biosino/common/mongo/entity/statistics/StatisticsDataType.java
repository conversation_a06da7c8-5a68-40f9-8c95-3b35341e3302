package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 数据类型统计
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_data_type")
public class StatisticsDataType extends BaseStatistics {
    @Id
    private String id;

    // 数据类型 (统计类型：fastq,fasta,bam,bed,bigwig,bw,bim,cel,csv,fq,maf,raw)
    private String type;

    private Long allTypeNum = 0L;

    private Long total = 0L;

    private Long totalPublic = 0L;

    private Long totalRestricted = 0L;

    private Long totalPrivate = 0L;

    private Long totalFileSize = 0L;

    private Long totalPublicFileSize = 0L;

    private Long totalRestrictedFileSize = 0L;

    private Long totalPrivateFileSize = 0L;

}
