package org.biosino.common.mongo.entity.admin;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.biosino.common.core.domain.dto.es.StatDTO;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 特殊数据集 Human Resource (Microbe Resource)
 *
 * <AUTHOR>
 */
@Data
@Document("special_human_resource")
public class HumanResource {

    @Id
    private String id;

    @NotBlank(message = "Category 1 cannot be empty")
    @Excel(name = "Category 1", cellType = Excel.ColumnType.STRING)
    private String category1;

    @Excel(name = "Category 2", cellType = Excel.ColumnType.STRING)
    private String category2;

    @Excel(name = "Category 3", cellType = Excel.ColumnType.STRING)
    private String category3;

    @Excel(name = "Comment", cellType = Excel.ColumnType.STRING)
    private String comment;

    @NotBlank(message = "Project ID cannot be empty")
    @Field("proj_no")
    @Excel(name = "Project ID", cellType = Excel.ColumnType.STRING)
    private String projectNo;

    private boolean microbeFlag = false;

    private String status;
    private String creator;
    private Date createTime;
    private Date updateTime;

    private StatDTO statInfo;

    @Transient
    private String category1Des;
}
