package org.biosino.common.mongo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <AUTHOR>
 * @date 2025/1/4
 */
@Data
@Document(collection = "dict_biome_curated")
public class BiomeCurated {

    @Id
    private String id;

    @Alias("biome_curated")
    @Field("biome_curated")
    private String biomeCurated;

    @Alias("data_type_curated")
    @Field("data_type_curated")
    private String dataTypeCurated;

    @Field("order_num")
    private Integer orderNum;
}
