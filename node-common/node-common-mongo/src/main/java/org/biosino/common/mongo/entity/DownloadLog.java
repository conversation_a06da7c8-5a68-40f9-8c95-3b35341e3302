package org.biosino.common.mongo.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "download_log")
public class DownloadLog {
    @Id
    private String id;

    @Field
    private String type;

    @Field("type_no")
    private String typeNo;

    /**
     * 下载数据的人ID
     */
    @Field("member_id")
    private String memberId;

    /**
     * 数据所属人ID
     */
    @Field("owner_id")
    private String ownerId;

    @Field("download_type")
    private String downloadType;

    @Field
    private String ip;

    @Field
    private String country;

    @Field("create_time")
    private Date createTime;
}
