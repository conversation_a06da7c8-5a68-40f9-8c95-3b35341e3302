package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_data_flow")
public class StatisticsDataFlow {

    @Id
    private String id;

    private String month;

    private Long download = 0L;
    private Long downloadDataSize = 0L;

    private Long uploadData = 0L;
    private Long uploadDataSize = 0L;

    private Long submission = 0L;
    private Long submissionDataSize = 0L;

}
