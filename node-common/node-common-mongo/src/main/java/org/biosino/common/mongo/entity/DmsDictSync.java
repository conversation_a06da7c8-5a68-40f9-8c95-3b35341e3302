package org.biosino.common.mongo.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "dms_dict_sync")
public class DmsDictSync {
    @Id
    private String id;

    @Field("dict_name")
    private String dictName;

    @Field("description")
    private String description;

    @Field
    private String abbreviation;

    @Field("last_record_count")
    private Long lastRecordCount;

    @Field("record_count")
    private Long recordCount;

    @Field("update_time")
    private Date updateTime;

    @Field
    private String status;
}
