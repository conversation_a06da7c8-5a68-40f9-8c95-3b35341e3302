package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_data_volume")
public class StatisticsDataVolume {

    @Id
    private String id;

    private String month;

    // 公开项目数量
    private Long projAccessible = 0L;
    // 未公开项目数量
    private Long projUnAccessible = 0L;

    private Long expAccessible = 0L;
    private Long expUnAccessible = 0L;

    private Long sapAccessible = 0L;
    private Long sapUnAccessible = 0L;

    private Long runAccessible = 0L;
    private Long runUnAccessible = 0L;

    private Long analAccessible = 0L;
    private Long analUnAccessible = 0L;

    private Long rawDataPrivate = 0L;
    private Long rawDataPrivateSize = 0L;
    private Long rawDataRestricted = 0L;
    private Long rawDataRestrictedSize = 0L;
    private Long rawDataPublic = 0L;
    private Long rawDataPublicSize = 0L;

    private Long analDataPrivate = 0L;
    private Long analDataPrivateSize = 0L;
    private Long analDataRestricted = 0L;
    private Long analDataRestrictedSize = 0L;
    private Long analDataPublic = 0L;
    private Long analDataPublicSize = 0L;
}
