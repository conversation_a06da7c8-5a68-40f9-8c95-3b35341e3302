package org.biosino.common.mongo.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.biosino.common.mongo.entity.other.SyncFile;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/11
 */
@Data
@Document("vipmap_sync_task")
@ApiModel("NODE To VipMAP数据同步任务")
public class VipMapSyncTask {

    @Id
    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("ViPMAP中分析的任务编号")
    @Field("task_id")
    private String taskId;

    @ApiModelProperty("NODE中生成的Analysis编号")
    @Field("anal_no")
    private String analysisNo;

    @ApiModelProperty("分析类型")
    @Field("analysis_type")
    private String analysisType;

    @ApiModelProperty("ViPMAP中分析结果的输出目录")
    @Field("output_dir")
    private String outputDir;

    @ApiModelProperty("同步文件列表")
    @Field("sync_files")
    private List<SyncFile> syncFiles;

    @ApiModelProperty("任务创建人")
    private String creator;

    @ApiModelProperty("任务创建时间")
    @Field("create_date")
    private Date createDate;

}
