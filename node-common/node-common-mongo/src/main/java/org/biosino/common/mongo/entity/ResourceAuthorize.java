package org.biosino.common.mongo.entity;


import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * resource_authorize 表
 */
@Data
@Document(value = "resource_authorize")
public class ResourceAuthorize {

    @Id
    private String id;

    @Field(value = "batch_no")
    private String batchNo;

    @Field(value = "owner")
    private String owner;

    @Field(value = "type")
    private String type;

    @Field(value = "type_id")
    private String typeId;

    @Field(value = "data")
    private List<String> data;

    @Field(value = "description")
    private String description;

    @Field(value = "reply_text")
    private String replyText;

    @Field(value = "authorize_to")
    private String authorizeTo;

    @Field(value = "see")
    private Boolean see;

    @Field(value = "status")
    private String status;

    @Field(value = "expire_date")
    private Date expireDate;

    @Field(value = "apply_date")
    private Date applyDate;

    @Field(value = "reply_date")
    private Date replyDate;


}
