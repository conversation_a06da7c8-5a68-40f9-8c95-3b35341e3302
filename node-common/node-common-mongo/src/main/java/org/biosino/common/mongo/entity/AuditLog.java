package org.biosino.common.mongo.entity;

import lombok.Data;
import org.biosino.common.mongo.entity.other.RejectReason;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

/**
 * 审核员审核日志
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "audit_log")
public class AuditLog {
    @Id
    private String id;

    @Field("sub_no")
    private String subNo;

    /**
     * @see org.biosino.common.core.enums.SubmissionDataTypeEnum
     */
    @Field("data_type")
    private String dataType;

    /**
     * 审核通过（Pass） 或者 驳回（Reject）
     */
    private String status;

    // 审核员ID
    @Field("auditor_id")
    private Long auditorId;
    private String auditor;

    @Field("reject_reason")
    private List<RejectReason> rejectReason;

    // 数据提交人
    private String creator;

    @Field("create_time")
    private Date createTime;

    @Field("proj_num")
    private Integer projNum;

    @Field("exp_num")
    private Integer expNum;

    @Field("sap_num")
    private Integer sapNum;

    @Field("anal_num")
    private Integer analNum;

    @Field("run_num")
    private Integer runNum;

    @Field("data_num")
    private Integer dataNum;

    @Field("data_size")
    private Long dataSize;

    @Field("publish_num")
    private Integer publishNum = 0;

    private Integer total = 0;

}
