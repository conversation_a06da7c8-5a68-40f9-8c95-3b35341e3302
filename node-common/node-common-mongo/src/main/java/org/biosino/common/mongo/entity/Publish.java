package org.biosino.common.mongo.entity;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "publish")
public class Publish {
    @Id
    @Excel(name = "ID")
    private String id;

    /**
     * Submission表NO
     */
    @Field("sub_no")
    @Excel(name = "Submission NO")
    private String subNo;

    @NotBlank(message = "type不能为空")
    @Excel(name = "Type")
    private String type;

    @NotBlank(message = "type_id不能为空")
    @Field("type_id")
    @Excel(name = "Type ID")
    private String typeId;

    @Excel(name = "Audited Status")
    private String audited;

    @Field("temp_data")
    private Publish tempData;

    /**
     * 期刊名
     */
    @NotBlank(message = "publication不能为空")
    @Excel(name = "Publication")
    private String publication;

    /**
     * 文献名
     */
    @NotBlank(message = "article_name不能为空")
    @Field("article_name")
    @Excel(name = "article name")
    private String articleName;

    @Field("PMID")
    @Excel(name = "PMID")
    private String pmid;

    @NotBlank(message = "doi不能为空")
    @Field("DOI")
    @Excel(name = "DOI")
    private String doi;

    @Excel(name = "Reference")
    private String reference;

    // 一期的字段，2.0废弃
    private String priority;

    // 字段的排序，desc显示
    private Integer sort = 1;

    @Excel(name = "Creator ID")
    private String creator;

    @Excel(name = "Status")
    private String status;

    @Field("create_date")
    @Excel(name = "Create Date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    private String updater;

    @Field("update_date")
    @Excel(name = "Update Date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    /**
     * 数据是否已删除
     */
    @Excel(name = "Deleted Status")
    private Boolean deleted = false;

}
