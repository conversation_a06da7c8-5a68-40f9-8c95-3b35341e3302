package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2024/8/2
 */
@Data
@Document(collection = "statistics_node_member")
public class StatisticsNodeMember {

    @Id
    private String id;

    @Excel(name = "Month")
    private String month;

    /**
     * 按月统计新增用户记录数
     * (增量只统计当月的)
     * member表中create time在月度内
     */
    @Excel(name = "Create Member", cellType = Excel.ColumnType.NUMERIC)
    private Long createMember = 0L;

    /**
     * 按月统计用户总量
     * (存量)
     * 截止到月份，member表的总记录数
     */
    @Excel(name = "Total Member", cellType = Excel.ColumnType.NUMERIC)
    private Long totalMember = 0L;

    /**
     * 按月统计有效用户量
     * (存量)
     * 截止到月份，member表中状态为enable的总记录数
     */
    @Excel(name = "Total Enable Member", cellType = Excel.ColumnType.NUMERIC)
    private Long totalEnableMember = 0L;

    /**
     * 按月统计用户登录量
     * (增量)
     * 以月度内登录人次计。一个人登录多次记录多次
     */
    @Excel(name = "Login Count", cellType = Excel.ColumnType.NUMERIC)
    private Long loginCount = 0L;

    /**
     * 按月度统计用户登录账号数
     * (增量)
     * 以月度内登录过一次计1，登录多次不重复计
     */
    @Excel(name = "Login Member", cellType = Excel.ColumnType.NUMERIC)
    private Long loginMember = 0L;

    /**
     * 按月度统计提交数据的用户量
     * (增量)
     * DATA_SUBMISSION_001中不同的用户计1
     */
    @Excel(name = "Submission Member", cellType = Excel.ColumnType.NUMERIC)
    private Long submissionMember = 0L;
}
