package org.biosino.common.mongo.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.common.mongo.dto.TreeItemDTO;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "exp_sample_type_group")
@NoArgsConstructor
public class ExpSampleTypeGroup {
    @Id
    private String id;
    private Integer sort;
    // experiment or sample
    @Indexed
    private String type;
    private String description;

//    private Integer lastId;

    private TreeItemDTO groupInfo;
}
