package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_visitor")
public class StatisticsVisitor {

    @Id
    private String id;

    @Field("owner_id")
    private String ownerId;

    @Field
    private String month;

    @Field("project_count")
    private long projectCount;

    @Field("project_country")
    private Map<String, Long> projectCountry;

    @Field("experiment_count")
    private long experimentCount;

    @Field("experiment_country")
    private Map<String, Long> experimentCountry;

    @Field("sample_count")
    private long sampleCount;

    @Field("sample_country")
    private Map<String, Long> sampleCountry;

    @Field("analysis_count")
    private long analysisCount;

    @Field("analysis_country")
    private Map<String, Long> analysisCountry;

    public StatisticsVisitor(String ownerId, String month) {
        this.ownerId = ownerId;
        this.month = month;
    }
}
