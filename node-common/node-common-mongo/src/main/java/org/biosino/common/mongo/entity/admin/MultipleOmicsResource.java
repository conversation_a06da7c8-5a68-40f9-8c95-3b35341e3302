package org.biosino.common.mongo.entity.admin;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.LinkedHashSet;

/**
 * 特殊数据集 Multiple Omics Resource
 *
 * <AUTHOR>
 */
@Data
@Document("special_mult_omics_resource")
public class MultipleOmicsResource {

    @Id
    private String id;

    @NotBlank(message = "Project ID cannot be empty")
    @Field("proj_no")
    @Excel(name = "Project ID", cellType = Excel.ColumnType.STRING)
    @Indexed(unique = true)
    private String projID;

    private String projName;
    private String submitter;
    private String des;

    private LinkedHashSet<String> expTypes;

    private String status;

    private String creator;
    private Date createTime;
    private Date modifiedDate;

//    private StatDTO statInfo;

}
