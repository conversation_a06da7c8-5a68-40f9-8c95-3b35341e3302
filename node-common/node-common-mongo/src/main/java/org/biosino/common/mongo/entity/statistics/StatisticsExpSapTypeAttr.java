package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 标签数据统计
 * 以一个标签{source oragnization，source project，source leader}为例
 *
 * <AUTHOR>
 * @date 2024/7/25
 */
@Data
@Document("statistics_exp_sap_type_attr")
public class StatisticsExpSapTypeAttr {

    @Id
    private String id;

    private String month;

    /**
     * metadata类型
     */
    @Field("metadata_type")
    private String metadataType;

    /**
     * 模板类型
     */
    @Field("subject_type")
    private String subjectType;

    /**
     * 标签
     */
    private String tag;

    /**
     * 属性字段
     */
    @Field("attr_field")
    private String attrField;

    /**
     * 可访问的数量
     */
    @Field("accessible")
    private Long accessible = 0L;

    /**
     * 总可访问的数量
     */
    @Field("total_accessible")
    private Long totalAccessible = 0L;

    /**
     * 不可访问的数量
     */
    @Field("unaccessible")
    private Long unaccessible = 0L;

    /**
     * 总不可访问的数量
     */
    @Field("total_unaccessible")
    private Long totalUnaccessible = 0L;
}
