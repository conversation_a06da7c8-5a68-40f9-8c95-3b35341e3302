package org.biosino.common.mongo.entity.other;

import lombok.Data;

import java.util.List;

@Data
public class Pipeline implements Comparable<Pipeline> {

    private int index;
    private String program;
    private String link;
    private String version;
    private String note;
    private List<String> output;

    @Override
    public int compareTo(Pipeline o) {
        return o.getIndex() >= this.getIndex() ? -1 : 1;
    }
}
