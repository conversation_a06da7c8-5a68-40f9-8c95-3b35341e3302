package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 临时数据统计信息
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_temp_data")
public class StatisticsTempData extends BaseStatistics {
    @Id
    private String id;

    // 存量 DATA_UNARCHIVED_001，data表中archived为“no”的记录数
    private Long unarchivedFile = 0L;

    // 存量 DATA_UNARCHIVED_002，DATA_UNARCHIVED_001 记录的file size之和
    private Long unarchivedFileSize = 0L;

    // 存量 DATA_UNARCHIVED_003，data表中archived为“no” 且 submission_date超过6个月 的记录数
    private Long unarchived6MonthFile = 0L;

    // 存量 DATA_UNARCHIVED_004，DATA_UNARCHIVED_003 记录的file size之和
    private Long unarchived6MonthFileSize = 0L;

    // 存量 DATA_UNARCHIVED_005，data表中archived为“no” 且 submission_date超过12个月 的记录数
    private Long unarchived1YearFile = 0L;

    // 存量 DATA_UNARCHIVED_006，DATA_UNARCHIVED_005 记录的file size之和
    private Long unarchived1YearFileSize = 0L;

    // 存量 DATA_PRIVATE_001，data表中security为“Private”的记录数
    private Long unAccessibleFile = 0L;

    // 存量 DATA_PRIVATE_002，DATA_PRIVATE_001 记录的file size之和
    private Long unAccessibleFileSize = 0L;

    // 存量 DATA_PRIVATE_003，data表中security为“Private” 且 submission_date超过6个月 的记录数
    private Long unAccessible6MonthFile = 0L;

    // 存量 DATA_PRIVATE_004，DATA_PRIVATE_003 记录的file size之和
    private Long unAccessible6MonthFileSize = 0L;

    // 存量 DATA_PRIVATE_005，data表中security为“Private” 且 submission_date超过12个月 的记录数
    private Long unAccessible1YearFile = 0L;

    // 存量 DATA_PRIVATE_006，DATA_PRIVATE_005 记录的file size之和
    private Long unAccessible1YearFileSize = 0L;

}
