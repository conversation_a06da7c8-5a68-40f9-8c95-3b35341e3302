package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 文献统计数据
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_publish")
public class StatisticsPublish extends BaseStatistics {
    @Id
    private String id;

    // 存量 DATA_PUBLISH_001，历史所有approvedMonth求和
    @Excel(name = "Approved Total Publish", cellType = Excel.ColumnType.NUMERIC)
    private Long approvedTotal = 0L;

    // 存量 DATA_PUBLISH_002，publish表的记录数
    @Excel(name = "Total Publish", cellType = Excel.ColumnType.NUMERIC)
    private Long total = 0L;

    // 增量 DATA_PUBLISH_003，找出publish表中audited是audited且create_time是当前month内的记录数
    @Excel(name = "Month Approved Publish", cellType = Excel.ColumnType.NUMERIC)
    private Long approvedMonth = 0L;

}
