package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 临时数据统计ftp log信息
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_temp_ftp_data")
public class StatisticsTempFtpData extends BaseStatistics {
    @Id
    private String id;

    // 存量 DATA_FTP_HOME_001，MySQL中ftp_file_log表中status为“待校验” 且 文件名结尾不是.md5的记录数
    private Long ftpHomeFile = 0L;

    // 存量 DATA_FTP_HOME_002，DATA_FTP_HOME_001记录的file size之和
    private Long ftpHomeFileSize = 0L;

    // 存量 DATA_FTP_HOME_003，MySQL中ftp_file_log表中status为“待校验” 且 文件名结尾不是.md5 且 拥有相关的md5文件的记录数
    private Long ftpHomeHasMd5File = 0L;

    // 存量 DATA_FTP_HOME_004，DATA_FTP_HOME_003记录的file size之和
    private Long ftpHomeHasMd5FileSize = 0L;

    // 存量 DATA_FTP_HOME_005，MySQL中ftp_file_log表中status为“待校验” 且 文件名结尾不是.md5 且 create_time超过6个月 的记录数
    private Long ftpHomeExpired6MonthFile = 0L;

    // 存量 DATA_FTP_HOME_006，DATA_FTP_HOME_005记录的file size之和
    private Long ftpHomeExpired6MonthFileSize = 0L;

    // 存量 DATA_FTP_HOME_007，MySQL中ftp_file_log表中status为“待校验” 且 文件名结尾不是.md5 且 create_time超过12个月 的记录数
    private Long ftpHomeExpired1YearFile = 0L;

    // 存量 DATA_FTP_HOME_008，DATA_FTP_HOME_007记录的file size之和
    private Long ftpHomeExpired1YearFileSize = 0L;

}
