package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 数据提交方式
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_submission_method")
public class StatisticsSubMethod {

    @Id
    private String id;

    @Field
    @Excel(name = "Month")
    private String month;

    @Excel(name = "HTTP", cellType = Excel.ColumnType.NUMERIC)
    private Long http = 0L;
    @Field("http_size")
    private Long httpSize = 0L;

    @Excel(name = "HTTP Data Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double httpDataSize = 0D;

    @Excel(name = "FTP", cellType = Excel.ColumnType.NUMERIC)
    private Long ftp = 0L;
    @Field("ftp_size")
    private Long ftpSize = 0L;

    @Excel(name = "FTP Data Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double ftpDataSize = 0D;

    @Excel(name = "Project", cellType = Excel.ColumnType.NUMERIC)
    private Long project = 0L;

    @Field("exp_single")
    @Excel(name = "Experiment Single", cellType = Excel.ColumnType.NUMERIC)
    private Long expSingle = 0L;

    @Field("exp_multiple")
    @Excel(name = "Experiment Multiple", cellType = Excel.ColumnType.NUMERIC)
    private Long expMultiple = 0L;

    @Field("sap_single")
    @Excel(name = "Sample Single", cellType = Excel.ColumnType.NUMERIC)
    private Long sapSingle = 0L;

    @Field("sap_multiple")
    @Excel(name = "Sample Multiple", cellType = Excel.ColumnType.NUMERIC)
    private Long sapMultiple = 0L;

    @Field("anal_single")
    @Excel(name = "Analysis Single", cellType = Excel.ColumnType.NUMERIC)
    private Long analSingle = 0L;

    @Field("anal_multiple")
    @Excel(name = "Analysis Multiple", cellType = Excel.ColumnType.NUMERIC)
    private Long analMultiple = 0L;

    // single页面归档分析
    @Field("anal_data_archiving_single")
    @Excel(name = "Analysis Data Archiving Single", cellType = Excel.ColumnType.NUMERIC)
    private Long analDataArchivingSingle = 0L;

    // 批量归档分析
    @Field("anal_data_archiving_multiple")
    @Excel(name = "Analysis Data Archiving Multiple", cellType = Excel.ColumnType.NUMERIC)
    private Long analDataArchivingMultiple = 0L;

    // single页面归档raw data
    @Field("raw_data_archiving_single")
    @Excel(name = "Raw Data Archiving Single", cellType = Excel.ColumnType.NUMERIC)
    private Long rawDataArchivingSingle = 0L;

    // 批量归档raw data
    @Field("raw_data_archiving_multiple")
    @Excel(name = "Raw Data Archiving Multiple", cellType = Excel.ColumnType.NUMERIC)
    private Long rawDataArchivingMultiple = 0L;
}
