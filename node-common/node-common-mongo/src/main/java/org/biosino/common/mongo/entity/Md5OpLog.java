package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

@Data
@Document(collection = "md5_op_log")
public class Md5OpLog {
    @Id
    private String id;

    @Field("file_path")
    private String filePath;

    @Field("create_date")
    private Date createDate;

    @Field("reply_date")
    private Date replyDate;

    @Field("status")
    private String status;/*sucess/fail*/

    @Field("fail_cause")
    private String failCause;

    @Field("md5_value")
    private String md5Value;

}
