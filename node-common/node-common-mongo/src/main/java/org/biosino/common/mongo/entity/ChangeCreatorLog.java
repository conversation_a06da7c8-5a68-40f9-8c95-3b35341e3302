package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;
import java.util.List;

@Data
@Document(collection = "change_creator_log")
public class ChangeCreatorLog {
    @Id
    private String id;
    @Field("operator")
    private String operator;
    @Field("operate_time")
    private Date operateTime;
    @Field("ip")
    private String ip;
    @Field("type")
    private String type;
    @Field("type_id")
    private String typeId;
    @Field("source_creator")
    private String sourceCreator;
    @Field("source_email")
    private String sourceEmail;
    @Field("target_creator")
    private String targetCreator;
    @Field("target_email")
    private String targetEmail;

    @Field("proj_nos")
    private List<String> projNos;

    @Field("exp_nos")
    private List<String> expNos;

    @Field("run_nos")
    private List<String> runNos;

    @Field("sap_nos")
    private List<String> sapNos;

    @Field("data_nos")
    private List<String> dataNos;

    @Field("anal_nos")
    private List<String> analNos;
}