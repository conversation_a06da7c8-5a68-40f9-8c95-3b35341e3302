package org.biosino.common.mongo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.mongo.entity.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @date 2024/1/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TypeInformation {
    private Class clazz;
    private String field;
    private String mongoField;
    private String shareListField;
    private String shareItemField;
    private String authorizeType;

    public static final Map<String, TypeInformation> typeInfoMap = new HashMap<String, TypeInformation>() {
        {
            put(AuthorizeType.project.name(), new TypeInformation(Project.class, "projectNo", "proj_no", "projects", "projectNo", AuthorizeType.project.name()));
            put(AuthorizeType.experiment.name(), new TypeInformation(Experiment.class, "expNo", "exp_no", "experiments", "expNo", AuthorizeType.experiment.name()));
            put(AuthorizeType.sample.name(), new TypeInformation(Sample.class, "sapNo", "sap_no", "samples", "sapNo", AuthorizeType.sample.name()));
            put(AuthorizeType.run.name(), new TypeInformation(Run.class, "runNo", "run_no", "runs", "runNo", AuthorizeType.run.name()));
            put(AuthorizeType.data.name(), new TypeInformation(org.biosino.common.mongo.entity.Data.class, "datNo", "dat_no", "datas", "datNo", AuthorizeType.data.name()));
            put(AuthorizeType.analysis.name(), new TypeInformation(Analysis.class, "analysisNo", "anal_no", "analysis", "analNo", AuthorizeType.analysis.name()));
        }
    };

    public String getShareMongoQueryField() {
        return this.shareListField + "." + this.shareItemField;
    }
}
