package org.biosino.common.mongo.entity.email;

import lombok.Data;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 邮件模版
 *
 * <AUTHOR>
 */
@Data
@Document("email_template")
public class EmailTemplate {
    @Id
    private String id;

    @Field("email_no")
    @GenerateValue(prefix = SequenceType.EMAIL)
    private String emailNo;

    @Indexed(unique = true)
    @NotBlank(message = "The subject cannot be empty")
    private String subject;

    @NotBlank(message = "Template file cannot be empty")
    private String templateName;

    @NotBlank(message = "Template file cannot be empty")
    private String randomFileName;

    private String remark;

    @Transient
    private String filePreview;

    private String creator;
    private Date createTime;
    private Date updateTime;

    private boolean sendingAll = false;

}
