package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "submission_log")
public class SubDataLog {
    @Id
    private String id;

    private String subNo;

    private String creator;

    private String status;

    private String data;

    @Field("create_time")
    private Date createTime;

}
