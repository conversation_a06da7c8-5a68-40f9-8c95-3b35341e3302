package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "visitor_log")
public class VisitorLog {

    @Id
    private String id;

    @Field
    private String type;

    @Field("type_id")
    private String typeId;

    @Field("create_time")
    private Date createTime;

    @Field
    private String ip;

    @Field
    private String country;

    /**
     * 访问数据的人ID
     */
    @Field("member_id")
    private String memberId;

    /**
     * 数据所属人ID
     */
    @Field("owner_id")
    private String ownerId;
}
