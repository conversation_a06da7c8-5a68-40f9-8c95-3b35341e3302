package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 样本基础统计
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_sample")
public class StatisticsSample extends BaseStatistics {
    @Id
    private String id;

    // 样本类型 (Human)
    private String type;

    // 存量 SAMPLE_TYPE_HUMAN_001，sample表中visible_status为unAccessible或Accessible，且subject_type为Human的sap_no总数
    private Long total = 0L;

    // 存量 SAMPLE_TYPE_HUMAN_002，sample表中visible_status为Accessible，且subject_type为Human的sap_no总数
    private Long accessibleNum = 0L;

    // 存量 SAMPLE_TYPE_HUMAN_003，SAMPLE_TYPE_HUMAN_001- SAMPLE_TYPE_HUMAN_002
    private Long unAccessibleNum = 0L;

    // 增量 SAMPLE_TYPE_HUMAN_004，SAMPLE_TYPE_HUMAN_001（当前月份）-SAMPLE_TYPE_HUMAN_001（上个月）
    private Long totalGrowth = 0L;

    // 增量 SAMPLE_TYPE_HUMAN_005，SAMPLE_TYPE_HUMAN_002（当前月份）-SAMPLE_TYPE_HUMAN_002（上个月）
    private Long accessibleGrowth = 0L;

    // 增量 SAMPLE_TYPE_HUMAN_006，SAMPLE_TYPE_HUMAN_003（当前月份）-SAMPLE_TYPE_HUMAN_003（上个月）
    private Long unAccessibleGrowth = 0L;

    // 存量 SAMPLE_TYPE_HUMAN_007，查询node_related_es索引库中，sapNo在SAMPLE_TYPE_HUMAN_001所属sapNo集合内的fileSize总和，单位TB
    private Long totalFileSize = 0L;

    // 存量 SAMPLE_TYPE_HUMAN_008，查询node_related_es索引库中，sapNo在SAMPLE_TYPE_HUMAN_002所属sapNo集合内的fileSize总和，单位TB
    private Long accessibleFileSize = 0L;

    // 存量 SAMPLE_TYPE_HUMAN_009，SAMPLE_TYPE_HUMAN_007-SAMPLE_TYPE_HUMAN_008，单位TB
    private Long unAccessibleFileSize = 0L;

    // 增量 SAMPLE_TYPE_HUMAN_010，SAMPLE_TYPE_HUMAN_007（当前月份）-SAMPLE_TYPE_HUMAN_007（上个月
    private Long totalFileSizeGrowth = 0L;

    // 增量 SAMPLE_TYPE_HUMAN_011，SAMPLE_TYPE_HUMAN_008（当前月份）-SAMPLE_TYPE_HUMAN_008（上个月）
    private Long accessibleFileSizeGrowth = 0L;

    // 增量 SAMPLE_TYPE_HUMAN_012，SAMPLE_TYPE_HUMAN_009（当前月份）-SAMPLE_TYPE_HUMAN_009（上个月）
    private Long unAccessibleFileSizeGrowth = 0L;


}
