package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 数据共享统计信息
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_share")
public class StatisticsShare extends BaseStatistics {
    @Id
    private String id;

    // 增量DATA_SHARE_001(004)，share创建时间在月度内的记录数
    private Long share = 0L;
    // 增量DATA_SHARE_002(005)，DATA_SHARE_001内涉及的file_size综合
    private Long shareFileSize = 0L;
    // 增量DATA_SHARE_003(006)，DATA_SHARE_001内涉及的data的文件数量
    private Long shareFileNum = 0L;

    // 增量DATA_REVIEW_001(004), Review创建时间在月度内的记录数
    private Long review = 0L;
    // 增量DATA_REVIEW_002(005), DATA_REVIEW_001内涉及的file_size综合
    private Long reviewFileSize = 0L;
    // 增量DATA_REVIEW_003(006), DATA_REVIEW_001内涉及的data的文件数量
    private Long reviewFileNum = 0L;

    // 增量DATA_REQUEST_001(004), Request创建时间在月度内的记录数
    private Long request = 0L;
    // 增量DATA_REQUEST_002(005), DATA_REQUEST_001内涉及的file_size综合
    private Long requestFileSize = 0L;
    // 增量DATA_REQUEST_003(006), DATA_REQUEST_001内涉及的data的文件数量
    private Long requestFileNum = 0L;

    // 增量DATA_REQUEST_007, resource_authorize表中apply_date在月度内，且status为Authorized的记录数
    private Long requestAuthorized = 0L;
    // 增量DATA_REQUEST_008, DATA_REQUEST_007内涉及的data字段关联的size之和
    private Long requestAuthorizedFileSize = 0L;
    // 增量DATA_REQUEST_009, DATA_REQUEST_007内涉及的data数组长度
    private Long requestAuthorizedFileNum = 0L;

    // 【增量】DATA_GSA_001, 统计当前月内，通过project detail页面的导出gsa模版功能按钮，成功导出模版文件的次数。比如一个project导出human一次，又导出cell_line一次，那么记录为2次。
    private Long gsa = 0L;
    // 【增量】DATA_GSA_002, DATA_GSA_001中导出成功的模版中涉及的data 的file_size之和。单位：GB
    private Long gsaFileSize = 0L;

}
