package org.biosino.common.mongo.entity;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.biosino.common.core.enums.dict.BaseAttrType;
import org.biosino.common.core.enums.dict.ExpSampleDataType;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.core.validator.ValidEnum;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "exp_sample_type")
public class ExpSampleType {
    public final static String PLATFORM = "platform";
    public final static String READ_LENGTH_FOR_MATE2 = "read_length_for_mate2(bp)";
    public final static String VALUE_ARRAY = "value_array";
    // handsontable列字段名 （sample）
    public final static String SAMPLE_ID = "sample_id";
    public final static String SAMPLE_NAME = "sample_name";
    public final static String ORGANISM = "organism";
    public final static String TISSUE = "tissue";
    // handsontable列字段名 （experiment）
    public final static String EXPERIMENT_ID = "experiment_id";
    public final static String EXPERIMENT_NAME = "experiment_name";
    public final static String PROJECT_ID = "project_id";
    public final static String PROJECT_NAME = "project_name";
    // handsontable列字段名 （公共 ）
    public final static String DESCRIPTION = "description";
    public final static String PROTOCOL = "protocol";
    public final static String RELATED_LINKS = "related_links";

    @Id
    private String id;

    @NotBlank(message = "The name cannot be empty")
    @Pattern(regexp = "^(?!\\s+$)[-\\w ]{1,40}$", message = "Illegal name format")
    private String name;

    private String parentName;

    // 模板版本
    @NotBlank(message = "The version cannot be empty")
    @Pattern(regexp = "^\\d+(\\.\\d+){0,2}$", message = "Illegal version format")
    private String version;

    // 模板状态
    @ValidEnum(enumClass = DataStatusEnum.class, allowNull = false, message = "Illegal status")
    private String status;

    // 推荐填写个数
    @NotNull(message = "The Recommend Num cannot be empty")
    @Max(value = 200, message = "The Recommend Num cannot be greater than 200")
    @Min(value = 0, message = "The Recommend Num cannot be less than 0")
    private Integer recommendNum;
    // 填写提示信息
    private String recommendTip;

    /**
     * 字典数据的类别 experiment or sample
     *
     * @see ExpSampleTypeEnum
     */
    @Indexed
    @ValidEnum(enumClass = ExpSampleTypeEnum.class, allowNull = false, message = "Illegal type")
    private String type;

    @NotNull(message = "The Sort Num cannot be empty")
    @Max(value = 200, message = "The Sort Num cannot be greater than 200")
    @Min(value = 1, message = "The Sort Num cannot be less than 1")
    private Integer sort;

    private String iconName;
    private String iconColor;

    @NotBlank(message = "Template file cannot be empty")
    private String templateName;
    @NotBlank(message = "Example file cannot be empty")
    private String exampleName;

    private String description;
    private List<Attributes> attributes;

    /**
     * 控制组学或者样本中的基本信息字段类型：必填、推荐、选填、没有
     *
     * @see BaseAttrType
     */
    @ValidEnum(enumClass = BaseAttrType.class, allowNull = true, message = "Illegal Organism type")
    private String organism;
    @ValidEnum(enumClass = BaseAttrType.class, allowNull = true, message = "Illegal Tissue type")
    private String tissue;
    // 控制组学或者样本中的描述信息和实验方案是否必填、推荐
    @ValidEnum(enumClass = BaseAttrType.class, allowNull = false, message = "Illegal Description type")
    private String desc;
    @ValidEnum(enumClass = BaseAttrType.class, allowNull = false, message = "Illegal Protocol type")
    private String protocol;

    private String creator;
    private Date createTime;
    private Date updateTime;

    @Data
    public static class Attributes {
        @Field(name = "id")
        private String id;

        /**
         * @see BaseAttrType
         */
        @ValidEnum(enumClass = BaseAttrType.class, allowNull = false, message = "Illegal required type")
        private String required;
        private String attributesName;
        private String attributesField;
        private String description;
        private String zhDescription;
        private String valueFormat;
        private String valueRegex;
        // sample中的分组
        private Integer group;
        private Integer sort;

        /**
         * @see ExpSampleDataType
         */
        @ValidEnum(enumClass = ExpSampleDataType.class, allowNull = false, message = "Illegal attribute input type")
        private String dataType;
        private List<Object> valueRange;
        // 下拉框数据是否允许自定义内容
        private boolean allowCreate = false;

        @ValidEnum(enumClass = DataStatusEnum.class, allowNull = false, message = "Illegal attribute status")
        private String status;

        private String dataSource;
        private Date createTime;
        private Date updateTime;

        @Transient
        private List<String> rangeStrList;

        /**
         * 是否为基础数据，当配置handsontable基础数据列时（如:project_id, project_name），设置为true
         */
        @Transient
        private boolean customAttrFlag = false;

        public Attributes() {
        }

        public Attributes(List<String> rangeStrList) {
            this.rangeStrList = rangeStrList;
        }
    }

    /**
     * 先按照group升序，再按sort升序
     */
    public static class MyAttributesComparator implements Comparator<Attributes> {
        @Override
        public int compare(Attributes o1, Attributes o2) {
            // 检查o1和o2的group是否都非空
            if (o1.getGroup() != null && o2.getGroup() != null) {
                // 如果两者都不为空，先按group升序排序
                int result = o1.getGroup().compareTo(o2.getGroup());

                // 如果group相等，则进一步按sort升序排序
                if (result == 0) {
                    result = o1.getSort().compareTo(o2.getSort());
                }
                return result;
            } else {
                // 如果两者存在空，则直接按sort升序排序
                return o1.getSort().compareTo(o2.getSort());
            }
            /* else {
                // 当只有一个对象的group为null时，将null视为最小值（或最大值）
                // 根据实际业务需求调整此逻辑，这里假设null应排在后面
                return o1.getGroup() == null ? 1 : -1;
            }*/
        }
    }

    public static Map<String, Attributes> handelExpAttrMap(final ExpSampleType expSampleType) {
        final Map<String, ExpSampleType.Attributes> rangeMap = new LinkedHashMap<>();
        if (expSampleType != null) {
            final List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
            if (CollUtil.isNotEmpty(attributes)) {
                for (ExpSampleType.Attributes attribute : attributes) {
                    if (!DataStatusEnum.enable.name().equals(attribute.getStatus())) {
                        // 忽略禁用的字段
                        continue;
                    }
                    final String attributesField = attribute.getAttributesField();
                    // 处理下拉框字段
                    attribute.setRangeStrList(initRangeStrList(attribute.getValueRange(), attributesField));
                    rangeMap.put(attributesField, attribute);
                }
            }
        }
        return rangeMap;
    }

    public static List<String> initRangeStrList(final List<Object> valueRangeList, final String attributesField) {
        List<String> valueRanges = null;
        if (CollUtil.isNotEmpty(valueRangeList)) {
            if (PLATFORM.equals(attributesField)) {
                valueRanges = new ArrayList<>();
                for (Object o : valueRangeList) {
                    final JSONObject jsonObject = (JSONObject) JSON.toJSON(o);
                    valueRanges.addAll(jsonObject.getJSONArray(VALUE_ARRAY).toJavaList(String.class));
                }
            } else {
                valueRanges = valueRangeList.stream().map(Object::toString).collect(Collectors.toList());
            }
        }
        return valueRanges;
    }

    /**
     * handsontable表格插件基础信息列
     */
    public static List<String> allBaseColumn() {
        return CollUtil.toList(SAMPLE_ID, SAMPLE_NAME, ORGANISM, TISSUE, EXPERIMENT_ID, EXPERIMENT_NAME, PROJECT_ID, PROJECT_NAME, DESCRIPTION, PROTOCOL, RELATED_LINKS);
    }

}
