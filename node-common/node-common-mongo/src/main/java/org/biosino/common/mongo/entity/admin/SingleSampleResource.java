package org.biosino.common.mongo.entity.admin;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.LinkedHashSet;

/**
 * 特殊数据集 Single Sample Multiple Omics Resource
 *
 * <AUTHOR>
 */
@Data
@Document("special_single_sample_resource")
public class SingleSampleResource {

    @Id
    private String id;

    @NotBlank(message = "Sample ID cannot be empty")
    @Field("sap_no")
    @Excel(name = "Sample ID", cellType = Excel.ColumnType.STRING)
    @Indexed(unique = true)
    private String sapID;

    private String sapName;
    private String sapType;
    private String submitter;
    private String des;

    private LinkedHashSet<String> expTypes;

    private String status;

    private String creator;
    private Date createTime;
    private Date modifiedDate;

//    private StatDTO statInfo;

}
