package org.biosino.common.mongo.enums;

import lombok.Getter;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.mongo.entity.Sample;

import java.util.Optional;

@Getter
public enum PopularDataType {
    Projects("proj_no", Project.class, AuthorizeType.project, "relaExpType", "relaSampleType"),
    Experiments("exp_no", Experiment.class, AuthorizeType.experiment, "relaExpType", "relaSampleType"),
    Samples("sap_no", Sample.class, AuthorizeType.sample, "relaSampleType"),
    Analysis("anal_no", org.biosino.common.mongo.entity.Analysis.class, AuthorizeType.analysis, "relaAnalysisType"),
    ;

    private final String noField;
    private final Class<?> dbClz;
    private final AuthorizeType authorizeType;
    private final String[] typeFields;

    PopularDataType(String noField, Class<?> dbClz, AuthorizeType authorizeType, String... typeFields) {
        this.noField = noField;
        this.dbClz = dbClz;
        this.authorizeType = authorizeType;
        this.typeFields = typeFields;
    }

    public static Optional<PopularDataType> findByName(final String name) {
        if (name == null) {
            return Optional.empty();
        }
        for (PopularDataType value : values()) {
            if (value.name().equals(name)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

}
