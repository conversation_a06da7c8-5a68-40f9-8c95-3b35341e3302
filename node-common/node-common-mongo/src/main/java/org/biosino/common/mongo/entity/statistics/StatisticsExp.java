package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 实验统计
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_exp")
public class StatisticsExp extends BaseStatistics {
    @Id
    private String id;

    private String type;

    // 存量 EXPERIMENT_TYPE_GENOMIC_001，experiment表中visible_status为Unaccessible或Accessible，且exp_type为Genomic的exp_no总数
    private Long total = 0L;

    // 存量 EXPERIMENT_TYPE_GENOMIC_002，experiment表中visible_status为Accessible，且exp_type为Genomic的exp_no总数
    private Long accessibleNum = 0L;

    // 存量 EXPERIMENT_TYPE_GENOMIC_003，EXPERIMENT_TYPE_GENOMIC_001 - EXPERIMENT_TYPE_GENOMIC_002
    private Long unAccessibleNum = 0L;

    // 增量 EXPERIMENT_TYPE_GENOMIC_004，EXPERIMENT_TYPE_GENOMIC_001（当前月份）-EXPERIMENT_TYPE_GENOMIC_001（上个月）
    private Long totalGrowth = 0L;

    // 增量 EXPERIMENT_TYPE_GENOMIC_005，EXPERIMENT_TYPE_GENOMIC_002（当前月份）-EXPERIMENT_TYPE_GENOMIC_002（上个月）
    private Long accessibleGrowth = 0L;

    // 增量 EXPERIMENT_TYPE_GENOMIC_006，EXPERIMENT_TYPE_GENOMIC_003（当前月份）-EXPERIMENT_TYPE_GENOMIC_003（上个月）
    private Long unAccessibleGrowth = 0L;

    // 存量 EXPERIMENT_TYPE_GENOMIC_007，查询node_related_es索引库中，expNo在EXPERIMENT_TYPE_GENOMIC_001所属exp_no集合内的fileSize总和，单位TB
    private Long totalFileSize = 0L;

    // 存量 EXPERIMENT_TYPE_GENOMIC_008，查询node_related_es索引库中，expNo在EXPERIMENT_TYPE_GENOMIC_002所属exp_no集合内的fileSize总和，单位TB
    private Long accessibleFileSize = 0L;

    // 存量 EXPERIMENT_TYPE_GENOMIC_009，查询node_related_es索引库中，expNo在EXPERIMENT_TYPE_GENOMIC_003所属exp_no集合内的fileSize总和，单位TB
    private Long unAccessibleFileSize = 0L;

    // 增量 EXPERIMENT_TYPE_GENOMIC_010，EXPERIMENT_TYPE_GENOMIC_007（当前月份）-EXPERIMENT_TYPE_GENOMIC_007（上个月）
    private Long totalFileSizeGrowth = 0L;

    // 增量 EXPERIMENT_TYPE_GENOMIC_011，EXPERIMENT_TYPE_GENOMIC_008（当前月份）-EXPERIMENT_TYPE_GENOMIC_008（上个月）
    private Long accessibleFileSizeGrowth = 0L;

    // 增量 EXPERIMENT_TYPE_GENOMIC_012，EXPERIMENT_TYPE_GENOMIC_009（当前月份）-EXPERIMENT_TYPE_GENOMIC_009（上个月）
    private Long unAccessibleFileSizeGrowth = 0L;


}
