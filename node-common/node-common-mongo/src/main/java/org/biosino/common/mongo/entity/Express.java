package org.biosino.common.mongo.entity;

import lombok.Data;
import org.biosino.common.mongo.entity.other.Submitter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

@Data
@Document("express")
public class Express {

    @Id
    private String id;

    @Field("express_name")
    private String expressName;

    @Field("tracking_num")
    private String trackingNum;

    @Field("file_path")
    private String filePath;

    @Field("submitter")
    private Submitter submitter;

    private String creator;

    @Field("create_time")
    private Date createTime;

    private String remark;
}
