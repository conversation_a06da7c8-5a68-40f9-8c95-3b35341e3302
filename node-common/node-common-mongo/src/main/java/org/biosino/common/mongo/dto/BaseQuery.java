package org.biosino.common.mongo.dto;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Date;

import static org.springframework.data.domain.Sort.Direction.DESC;

/**
 * <AUTHOR>
 * @date 2024/1/4
 */
@Data
public class BaseQuery {
    @Min(value = 1, message = "pageNum不能小于1")
    private Integer pageNum = 1;

    @Max(value = 1000, message = "pageSize不能超过1000")
    @Min(value = 1, message = "pageSize不能小于1")
    private Integer pageSize = 10;
    private String orderByColumn;
    private String isAsc;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    public Pageable getPageable() {
        // spring的Pageable 的页码数是从0开始的
        pageSize = pageSize == null || pageSize <= 0 ? Integer.MAX_VALUE : pageSize;
        if (StrUtil.isBlank(orderByColumn)) {
            return PageRequest.of(pageNum - 1, pageSize);
        }
        if (StrUtil.equalsIgnoreCase(isAsc, DESC.name())) {
            return PageRequest.of(pageNum - 1, pageSize, Sort.by(Sort.Order.desc(orderByColumn)));
        }
        return PageRequest.of(pageNum - 1, pageSize, Sort.by(Sort.Order.asc(orderByColumn)));
    }

    /**
     * 兼容前端排序类型
     *
     * @param isAsc
     */
    public void setIsAsc(String isAsc) {
        if (StrUtil.isNotEmpty(isAsc)) {
            // 兼容前端排序类型
            if ("ascending".equals(isAsc)) {
                isAsc = "asc";
            } else if ("descending".equals(isAsc)) {
                isAsc = "desc";
            }
            this.isAsc = isAsc;
        }
    }
}
