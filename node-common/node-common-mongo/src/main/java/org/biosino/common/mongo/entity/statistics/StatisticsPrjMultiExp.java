package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 多组学项目统计
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_prj_multi_exp")
public class StatisticsPrjMultiExp extends BaseStatistics {
    @Id
    private String id;

    // 存量 MULTI_OMICS_PROJECT_BASE_001，查询node_related_es索引库中，expType类别超过1种的projNo的个数
    private Long multi = 0L;

    // 存量 MULTI_OMICS_PROJECT_BASE_002，查询node_related_es索引库中，expType类别超过1种、且projVisible为Accessible的projNo的个数
    private Long accessibleMulti = 0L;

    // 存量 MULTI_OMICS_PROJECT_BASE_003，查询node_related_es索引库中，expType类别只有1种的projNo的个数
    private Long single = 0L;

    // 存量 MULTI_OMICS_PROJECT_BASE_013，查询node_related_es索引库中，MULTI_OMICS_PROJECT_BASE_001对应projNo所属data文件大小总和，单位：GB
    private Long multiFileSize = 0L;

    // 存量 MULTI_OMICS_PROJECT_BASE_014，查询node_related_es索引库中，MULTI_OMICS_PROJECT_BASE_002对应projNo所属data文件大小总和，单位：GB
    private Long accessibleMultiFileSize = 0L;

    private List<TypeStat> expMultTypeStat;
    private List<TypeStat> expSingleTypeStat;

    @Data
    public static class TypeStat {
        private String type;
        private int count;
    }

}
