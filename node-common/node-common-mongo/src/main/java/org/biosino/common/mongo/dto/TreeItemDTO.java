package org.biosino.common.mongo.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class TreeItemDTO implements Serializable {

    @Field("id")
    private String id;
    // 树节点展示名称
    private String name;
    // es字段名称
    private String fieldName;
    private Integer number;
    private List<TreeItemDTO> data;
    private List<String> defaultCheckedKeys = new ArrayList<>();
    private List<String> defaultExpandedKeys = new ArrayList<>();

    // 是否展开
    private Boolean isExpand;
    // 是否显示
    private Boolean isShow;
    // 是否首字母大写
    private boolean upperFirst = false;

    public TreeItemDTO(String id, String name) {
        this(id, name, false);
    }

    public TreeItemDTO(String id, String name, boolean upperFirst) {
        this.id = id;

        this.name = name;
        this.fieldName = name;

        this.isExpand = false;
        this.isShow = true;
        this.upperFirst = upperFirst;
    }

}
