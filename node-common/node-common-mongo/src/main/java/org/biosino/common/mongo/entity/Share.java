package org.biosino.common.mongo.entity;


import lombok.Data;
import org.biosino.common.core.enums.ShareStatusEnum;
import org.biosino.common.core.validator.ValidEnum;
import org.biosino.common.mongo.entity.other.*;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@Data
@Document
public class Share {

    @Id
    private String id;

    @NotBlank(message = "share_id不能为空")
    @GenerateValue(prefix = SequenceType.SHARE)
    @Field("share_id")
    private String shareId;

    @NotEmpty(message = "share_to不能为空")
    @Field("share_to")
    private List<String> shareTo;

    private List<ShareProject> projects;

    private List<ShareExperiment> experiments;

    private List<ShareSample> samples;

    private List<ShareRun> runs;

    private List<ShareAnalysis> analysis;

    private List<ShareData> datas;

    @NotEmpty(message = "creator不能为空")
    private String creator;

    @Field("share_date")
    private Date shareDate;

    @Field("expire_date")
    private Date expireDate;

    @ValidEnum(enumClass = ShareStatusEnum.class, allowNull = false, message = "status不在合法范围内")
    private String status;

    private List<String> see;

    private String creatorEmail;

}
