package org.biosino.common.mongo.entity;

import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/15
 */
@Data
@Builder
@Document("http_upload_log")
public class HttpUploadLog {
    @Id
    private String id;

    @Field("data_no")
    private String dataNo;

    @Field("file_size")
    private Long fileSize;

    private String creator;

    @Field("create_time")
    private Date createTime;

    @Field("ip")
    private String ip;

}
