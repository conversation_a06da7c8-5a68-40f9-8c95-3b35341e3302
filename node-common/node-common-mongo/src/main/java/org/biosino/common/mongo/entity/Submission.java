package org.biosino.common.mongo.entity;

import lombok.Data;
import org.biosino.common.mongo.entity.other.RejectReason;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "submission")
public class Submission {
    @Id
    private String id;

    @Field("sub_no")
    @GenerateValue(prefix = SequenceType.UPLOADSUBMISSION)
    private String subNo;

    /**
     * @see org.biosino.common.core.enums.SubmissionDataTypeEnum
     */
    @Field("data_type")
    private String dataType;

    /**
     * @see org.biosino.common.core.enums.SubmissionStatusEnum
     */
    private String status;

    @Field("auditor_id")
    private Long auditorId;
    private String auditor;

    @Field("audit_time")
    private Date auditTime;

    @Field("reject_reason")
    private List<RejectReason> rejectReason;

    // 创建人，node-cas的用户ID
    private String creator;

    @Field("create_time")
    private Date createTime;

    @Field("update_time")
    private Date updateTime;

    // 首次提交时间，主要用途是统计
    @Field("first_submit_time")
    private Date firstSubmitTime;

    @Field("submit_time")
    private Date submitTime;

    private Submitter submitter;

    @Field("proj_no")
    private String projNo;

    @Field("exp_single_no")
    private String expSingleNo;

    @Field("exp_multiple_nos")
    private List<String> expMultipleNos;

    @Field("sap_single_no")
    private String sapSingleNo;

    @Field("sap_multiple_data")
    private List<SampleGroup> sapMultipleData;

    @Field("anal_single_no")
    private String analSingleNo;

    // 批量提交分析
    @Field("anal_multiple_nos")
    private List<String> analMultipleNos;

    // single页面归档分析
    @Field("analysis_data_nos")
    private List<String> analysisDataNos;

    // 批量归档分析
    @Field("analysis_data_multiple_nos")
    private List<String> analDataMultipleNos;

    // single页面归档raw data
    @Field("raw_data_nos")
    private List<String> rawDataNos;

    // 批量归档raw data
    @Field("raw_data_multiple_nos")
    private List<String> rawDataMultipleNos;

    /**
     * 在详情页面新增的文献ID
     */
    @Field("publish_id")
    private String publishId;

    // 统计信息
    @Field("proj_num")
    private Integer projNum = 0;

    @Field("exp_num")
    private Integer expNum = 0;

    @Field("sap_num")
    private Integer sapNum = 0;

    @Field("anal_num")
    private Integer analNum = 0;

    @Field("run_num")
    private Integer runNum = 0;

    @Field("data_num")
    private Integer dataNum = 0;

    // project + experiment + sample + analysis + run + data + publish
    private Integer total = 0;

    @Field("data_size")
    private Long dataSize = 0L;

    @Field("publish_num")
    private Integer publishNum = 0;

    @Data
    public static class SampleGroup {
        private String type;
        private List<String> nos;

        public SampleGroup() {
        }

        public SampleGroup(String type, List<String> nos) {
            this.type = type;
            this.nos = nos;
        }
    }

}
