package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_download")
public class StatisticsDownload {

    @Id
    private String id;

    @Field("owner_id")
    private String ownerId;

    @Field
    private String month;

    @Field("project_count")
    private long projectCount;

    @Field("project_country")
    private Map<String, Long> projectCountry;

    @Field("experiment_count")
    private long experimentCount;

    @Field("experiment_country")
    private Map<String, Long> experimentCountry;

    @Field("sample_count")
    private long sampleCount;

    @Field("sample_country")
    private Map<String, Long> sampleCountry;

    @Field("analysis_count")
    private long analysisCount;

    @Field("analysis_country")
    private Map<String, Long> analysisCountry;

    @Field("data_count")
    private long dataCount;

    @Field("data_country")
    private Map<String, Long> dataCountry;

    private Long ftpPublicData = 0L;
    private Long ftpPublicDataSize = 0L;
    private Long ftpRestrictedData = 0L;
    private Long ftpRestrictedDataSize = 0L;
    private Long ftpPrivateData = 0L;
    private Long ftpPrivateDataSize = 0L;

    private Long httpPublicData = 0L;
    private Long httpPublicDataSize = 0L;
    private Long httpRestrictedData = 0L;
    private Long httpRestrictedDataSize = 0L;
    private Long httpPrivateData = 0L;
    private Long httpPrivateDataSize = 0L;

    public StatisticsDownload(String ownerId, String month) {
        this.ownerId = ownerId;
        this.month = month;
    }
}
