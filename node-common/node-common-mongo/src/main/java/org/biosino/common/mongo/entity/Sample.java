package org.biosino.common.mongo.entity;

import lombok.Data;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.validator.ValidEnum;
import org.biosino.common.mongo.authorize.IJudgeAuthorize;
import org.biosino.common.mongo.authorize.Judge;
import org.biosino.common.mongo.entity.other.OtherIds;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.GenerateValue;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.validation.constraints.NotBlank;
import java.util.*;

@Data
@Document
public class Sample implements IJudgeAuthorize {

    @Id
    private String id;

    @Field("sap_no")
    @NotBlank(message = "sap_no不能为空")
    @GenerateValue(prefix = SequenceType.SAMPLE)
    private String sapNo;

    /**
     * Submission表NO
     */
    @Field("sub_no")
    private String subNo;

    private String name;
    private String description;
    private String protocol;

    @Field("tax_id")
    private String taxId;
    @NotBlank(message = "organism不能为空")
    private String organism;
    private String tissue;

    @ValidEnum(enumClass = AuditEnum.class, allowNull = false, message = "audited不在合法范围内")
    private String audited;

    @Field("related_links")
    private List<String> relatedLinks;

    @NotBlank(message = "creator不能为空")
    private String creator;

    @Field("submission_date")
    private Date createDate;

    // Node2.0 废弃，因为updater就是creator
    // private String updater;

    @Field("update_date")
    private Date updateDate;

    @Field("public_date")
    private Date publicDate;

    @Field("other_ids")
    private List<OtherIds> otherIds;

    @Field("operator")
    private String operator;

    @Field("operation_date")
    private Date operationDate;

    @Field("hit_num")
    private Long hitNum;

    @Field("export_num")
    private Long exportNum;

    @Field("submitter")
    private Submitter submitter;

    /**
     * 样本暂存数据
     */
    @Field(TEMP_DATA_FIELD)
    private Sample tempData;

    // 管理员定义的样本表单属性
    private Map<String, String> attributes = new LinkedHashMap<>();

    // 用户自定义的属性
    @Field("custom_attr")
    private Map<String, String> customAttr = new LinkedHashMap<>();

    // 用户自定义的属性描述信息
    @Field("custom_attr_desc")
    private Map<String, String> customAttrDesc = new LinkedHashMap<>();

    @Field("used_ids")
    private List<String> usedIds;

    @Field("ownership")
    private String ownership;

    @Field("source_project")
    private List<String> sourceProject;

    /**
     * @see org.biosino.common.core.enums.SampleSubjectTypeEnum
     */
    @Field("subject_type")
    private String subjectType;

    @ValidEnum(enumClass = VisibleStatusEnum.class, allowNull = false, message = "visible_status不在合法范围内")
    // VisibleStatusEnum
    @Field("visible_status")
    private String visibleStatus;


    @Override
    public String getObjType() {
        return "sample";
    }

    @Override
    public Judge getJudge() {
        Judge judge = new Judge();
        judge.setObjNo(getSapNo());
        judge.setCreator(getCreator());
        judge.setPubDate(getPublicDate());
        judge.setVisibleStatus(getVisibleStatus());
        return judge;
    }


    @Transient
    private LinkedHashSet<String> dataTypes;
    @Transient
    private LinkedHashSet<String> securities;
    @Transient
    private List<Publish> publishInfos;
    @Transient
    private LinkedHashSet<FastQCTask.SeqkitResult> seqkitResultSet;

    // 用于前端browse明细界面展示Other类型数据
    @Transient
    private Map<String, String> customAttrOther = new LinkedHashMap<>();

}
