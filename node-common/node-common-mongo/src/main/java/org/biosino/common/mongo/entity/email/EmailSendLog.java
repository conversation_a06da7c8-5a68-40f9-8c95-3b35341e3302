package org.biosino.common.mongo.entity.email;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * 邮件模版
 *
 * <AUTHOR>
 */
@Data
@Document("email_send_log")
public class EmailSendLog {
    @Id
    private String id;

    @Field("email_no")
    private String emailNo;
    private String recipient;
    private String subject;
    private String remark;
    private String content;

    private String creator;
    private Date createTime;
    private Date updateTime;

    private String status;
    private String errorMsg;

}
