package org.biosino.common.mongo.entity.statistics;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 样本-实验组合统计
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "statistics_sample_exp")
public class StatisticsSampleExp extends BaseStatistics {
    @Id
    private String id;

    private String sapType;
    private String expType;

    private Long expTotal = 0L;

    private Long expTotalFileSize = 0L;

    private Long expPrivateFileSize = 0L;


}
