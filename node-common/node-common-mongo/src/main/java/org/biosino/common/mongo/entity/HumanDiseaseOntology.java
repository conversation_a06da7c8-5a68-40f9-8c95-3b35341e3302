package org.biosino.common.mongo.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@Document(collection = "humanDiseaseOntology")
public class HumanDiseaseOntology {
    @Id
    private String id;

    @Field("DOID")
    private String doid;

    @Field("type")
    private String type;

    @Field("lbl")
    private String lbl;
}
