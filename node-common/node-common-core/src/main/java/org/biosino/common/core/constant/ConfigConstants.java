package org.biosino.common.core.constant;

/**
 * 系统参数管理配置
 *
 * <AUTHOR>
 */
public class ConfigConstants {
    // 开启
    public static final String enable = "enable";
    // 关闭
    public static final String disable = "disable";

    /**
     * 整个系统Project、Experiment等的ID位数（谨慎修改）
     */
    public static final int ID_LENGTH = 8;

    /**
     * 系统数据起始时间
     */
    public static final int START_YEAR = 2016;
    /**
     * 系统数据起始时间
     */
    public static final int START_MONTH = 3;

    /**
     * 实验组学类型密钥
     */
    public static final String EXP_TOKEN = "node.exp.type";

    /**
     * 样本组学类型密钥
     */
    public static final String SAMPLE_TOKEN = "node.sample.type";

    /**
     * 是否开启审核系统
     */
    public static final String QC_STATUS = "node.qc.status";

    /**
     * 是否开启fastqc任务
     */
    public static final String FASTQC_STATUS = "node.fastqc.status";

    /**
     * 数据共享-Request
     */
    public static final String REQUEST_STATUS = "node.request.status";

    /**
     * 数据共享-Share
     */
    public static final String SHARE_STATUS = "node.share.status";

    /**
     * 数据共享-Review
     */
    public static final String REVIEW_STATUS = "node.review.status";

    /**
     * 组学和样本的许可证开关
     */
    public static final String LICENSE_STATUS = "node.license.status";

    /**
     * 特色数据集开关
     */
    public static final String FEATURE_DATA_STATUS = "node.featureData.status";

    /**
     * 漏洞扫描IP
     */
    public static final String BUG_SCAN_IP = "sys.bugScan.IPList";
}
