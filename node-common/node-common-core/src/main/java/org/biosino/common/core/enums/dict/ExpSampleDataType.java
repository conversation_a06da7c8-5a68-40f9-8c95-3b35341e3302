package org.biosino.common.core.enums.dict;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * exp_sample_type中dataType字段
 *
 * <AUTHOR>
 */
@Getter
public enum ExpSampleDataType {

    Input(ExcelCellType.string_, "Input", "字符串类型的文本数据"),
    Select(ExcelCellType.string_, "Select", "下拉框"),
    Select2(ExcelCellType.string_, "Two Level Select", "2级下拉框"),
    Number_int(ExcelCellType.integer_, "Integer", "整型数据"),
    Number_double(ExcelCellType.double_, "Float", "浮点型数据"),
    Date(ExcelCellType.date_, "Date", "日期数据，日期格式为 yyyy-MM-dd hh:mm:ss"),
    Textarea(ExcelCellType.string_, "Textarea", "超长文本类型数据"),
    Custom(ExcelCellType.string_, "Custom", "特殊的自定义字段需要联系开发人员来实现规则。请清楚地解释数据来源"),
    ;

    private final ExcelCellType cellType;
    private final String title;
    private final String remark;

    ExpSampleDataType(ExcelCellType cellType, String title, String remark) {
        this.cellType = cellType;
        this.title = title;
        this.remark = remark;
    }

    public static Optional<ExpSampleDataType> findByName(String name) {
        if (StrUtil.isBlank(name)) {
            return Optional.empty();
        }
        final ExpSampleDataType[] values = ExpSampleDataType.values();
        for (ExpSampleDataType value : values) {
            if (value.name().equals(name)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

    public static Map<String, ExcelCellType> findCellTypeMap() {
        Map<String, ExcelCellType> map = new LinkedHashMap<>();
        final ExpSampleDataType[] values = ExpSampleDataType.values();
        for (ExpSampleDataType value : values) {
            map.put(value.name(), value.getCellType());
        }
        return map;
    }

    public static Map<String, ExpSampleDataType> nameMap() {
        Map<String, ExpSampleDataType> map = new LinkedHashMap<>();
        final ExpSampleDataType[] values = values();
        for (ExpSampleDataType value : values) {
            map.put(value.name(), value);
        }
        return map;
    }

    public static Map<String, ExpSampleDataType> titleMap() {
        Map<String, ExpSampleDataType> map = new LinkedHashMap<>();
        final ExpSampleDataType[] values = values();
        for (ExpSampleDataType value : values) {
            map.put(value.getTitle(), value);
        }
        return map;
    }

    /**
     * NODE数据集标准导出时，生成数据类型列
     */
    public static String initExportType(final String dataType) {
        if (dataType == null) {
            return null;
        }
        final ExpSampleDataType[] values = values();
        for (final ExpSampleDataType value : values) {
            if (value.name().equals(dataType)) {
                switch (value) {
                    case Input:
                        return "STRING";
                    case Number_int:
                        return "INT";
                    case Number_double:
                        return "FLOAT";
                    case Date:
                        return "DATETIME";
                    case Textarea:
                        return "TEXT";
                    case Custom:
                    case Select:
                    case Select2:
                        return "ENUM";
                }
            }
        }
        return null;
    }

}
