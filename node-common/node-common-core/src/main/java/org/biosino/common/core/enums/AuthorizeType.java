package org.biosino.common.core.enums;

import cn.hutool.core.util.StrUtil;

import java.util.Optional;

public enum AuthorizeType {

    project, sample, experiment, run, data, analysis, share, review, archiving, fastqc;

    public static Optional<AuthorizeType> findByName(String name) {
        if (StrUtil.isBlank(name)) {
            return Optional.empty();
        }
        final AuthorizeType[] values = AuthorizeType.values();
        for (AuthorizeType value : values) {
            if (value.name().equals(name)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }

}
