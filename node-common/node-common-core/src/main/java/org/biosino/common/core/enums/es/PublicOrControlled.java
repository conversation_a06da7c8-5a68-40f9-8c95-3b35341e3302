package org.biosino.common.core.enums.es;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum PublicOrControlled {
    Public(true),
    Controlled(false);

    private final boolean val;

    PublicOrControlled(boolean val) {
        this.val = val;
    }

    public static String findNameByBool(boolean val) {
        PublicOrControlled[] values = PublicOrControlled.values();
        for (PublicOrControlled value : values) {
            if (value.val == val) {
                return value.name();
            }
        }
        return null;
    }
}
