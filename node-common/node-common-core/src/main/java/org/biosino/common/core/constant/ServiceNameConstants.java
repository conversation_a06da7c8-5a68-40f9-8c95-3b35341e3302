package org.biosino.common.core.constant;

/**
 * 服务名称
 *
 * <AUTHOR>
 */
public class ServiceNameConstants {
    /**
     * 认证服务的serviceid
     */
    public static final String AUTH_SERVICE = "node-auth";

    /**
     * 系统模块的serviceid
     */
    public static final String SYSTEM_SERVICE = "node-system";

    /**
     * 文件服务的serviceid
     */
    public static final String FILE_SERVICE = "node-file";

    /**
     * NODE用户服务的serviceid
     */
    public static final String NODE_MEMBER_SERVICE = "node-member";
    /**
     * NODEt通知服务的serviceid
     */
    public static final String NODE_NOTIFICATION_SERVICE = "node-notification";

    /**
     * 公共ES数据检索服务
     */
    public static final String NODE_ES_SERVICE = "node-es-index";

    /**
     * Node 门户网站
     */
    public static final String NODE_APP_SERVICE = "node-app";

    /**
     * Node 数据提交 服务
     */
    public static final String NODE_UPLOAD_SERVICE = "node-upload";

    /**
     * Node 审核 服务
     */
    public static final String NODE_QC_SERVICE = "node-qc";

    /**
     * Node Task服务
     */
    public static final String NODE_TASK_SERVICE = "node-task";

    /**
     * Node 定时任务 服务
     */
    public static final String NODE_JOB_SERVICE = "node-job";
}
