package org.biosino.common.core.enums;

public enum BussinessTaskTypeEnum {
    data_transfer, ftp_upload, custom_analysis, uploadSubmission, fastqc, rna_seq, iMAC, data_transfer_to_ftp;
}
/*
 * data_transfer:数据传输任务，对应nodeadmin的data transfer功能和excel批量导入功能
 * ftp_upload:ftp上传数据任务，对应node系统从ftp缓存提交到node的功能
 * cutom_analysis:定制分析任务，对应node系统的定制分析任务，包括把数据从node同步到分析服务器和把分析服务器上的分析结果数据同步到node系统
 * oneStopMultipleSubmit:submit metadata by one stop multiple批量提交数据
 * fastqc:fastqc分析任务，对应node系统的fastqc分析任务，包括把数据从node同步到fastqc分析目录和把fastqc分析服务器上的结果同步到node系统
 * data_transfer_to_ftp: 往ftp_home目录下上传数据，对应后台的data transfer功能页面上填写了ftp路径的情况*/
