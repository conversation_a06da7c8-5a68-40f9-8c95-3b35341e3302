package org.biosino.common.core.utils;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Encode {

    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /**
     * <AUTHOR>
     */
    public static String encode(String password) {
        if (StringUtils.isBlank(password)) return null;
        MessageDigest messageDigest = null;
        try {
            messageDigest = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        if (Charset.isSupported("UTF-8")) {
            messageDigest.update(password.getBytes(Charset.forName("UTF-8")));
        } else {
            messageDigest.update(password.getBytes());
        }
        final byte[] digest = messageDigest.digest();
        return getFormattedText(digest);
    }

    /**
     * <AUTHOR>
     */
    private static String getFormattedText(byte[] bytes) {
        final StringBuilder buf = new StringBuilder(bytes.length * 2);

        for (int j = 0; j < bytes.length; j++) {
            buf.append(HEX_DIGITS[(bytes[j] >> 4) & 0x0f]);
            buf.append(HEX_DIGITS[bytes[j] & 0x0f]);
        }
        return buf.toString();
    }
}
