package org.biosino.common.core.enums.dict;

import org.biosino.common.core.enums.es.YesOrNo;

/**
 * <AUTHOR>
 */

public enum BaseAttrType {
    //有且选填  有且必填    推荐填写     无
    optional, required, recommend, none;

    public static YesOrNo findYesOrNoByType(final String val) {
        return BaseAttrType.required.equals(BaseAttrType.valueOf(val))
                ? YesOrNo.Yes : YesOrNo.No;
    }

}
