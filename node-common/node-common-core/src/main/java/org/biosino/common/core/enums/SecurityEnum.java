package org.biosino.common.core.enums;

import java.util.ArrayList;
import java.util.List;

public enum SecurityEnum {

    _private("Private"),
    _public("Public"),
    _restricted("Restricted"),
    _require_request("Require Request"),
    _private_delete("Private_Delete"),
    _public_delete("Public_Delete"),
    _restricted_delete("Restricted_Delete");

    private String desc;

    SecurityEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static List<String> notPublicSecurity() {
        List<String> list = new ArrayList<>();
        list.add(SecurityEnum._private.getDesc());
        list.add(SecurityEnum._restricted.getDesc());
        return list;
    }

    public static List<String> includeSecurity() {
        List<String> list = new ArrayList<>();
        list.add(SecurityEnum._public.getDesc());
        list.add(SecurityEnum._restricted.getDesc());
        return list;
    }

    public static List<String> includeAllSecurity() {
        List<String> list = new ArrayList<>();
        list.add(SecurityEnum._public.getDesc());
        list.add(SecurityEnum._restricted.getDesc());
        list.add(SecurityEnum._private.getDesc());
        return list;
    }

    public static List<String> privateSecurity() {
        List<String> list = new ArrayList<>();
        list.add(SecurityEnum._private.getDesc());
        return list;
    }

    public static List<String> checkSecurityByShare() {
        List<String> list = new ArrayList<>();
        list.add(SecurityEnum._public.getDesc());
        list.add(SecurityEnum._restricted.getDesc());
        list.add(SecurityEnum._private.getDesc());
        list.add(SecurityEnum._private_delete.getDesc());
        list.add(SecurityEnum._public_delete.getDesc());
        list.add(SecurityEnum._restricted_delete.getDesc());
        return list;
    }

    public static List<String> checkSecurityByReview() {
        List<String> list = new ArrayList<>();
        list.add(SecurityEnum._public.getDesc());
        list.add(SecurityEnum._restricted.getDesc());
        return list;
    }

    public static List<String> deleteSecurity() {
        List<String> list = new ArrayList<>();
        list.add(SecurityEnum._private_delete.getDesc());
        list.add(SecurityEnum._public_delete.getDesc());
        list.add(SecurityEnum._restricted_delete.getDesc());
        return list;
    }

}
