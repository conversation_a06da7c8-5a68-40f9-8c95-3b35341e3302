package org.biosino.common.core.enums.stat;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/30
 */
public enum DataTypeEnum {
    fastq, fasta, bam, bed, bigwig, bw, bim, cel, csv, fq, maf, raw;


    public static List<String> allNames() {
        return Arrays.stream(values()).map(Enum::name).collect(Collectors.toList());
    }

}
