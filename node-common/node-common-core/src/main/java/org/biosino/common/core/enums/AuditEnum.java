package org.biosino.common.core.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * ID分配与审核状态
 */
public enum AuditEnum {
    init, // 未分配正式ID，未审核（使用场景：第一次提交的数据，UUID和未审核）
    audited, // 已分配正式ID，已经审核通过 （使用场景：已审核通过的数据）
    unaudited; // 已分配正式ID，未审核通过 （使用场景：已分配ID的数据，二次修改提交审核）


    /**
     * 获取所有非init状态
     */
    public static List<String> allNotInit() {
        AuditEnum[] values = AuditEnum.values();
        List<String> list = new ArrayList<>();
        for (AuditEnum value : values) {
            if (!value.equals(AuditEnum.init)) {
                list.add(value.name());
            }
        }
        return list;
    }

}
