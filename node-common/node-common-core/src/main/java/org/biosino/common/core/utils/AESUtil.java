package org.biosino.common.core.utils;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.zip.CRC32;

public class AESUtil {

    private static Logger logger = LoggerFactory.getLogger(AESUtil.class);

    public static final String DEFAULT_KEY = "**-NODE@PICB-***";


    public static String encrypt(String data) throws Exception {
        return encrypt(data, StandardCharsets.UTF_8, DEFAULT_KEY);
    }

    public static String encrypt(String data, Charset charset, String sKey) throws Exception {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(sKey)) {
            throw new IllegalArgumentException("parameter error!");
        }
        if (charset == null) {
            charset = StandardCharsets.UTF_8;
        }
        try {
            byte[] src = data.getBytes(charset);
            byte[] buf = encrypt(src, sKey);
            return Hex.encodeHexString(buf);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new Exception("encrypt " + data + " error, " + e.getMessage());
        }
    }

    public static String decrypt(String data) throws Exception {
        return decrypt(data, StandardCharsets.UTF_8, DEFAULT_KEY);
    }

    public static String decrypt(String data, Charset charset, String sKey) throws Exception {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(sKey)) {
            throw new IllegalArgumentException("Err, decrypt data is null");
        }
        if (charset == null) {
            charset = StandardCharsets.UTF_8;
        }
        try {
            byte[] bytes = Hex.decodeHex(data.toCharArray());
            byte[] buf = decrypt(bytes, sKey);
            return new String(buf, charset);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new Exception("decrypt " + data + " error, " + e.getMessage());
        }
    }

    private static byte[] encrypt(byte[] data, String sKey) throws Exception {
        byte[] key = sKey.getBytes();
        SecretKeySpec desKey = new SecretKeySpec(key, "AES");

        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, desKey);
        return cipher.doFinal(data);
    }

    private static byte[] decrypt(byte[] data, String sKey) throws Exception {
        byte[] key = sKey.getBytes();
        SecretKeySpec desKey = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, desKey);
        return cipher.doFinal(data);
    }

    public static void main(String[] args) throws Exception {
        String encryptResult = AESUtil.encrypt("00000001", Charset.forName("UTF-8"), DEFAULT_KEY);
        System.out.println(encryptResult);
        String decryResult = "";
        try {
            decryResult = AESUtil.decrypt(encryptResult, Charset.forName("UTF-8"), DEFAULT_KEY);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        System.out.println(decryResult);
        System.out.println(String.format("%08d", 1));
        CRC32 crc32 = new CRC32();
        crc32.update("00001".getBytes(Charset.forName("UTF-8")));
        System.out.println(crc32.getValue());
    }
}
