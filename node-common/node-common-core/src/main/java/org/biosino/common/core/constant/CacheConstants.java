package org.biosino.common.core.constant;

/**
 * 缓存常量信息
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 缓存有效期，默认720（分钟）
     */
    public final static long EXPIRATION = 720;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;

    /**
     * 密码最大错误次数
     */
    public final static int PASSWORD_MAX_RETRY_COUNT = 5;

    /**
     * 密码锁定时间，默认10（分钟）
     */
    public final static long PASSWORD_LOCK_TIME = 10;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 登录IP黑名单 cache key
     */
    public static final String SYS_LOGIN_BLACKIPLIST = SYS_CONFIG_KEY + "sys.login.blackIPList";


    /**
     * 特色数据集
     */
    public static final String SYS_FD_KEY = "sys_fd:";


    /**
     * 关联数据es同步时的key值
     */
    public static final String NODE_RELATED_ES_KEY = "node_related_es:";


    /**
     * 热门数据导出缓存
     */
    public static final String POPULAR_DATA_EXPORT_KEY = "popular_data_export:";

    /**
     * Biome的相关类型
     */
    public static final String HOST_BIOME = "host_biome";
    public static final String NON_HOST_BIOME = "non_host_biome";
    public static final String ENV_BIOME = "env_biome";
    public static final String ENV_BIOME_WATER = "env_biome_water";

    public static final String DISEASE = "disease_name";
    public static final String TAXONOMY = "Taxonomy";
    /**
     * 属性使用多个值时的分隔符
     */
    public static final String DELIMITER = "&";
}
