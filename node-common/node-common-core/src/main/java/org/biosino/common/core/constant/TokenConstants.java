package org.biosino.common.core.constant;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;

/**
 * Token的Key常量
 *
 * <AUTHOR>
 */
public class TokenConstants {
    /**
     * 令牌自定义标识
     */
    public static final String AUTHENTICATION = "Authorization";

    /**
     * 令牌前缀
     */
    public static final String PREFIX = "Bearer ";

    /**
     * 令牌秘钥
     */
    public final static String SECRET = "549f43dca4b4e00d4b366fc57f660554Z7jEOAMPMGawMfPqdwMj";


    public static void main(String[] args) {
        String secret = SecureUtil.md5(IdUtil.fastUUID()) + RandomUtil.randomString(20);
        // 生成token秘钥
//        System.out.println(secret);
    }

}
