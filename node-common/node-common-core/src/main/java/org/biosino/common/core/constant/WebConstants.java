package org.biosino.common.core.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
@Component
public class WebConstants {
    public static String WEB_DOMAIN = "http://local.biosino.org";

    public static String BASE_API = "/dev-api";

    public static String SUPPORT_EMAIL = "<EMAIL>";

    /**
     * 配置目录
     */
    @Value("${node.web.web-domain}")
    public void setDataHome(String webDomain) {
        WebConstants.WEB_DOMAIN = webDomain;
    }

    /**
     * 配置管理员邮箱
     */
    @Value("${node.support-email}")
    public void setSupportEmail(String email) {
        WebConstants.SUPPORT_EMAIL = email;
    }

    @Value("${node.web.base-api}")
    public void setSftpHome(String baseApi) {
        WebConstants.BASE_API = baseApi;
    }
}
