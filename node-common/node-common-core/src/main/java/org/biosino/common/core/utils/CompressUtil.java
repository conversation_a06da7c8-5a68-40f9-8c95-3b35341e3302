package org.biosino.common.core.utils;

import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.zip.Zip64Mode;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.apache.commons.compress.compressors.CompressorException;
import org.apache.commons.compress.compressors.CompressorInputStream;
import org.apache.commons.compress.compressors.CompressorStreamFactory;
import org.apache.commons.compress.compressors.bzip2.BZip2CompressorInputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.biosino.common.core.exception.ServiceException;

import java.io.*;
import java.nio.file.Files;
import java.util.Enumeration;
import java.util.zip.GZIPInputStream;

public class CompressUtil {

    public static boolean isSupported(String fileName) {
        String upperName = fileName.toUpperCase();
        return upperName.endsWith(".ZIP") || upperName.endsWith(".TAR") || upperName.endsWith(".TAR.BZ2") || upperName.endsWith(".BZ2") || upperName.endsWith(".TAR.GZ") || upperName.endsWith(".GZ") || upperName.endsWith(".XZ");
    }

    private static void unZip(File srcFile, File destDir) {
        OutputStream os = null;
        InputStream is = null;
        ZipFile zipFile = null;
        try {
            zipFile = new ZipFile(srcFile, "GBK");
            Enumeration<ZipArchiveEntry> entries = zipFile.getEntries();
            ZipArchiveEntry entry;
            while (entries.hasMoreElements()) {
                entry = entries.nextElement();
                File file = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    file.mkdirs();
                    continue;
                }
                is = zipFile.getInputStream(entry);
                os = Files.newOutputStream(file.toPath());
                IOUtils.copy(is, os);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                IOUtils.closeQuietly(is);
                IOUtils.closeQuietly(os);
                if (zipFile != null) {
                    zipFile.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static void unTar(InputStream inputStream, File destDir) {
        TarArchiveInputStream is = new TarArchiveInputStream(inputStream);
        OutputStream os = null;
        TarArchiveEntry entry;
        try {
            while ((entry = is.getNextTarEntry()) != null) {
                File file = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    file.mkdirs();
                    continue;
                }
                os = Files.newOutputStream(file.toPath());
                IOUtils.copy(is, os);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(os);
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(inputStream);
        }
    }

    private static void unTar(File srcFile, File destDir) throws FileNotFoundException {
        unTar(new BufferedInputStream(new FileInputStream(srcFile)), destDir);
    }

    private static void unTarBZIP2(File srcFile, File destDir) throws IOException {
        unTar(new BZip2CompressorInputStream(new FileInputStream(srcFile)), destDir);
    }

    private static void unTarGZ(File srcFile, File destDir) throws IOException {
        unTar(new GZIPInputStream(new FileInputStream(srcFile)), destDir);
    }

    private static void decompressByCompressor(String compressionAlgorithm, File srcFile, File destDir) throws IOException, CompressorException {
        InputStream is = null;
        OutputStream os = null;
        CompressorInputStream in = null;
        try {
            is = new FileInputStream(srcFile);
            in = new CompressorStreamFactory().createCompressorInputStream(compressionAlgorithm, is);
            File destFile = new File(destDir, FilenameUtils.getBaseName(srcFile.toString()));
            os = new BufferedOutputStream(new FileOutputStream(destFile));
            IOUtils.copy(in, os);
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(in);
            IOUtils.closeQuietly(os);
        }
    }


    public static void decompress(File srcFile, File destDir) throws ServiceException {
        decompress(srcFile, destDir, null);
    }

    public static void decompress(File srcFile, File destDir, String filename) throws ServiceException {
        if (!srcFile.isFile()) throw new ServiceException("FileNotFound");
        filename = StringUtils.isNotBlank(filename) ? filename : srcFile.getName();
        if (!isSupported(filename)) return;
        String upperName = filename.toUpperCase();
        try {
            if (upperName.endsWith(".ZIP")) {
                unZip(srcFile, destDir);
            } else if (upperName.endsWith(".TAR.BZ2")) {
                unTarBZIP2(srcFile, destDir);
            } else if (upperName.endsWith(".TAR.GZ")) {
                unTarGZ(srcFile, destDir);
            } else if (upperName.endsWith(".TAR")) {
                unTar(srcFile, destDir);
            } else if (upperName.endsWith(".BZ2")) {
                decompressByCompressor(CompressorStreamFactory.BZIP2, srcFile, destDir);
            } else if (upperName.endsWith(".GZ")) {
                decompressByCompressor(CompressorStreamFactory.GZIP, srcFile, destDir);
            } else if (upperName.equals(".XZ")) {
                decompressByCompressor(CompressorStreamFactory.XZ, srcFile, destDir);
            }
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("decompress failed");
        } catch (CompressorException e) {
            e.printStackTrace();
            throw new ServiceException("decompress failed");
        }
    }


    public static void zipFiles(File[] files, File zipFile) throws ServiceException {
        if (files == null || files.length == 0) return;
        if (zipFile == null) throw new ServiceException("文件为空");
        if (!zipFile.getParentFile().exists()) zipFile.getParentFile().mkdirs();
        ZipArchiveOutputStream zaos = null;
        try {
            zaos = new ZipArchiveOutputStream(zipFile);
            zaos.setUseZip64(Zip64Mode.AsNeeded);

            for (File file : files) {
                if (file == null) continue;
                ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(file, file.getName());
                zaos.putArchiveEntry(zipArchiveEntry);
                InputStream is = null;
                try {
                    is = new FileInputStream(file);
                    byte[] buffer = new byte[1024 * 5];
                    int len;
                    while ((len = is.read(buffer)) != -1) {
                        zaos.write(buffer, 0, len);
                    }
                    zaos.closeArchiveEntry();
                } finally {
                    if (is != null) is.close();
                }
            }
            zaos.finish();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            try {
                if (zaos != null) {
                    zaos.close();
                }
            } catch (IOException e) {
                throw new ServiceException(e.getMessage());
            }
        }
    }

    /**
     * 压缩文件或目录
     *
     * @param srcFile 文件或目录
     * @param zipFile
     * @throws IOException
     * @throws ServiceException
     */
    public static void zip(File srcFile, File zipFile) throws IOException, ServiceException {
        if (srcFile == null || !srcFile.exists()) throw new IllegalArgumentException("请指定要压缩的文件");
        if (!zipFile.getParentFile().exists()) zipFile.getParentFile().mkdirs();
        if (!srcFile.isDirectory()) {
            zipFiles(new File[]{srcFile}, zipFile);
            return;
        }
        ZipArchiveOutputStream out = null;
        try {
            out = new ZipArchiveOutputStream(new BufferedOutputStream(Files.newOutputStream(zipFile.toPath())));
            out.setUseZip64(Zip64Mode.AsNeeded);
            packFiles(out, srcFile, "");
        } finally {
            if (out != null) {
                out.closeArchiveEntry();
                IOUtils.closeQuietly(out);
            }
        }
    }

    private static void packFiles(ZipArchiveOutputStream out, File dir, String pathName) throws IOException {
        File[] files = dir.listFiles();
        if (files == null || files.length < 1) {
            return;
        }
        for (File zFile : files) {
            out.putArchiveEntry(new ZipArchiveEntry(zFile, pathName + zFile.getName()));
            if (zFile.isDirectory()) {
                packFiles(out, zFile, pathName + zFile.getName() + "/");
            } else {
                InputStream is = null;
                try {
                    is = new BufferedInputStream(Files.newInputStream(zFile.toPath()));
                    IOUtils.copy(is, out);
                } finally {
                    IOUtils.closeQuietly(is);
                }
            }
        }
    }

    public static void main(String[] args) {
        try {
            //            decompress(new File("E:\\test\\testzip.zip"), new File("E:\\test\\解压目录"));
            //            zipFiles(new File[]{new File("D:\\gtris\\report\\20170425\\111.pdf"), new File("D:\\gtris\\report\\20170425\\222.pdf")}, new File("D:\\gtris\\report\\20170425\\rs.zip"));
            zip(new File("D:\\gtris\\report\\20170426\\1493199147332.pdf"), new File("D:\\gtris\\report\\20170426\\rs.zip"));
            //              zip(new File("D:\\gtris\\report"), new File("D:\\gtris\\rs.zip"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
