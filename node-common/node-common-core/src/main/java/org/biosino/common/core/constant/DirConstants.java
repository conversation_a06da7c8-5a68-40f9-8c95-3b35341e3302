package org.biosino.common.core.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/12/29
 */
@Component
public class DirConstants {
    public static String DATA_HOME = "D:/node-data";
    public static String SFTP_HOME = "D:/node-sftp";
    public static String FASTQC_HOME = "D:/test/node/fastqc";

    /**
     * 配置目录
     */
    @Value("${node.data-home}")
    public void setDataHome(String dataHome) {
        DirConstants.DATA_HOME = dataHome;
    }

    @Value("${node.sftp-home}")
    public void setSftpHome(String sftpHome) {
        DirConstants.SFTP_HOME = sftpHome;
    }

    @Value("${node.fastqc-home}")
    public void setFastqcHome(String fastqcHome) {
        DirConstants.FASTQC_HOME = fastqcHome;
    }
}
