package org.biosino.common.core.utils;

import cn.hutool.core.thread.ThreadUtil;

import java.util.concurrent.ExecutorService;

/**
 * 公共线程池
 *
 * <AUTHOR>
 */
public class ThreadPoolUtil {
    private static ExecutorService executorService;

    public static synchronized ExecutorService executor() {
        if (executorService == null) {
            executorService = ThreadUtil.newExecutor(8, 16, 20000);
            return executorService;
        }
        return executorService;
    }
}
