package org.biosino.common.core.domain;


import com.maxmind.geoip2.record.Subdivision;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * select结构实体类
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IpAddr implements Serializable {
    private static final long serialVersionUID = 1L;

    // 国家
    private String country;
    // 国家（中文）
    private String countryCn;

    // 省份
    private String subdivision;
    private String subdivisionCn;

    // 城市
    private String city;
    private String cityCn;

    // 经度
    private Double longitude;
    // 纬度
    private Double latitude;

}
