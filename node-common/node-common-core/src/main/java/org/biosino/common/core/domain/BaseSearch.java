package org.biosino.common.core.domain;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.Set;

@Data
public class BaseSearch implements Serializable {
    public static final int MAX_PAGE_SIZE = 5000;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 排序关键字
     */
    private String sortKey;
    /**
     * 排序方式（asc、desc）
     */
    private String sortType;

    /**
     * 当前页(从1开始)
     */
    private int pageNum = 1;
    /**
     * 每页的数量
     */
    private int pageSize = 10;

    /**
     * 是否限制每页的大小的上限
     */
    private boolean pageLimit = true;

    public void setCreator(String creator) {
        this.creator = StrUtil.trimToNull(creator);
    }

    public void setSortKey(String sortKey) {
        this.sortKey = StrUtil.trimToNull(sortKey);
    }

    public void setSortType(String sortType) {
        this.sortType = StrUtil.trimToNull(sortType);
    }


    public void setPageSize(int pageSize) {
        this.pageSize = this.pageLimit ? Math.min(pageSize, MAX_PAGE_SIZE) : pageSize;
    }

    /**
     * 不限制每页的大小的上限
     */
    public void noLimitPageSize(int pageSize) {
        this.setPageLimit(false);
        this.setPageSize(pageSize);
    }

    public Pageable initPageInfo() {
        final Set<Sort.Order> orders = new LinkedHashSet<>();
        Sort.Direction direction;
        String sortType = this.getSortType();
        final String sortKey = this.getSortKey();
        if (StringUtils.isNotBlank(sortType) && StringUtils.isNotBlank(sortKey)) {
            sortType = sortType.toLowerCase();
            switch (sortType) {
                case "asc":
                    direction = Sort.Direction.ASC;
                    break;
                case "desc":
                    direction = Sort.Direction.DESC;
                    break;
                default:
                    direction = null;
                    break;
            }
            if (direction != null) {
                orders.add(new Sort.Order(direction, sortKey));
            }
        }
        // 前端element-plus起始页码从1开始，由于spring分页从0开始（page – zero-based page index, must not be negative.），此处需要减1
        if (orders.isEmpty()) {
            return PageRequest.of(this.getPageNum() - 1, this.getPageSize());
        } else {
            return PageRequest.of(this.getPageNum() - 1, this.getPageSize(), Sort.by(new ArrayList<>(orders)));
        }
    }

}
