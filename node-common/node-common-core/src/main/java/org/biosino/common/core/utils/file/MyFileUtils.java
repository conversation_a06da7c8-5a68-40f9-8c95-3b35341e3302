package org.biosino.common.core.utils.file;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.biosino.common.core.constant.Constants;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.enums.DirectoryEnum;
import org.biosino.common.core.utils.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * 文件处理工具类
 *
 * <AUTHOR>
 */
public class MyFileUtils {
    /**
     * 字符常量：斜杠 {@code '/'}
     */
    public static final char SLASH = '/';

    /**
     * 字符常量：反斜杠 {@code '\\'}
     */
    public static final char BACKSLASH = '\\';

    public static String FILENAME_PATTERN = "[a-zA-Z0-9_\\-\\|\\.\\u4e00-\\u9fa5]+";

    public static String FILEPATH_PATTERN = "^[a-zA-Z0-9_+\\-\\.\\\\/]+$";

    /**
     * 输出指定文件的byte数组
     *
     * @param filePath 文件路径
     * @param os       输出流
     * @return
     */
    public static void writeBytes(String filePath, OutputStream os) throws IOException {
        FileInputStream fis = null;
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                throw new FileNotFoundException(filePath);
            }
            fis = new FileInputStream(file);
            byte[] b = new byte[1024];
            int length;
            while ((length = fis.read(b)) > 0) {
                os.write(b, 0, length);
            }
        } catch (IOException e) {
            throw e;
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 删除文件
     *
     * @param filePath 文件
     * @return
     */
    public static boolean deleteFile(String filePath) {
        boolean flag = false;
        File file = new File(filePath);
        // 路径为文件且不为空则进行删除
        if (file.isFile() && file.exists()) {
            flag = file.delete();
        }
        return flag;
    }

    /**
     * 文件名称验证
     *
     * @param filename 文件名称
     * @return true 正常 false 非法
     */
    public static boolean isValidFilename(String filename) {
        return filename.matches(FILENAME_PATTERN);
    }

    /**
     * 文件路径校验
     */
    public static boolean isValidFilepath(String filepath) {
        return filepath.matches(FILEPATH_PATTERN);
    }

    /**
     * 检查文件是否可下载
     *
     * @param resource 需要下载的文件
     * @return true 正常 false 非法
     */
    public static boolean checkAllowDownload(String resource) {
        // 禁止目录上跳级别
        if (StringUtils.contains(resource, "..")) {
            return false;
        }
        // 判断是否在允许下载的文件规则内
        return ArrayUtils.contains(MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION, FileTypeUtils.getFileType(resource));
    }

    /**
     * 下载文件名重新编码
     *
     * @param request  请求对象
     * @param fileName 文件名
     * @return 编码后的文件名
     */
    public static String setFileDownloadHeader(HttpServletRequest request, String fileName) throws UnsupportedEncodingException {
        final String agent = request.getHeader("USER-AGENT");
        String filename = fileName;
        if (agent.contains("MSIE")) {
            // IE浏览器
            filename = URLEncoder.encode(filename, "utf-8");
            filename = filename.replace("+", " ");
        } else if (agent.contains("Firefox")) {
            // 火狐浏览器
            filename = new String(fileName.getBytes(), "ISO8859-1");
        } else if (agent.contains("Chrome")) {
            // google浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        } else {
            // 其它浏览器
            filename = URLEncoder.encode(filename, "utf-8");
        }
        return filename;
    }

    /**
     * 返回文件名
     *
     * @param filePath 文件
     * @return 文件名
     */
    public static String getName(String filePath) {
        if (null == filePath) {
            return null;
        }
        int len = filePath.length();
        if (0 == len) {
            return filePath;
        }
        if (isFileSeparator(filePath.charAt(len - 1))) {
            // 以分隔符结尾的去掉结尾分隔符
            len--;
        }

        int begin = 0;
        char c;
        for (int i = len - 1; i > -1; i--) {
            c = filePath.charAt(i);
            if (isFileSeparator(c)) {
                // 查找最后一个路径分隔符（/或者\）
                begin = i + 1;
                break;
            }
        }

        return filePath.substring(begin, len);
    }

    /**
     * 是否为Windows或者Linux（Unix）文件分隔符<br>
     * Windows平台下分隔符为\，Linux（Unix）为/
     *
     * @param c 字符
     * @return 是否为Windows或者Linux（Unix）文件分隔符
     */
    public static boolean isFileSeparator(char c) {
        return SLASH == c || BACKSLASH == c;
    }

    /**
     * 下载文件名重新编码
     *
     * @param response     响应对象
     * @param realFileName 真实文件名
     * @return
     */
    public static void setAttachmentResponseHeader(HttpServletResponse response, String realFileName) throws UnsupportedEncodingException {
        String percentEncodedFileName = percentEncode(realFileName);

        StringBuilder contentDispositionValue = new StringBuilder();
        contentDispositionValue.append("attachment; filename=")
                .append(percentEncodedFileName)
                .append(";")
                .append("filename*=")
                .append("utf-8''")
                .append(percentEncodedFileName);

        response.setHeader("Content-disposition", contentDispositionValue.toString());
        response.setHeader("download-filename", percentEncodedFileName);
    }

    /**
     * 百分号编码工具方法
     *
     * @param s 需要百分号编码的字符串
     * @return 百分号编码后的字符串
     */
    public static String percentEncode(String s) throws UnsupportedEncodingException {
        String encode = URLEncoder.encode(s, StandardCharsets.UTF_8.toString());
        return encode.replaceAll("\\+", "%20");
    }

    public static File multipartFileToFile(MultipartFile file) throws IOException {
        File toFile = null;
        if (StrUtil.isBlank(file.getOriginalFilename()) || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            try {
                ins = file.getInputStream();
                final String originalFilename = file.getOriginalFilename();
                final Path tempFile = Files.createTempFile(FileUtil.mainName(originalFilename), "." + FileUtil.extName(originalFilename));
                toFile = tempFile.toFile();
                FileUtil.writeFromStream(ins, toFile);
            } finally {
                IoUtil.close(ins);
            }
        }
        return toFile;

    }

    public static final String changeToLinuxSeparator(String path) {
        if (path == null) {
            return path;
        }
        return path.replace("\\", "/");
    }

    /**
     * 将文件的文件的绝对路径转为相对路径
     * 例如 /data/node/data/a.txt 去掉 /data/node 的结果 data/a.txt
     *
     * @param path          文件路径
     * @param replaceTarget 被截取掉的
     * @return 开始不会是斜线开头
     */
    public static String absoluteToRelativePath(String path, String replaceTarget) {
        String linuxPath = FilenameUtils.separatorsToUnix(path);
        String linuxReplaceTarget = FilenameUtils.separatorsToUnix(replaceTarget);
        String result = linuxPath.replace(linuxReplaceTarget, "");
        if (result.startsWith("/")) {
            return result.substring(1);
        }
        return result;
    }

    /**
     * 获取实验(样本)模板文件
     */
    public static File expSampleTemplate(final String dataHome, final String fileName) {
        final String dir = dataHome + File.separator + DirectoryEnum.documents.name();
        File file = new File(dir, "download" + File.separator + fileName);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        return file;
    }

    /**
     * 是否为excel文件
     */
    public static boolean isExcel(final String contentType, final String fileName) {
        if (!StrUtil.equalsAny(contentType, true, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-excel")) {
            return false;
        }
        final String extName = FileUtil.extName(fileName);
        if (!StrUtil.equalsAny(extName, true, "xls", "xlsx")) {
            return false;
        }
        return true;
    }

    public static File getTempDir() {
        long currentTime = System.currentTimeMillis();
        return FileUtil.file(DirConstants.DATA_HOME, DirectoryEnum.temp.name(), DateFormatUtils.format(new Date(), "yyyyMMdd"), String.valueOf(currentTime));
    }

    public static File getTempDirInDay() {
        return FileUtil.file(DirConstants.DATA_HOME, DirectoryEnum.temp.name(), DateFormatUtils.format(new Date(), "yyyyMMdd"));
    }


    /**
     * OER444631 ==> /OER44/OER4446/OER444631
     */
    public static String noToFtpFilePath(String no) {
        String regex = "^([A-Za-z]+)(\\d+)$";
        Pattern pattern = Pattern.compile(regex);
        java.util.regex.Matcher match = pattern.matcher(no);

        if (!match.find()) {
            throw new IllegalArgumentException("Input string does not match the expected format.");
        }

        String letterPart = match.group(1);
        String numberPart = match.group(2);
        StringBuilder result = new StringBuilder();

        for (int i = 2; i <= numberPart.length(); i += 2) {
            result.append('/').append(letterPart).append(numberPart.substring(0, i));
        }

        // 处理剩余的数字位
        if (numberPart.length() % 2 != 0) {
            result.append('/').append(letterPart).append(numberPart);
        }

        return result.toString();
    }

    /**
     * 获取新的存放data的位置
     */
    public static synchronized String getNewDataPath(Date date, String extension) {
        String relativePath = DirectoryEnum.data.name() + File.separator
                + DateUtil.format(date, "yyyyMMdd") + File.separator
                + System.currentTimeMillis() + extension;
        return FileUtil.exist(FileUtil.file(DirConstants.DATA_HOME, relativePath)) ? getNewDataPath(date, extension) : relativePath;
    }

    /**
     * 获取文件后缀名不带"."
     */
    public static String getFileNameSuffix(String fileName) {
        for (String suffix : Constants.FASTQ_SUFFIX) {
            if (fileName.endsWith(suffix)) {
                return suffix.substring(1);
            }
        }
        return FileNameUtil.getSuffix(fileName);
    }

    /**
     * 获取文件前缀
     */
    public static String getFileNamePrefix(String fileName) {
        String suffix = getFileNameSuffix(fileName);
        if (StrUtil.isBlank(suffix)) {
            return fileName;
        }
        return fileName.substring(0, fileName.length() - suffix.length() - 1);
    }
}
