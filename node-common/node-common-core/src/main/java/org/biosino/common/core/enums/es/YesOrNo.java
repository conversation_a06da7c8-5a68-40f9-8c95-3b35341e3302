package org.biosino.common.core.enums.es;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum YesOrNo {
    Yes(true, "是"),
    No(false, "否");

    private final boolean val;
    private final String title;

    YesOrNo(boolean val, String title) {
        this.val = val;
        this.title = title;
    }

    public static String findNameByBool(boolean val) {
        YesOrNo[] values = YesOrNo.values();
        for (YesOrNo value : values) {
            if (value.val == val) {
                return value.name();
            }
        }
        return null;
    }
}
