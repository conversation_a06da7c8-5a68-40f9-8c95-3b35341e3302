package org.biosino.common.core.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum SampleSubjectTypeEnum {
    Human("Human"),
    Animalia("Animalia"),
    Plantae("Plantae"),
    PathogenAffectingPublicHealth("Pathogen affecting public health"),
    CellLine("Cell line"),
    EnvironmentHost("Environment host"),
    EnvironmentNonHost("Environment non-host"),
    Microbe("Microbe"),
    Other("Other");

    private final String desc;

    SampleSubjectTypeEnum(String desc) {
        this.desc = desc;
    }

}
