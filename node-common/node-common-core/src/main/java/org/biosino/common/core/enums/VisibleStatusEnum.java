package org.biosino.common.core.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public enum VisibleStatusEnum {
    /**
     * 在页面上显示，可检索
     */
    Accessible,
    /**
     * 在页面上不显示
     */
    Unaccessible,
    /**
     * 已经删除
     */
    Deleted;

    public static List<String> includeAllVisibleStatus() {
        List<String> list = new ArrayList<>();
        list.add(VisibleStatusEnum.Accessible.name());
        list.add(VisibleStatusEnum.Unaccessible.name());
        list.add(VisibleStatusEnum.Deleted.name());
        return list;
    }

    public static List<String> includeExistsVisibleStatus() {
        List<String> list = new ArrayList<>();
        list.add(VisibleStatusEnum.Accessible.name());
        list.add(VisibleStatusEnum.Unaccessible.name());
        return list;
    }
}
