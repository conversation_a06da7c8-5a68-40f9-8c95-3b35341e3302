package org.biosino.common.core.enums.sys;

import java.util.Optional;

/**
 * 实验（样品）类型枚举
 *
 * <AUTHOR>
 */
public enum ExpSampleTypeEnum {
    experiment,
    sample;

    public static Optional<ExpSampleTypeEnum> findByName(final String name) {
        if (name == null) {
            return Optional.empty();
        }
        for (ExpSampleTypeEnum e : values()) {
            if (e.name().equals(name)) {
                return Optional.of(e);
            }
        }
        return Optional.empty();
    }

}
