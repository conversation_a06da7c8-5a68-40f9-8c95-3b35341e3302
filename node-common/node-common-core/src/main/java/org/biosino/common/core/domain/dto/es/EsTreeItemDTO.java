package org.biosino.common.core.domain.dto.es;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class EsTreeItemDTO implements Serializable {

    private String id;
    // 树节点展示名称
    private String name;
    // es字段名称
    private String fieldName;
    private Integer number;
    // 子节点数据
    private List<EsTreeItemDTO> data;

    private List<String> defaultCheckedKeys = new ArrayList<>();
    private List<String> defaultExpandedKeys = new ArrayList<>();

    // 是否展开
    private Boolean isExpand;
    // 是否显示
    private Boolean isShow;
    // 是否首字母大写
    private boolean upperFirst = false;

    public EsTreeItemDTO(String id, String name) {
        this(id, name, false);
    }

    public EsTreeItemDTO(String id, String name, boolean upperFirst) {
        this.id = id;

        this.name = name;
        this.fieldName = name;

        this.isExpand = false;
        this.isShow = true;
        this.upperFirst = upperFirst;
    }

}
