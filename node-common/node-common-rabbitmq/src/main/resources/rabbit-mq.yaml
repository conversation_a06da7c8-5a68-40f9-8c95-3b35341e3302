rabbit:
  # 交换机
  exchanges:
    - name: node
      type: DIRECT
    # 延时交换机
    - name: node_delay
      type: CUSTOM
      customType: x-delayed-message
      arguments:
        x-delayed-type: direct

  # 队列
  queues:
    # 同步Project和Analysis数据索引
    - name: es_index_queue
      routing-key: es_index_key
      exchange-name: node_delay
    # 更新编辑后的信息数据索引队列
    - name: es_index_update_queue
      routing-key: es_index_update_key
      exchange-name: node_delay
    # 删除数据索引
    - name: es_index_delete_queue
      routing-key: es_index_delete_key
      exchange-name: node_delay
    - name: analysis_task_queue
      routing-key: analysis_task_key
      exchange-name: node_delay
    - name: analysis_task_result_queue
      routing-key: analysis_task_result_key
      exchange-name: node_delay
    # md5校验任务创建队列
    - name: verification_create_queue
      routing-key: verification_create_key
      exchange-name: node_delay
    # md5校验任务后端消息队列
    - name: verification_delay_queue
      routing-key: verification_delay_key
      exchange-name: node_delay
    # md5校验任务结果队列
    - name: verification_result_queue
      routing-key: verification_result_key
      exchange-name: node_delay
    # 业务任务队列
    - name: business_task_queue
      routing-key: business_task_key
      exchange-name: node_delay
    # fastqc任务创建队列
    - name: fastqc_task_create_queue
      routing-key: fastqc_task_create_routing_key
      exchange-name: node_delay
    # fastqc任务开始队列(优先队列)，处理node1.0数据
    - name: fastqc_task_start_queue
      routing-key: fastqc_task_start_routing_key
      exchange-name: node_delay
      args:
        x-max-priority: 10
    # fastqc任务开始队列,高优先级，处理node2.0提交的数据
    - name: fastqc_hp_task_start_queue
      routing-key: fastqc_hp_task_start_routing_key
      exchange-name: node_delay
    - name: fastqc_task_status_queue
      routing-key: fastqc_task_status_routing_key
      exchange-name: node_delay
    #=============================================
    # samtool任务创建队列
    - name: samtool_task_create_queue
      routing-key: samtool_task_create_routing_key
      exchange-name: node_delay
    # fsamtool任务开始队列(优先队列)，处理node1.0数据
    - name: samtool_task_start_queue
      routing-key: samtool_task_start_routing_key
      exchange-name: node_delay
      args:
        x-max-priority: 10
    # samtool任务开始队列,高优先级，处理node2.0提交的数据
    - name: samtool_hp_task_start_queue
      routing-key: samtool_hp_task_start_routing_key
      exchange-name: node_delay
    - name: samtool_task_status_queue
      routing-key: samtool_task_status_routing_key
      exchange-name: node_delay
    # ============================================
    # vipmap同步任务队列
    - name: vipmap_sync_task_queue
      routing-key: vipmap_sync_task_routing_key
      exchange-name: node_delay
