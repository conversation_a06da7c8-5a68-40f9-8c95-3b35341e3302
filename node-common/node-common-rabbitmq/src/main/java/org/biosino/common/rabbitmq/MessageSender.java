package org.biosino.common.rabbitmq;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> @date 2023/12/29
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class MessageSender {

    private final RabbitTemplate rabbitTemplate;

    public void sendDelayMsg(String routingKey, Object msg) {
        log.info("发送消息到routingKey：{}，消息内容为：{}", routingKey, JSON.toJSONString(msg));
        rabbitTemplate.convertAndSend("node_delay", routingKey, msg, message -> {
            message.getMessageProperties().setDelay(3 * 1000);
            return message;
        });
    }


    public void sendMsg(String routingKey, Object msg) {
        log.info("发送消息到routingKey：{}，消息内容为：{}", routingKey, JSON.toJSONString(msg));
        rabbitTemplate.convertAndSend("node_delay", routingKey, msg);
    }

    public void sendPriorityMsg(String routingKey, Object msg, int priority) {
        log.info("发送消息到routingKey：{}，消息内容为：{}", routingKey, JSON.toJSONString(msg));
        rabbitTemplate.convertAndSend("node_delay", routingKey, msg, message -> {
            message.getMessageProperties().setPriority(priority);
            return message;
        });
    }

    public void sendDelayPriorityMsg(String routingKey, Object msg, int priority) {
        log.info("发送消息到routingKey：{}，消息内容为：{}", routingKey, JSON.toJSONString(msg));
        rabbitTemplate.convertAndSend("node_delay", routingKey, msg, message -> {
            message.getMessageProperties().setDelay(3 * 1000);
            message.getMessageProperties().setPriority(priority);
            return message;
        });
    }
}
