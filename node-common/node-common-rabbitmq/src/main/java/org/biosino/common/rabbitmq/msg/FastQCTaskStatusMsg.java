package org.biosino.common.rabbitmq.msg;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Data
public class FastQCTaskStatusMsg {

    @JsonProperty("data_no")
    private String dataNo;

    private String status;

    @JsonProperty("fail_cause")
    private String failCause;

    @JsonProperty("error_log_path")
    private String errorLogPath;

    private ResultData data;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ResultData {

        @JsonProperty("fastqc_html_file_path")
        private String fastqcHtmlFilepath;

        @JsonProperty("fastqc_report_file_path")
        private String fastqcReportFilepath;

        @JsonProperty("fastqc_zip_file_path")
        private String fastqcZipFilepath;

        @JsonProperty("fastqc_version")
        private String fastqcVersion;

        @JsonProperty("seqkit_file_path")
        private String seqkitFilepath;

        @JsonProperty("seqkit_version")
        private String seqkitVersion;
    }
}
