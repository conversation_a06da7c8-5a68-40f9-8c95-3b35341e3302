package org.biosino.common.rabbitmq.constant;

/**
 * <AUTHOR>
 * @date 2024/7/12
 */
public interface RoutingKeyConstant {

    String es_index_key = "es_index_key";

    String es_index_update_key = "es_index_update_key";

    String es_index_delete_key = "es_index_delete_key";

    String analysis_task_key = "analysis_task_key";

    String analysis_task_result_key = "analysis_task_result_key";

    String verification_create_key = "verification_create_key";

    String verification_delay_key = "verification_delay_key";

    String verification_result_key = "verification_result_key";

    String business_task_key = "business_task_key";

    String fastqc_task_create_routing_key = "fastqc_task_create_routing_key";

    String fastqc_task_start_routing_key = "fastqc_task_start_routing_key";

    String fastqc_task_status_routing_key = "fastqc_task_status_routing_key";

    String vipmap_sync_task_routing_key = "vipmap_sync_task_routing_key";
}
