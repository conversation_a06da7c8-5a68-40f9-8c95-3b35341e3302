package org.biosino.auth.cas;

import cn.hutool.core.thread.ThreadUtil;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.IpAddr;
import org.biosino.common.core.utils.ServletUtils;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.core.utils.uuid.IdUtils;
import org.biosino.system.api.RemoteLogService;
import org.biosino.system.api.domain.MemberLoginInfo;
import org.biosino.system.api.model.CustomUserDetails;
import org.biosino.system.api.model.Member;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;

@Component
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class SuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

    private CasProperties casProperties;


    private RemoteLogService remoteLogService;


    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
        Member member = userDetails.getMember();
        MemberLoginInfo loginInfo = new MemberLoginInfo();
        loginInfo.setId(IdUtils.getShortUUID());
        loginInfo.setUserId(member.getId());
        loginInfo.setUserEmail(member.getEmail());
        loginInfo.setBrowser(ServletUtils.getHeader(request, "User-Agent"));
        loginInfo.setIp(getVisitorIp(request));
        IpAddr ipAddr = IpUtils.getIpCountry(loginInfo.getIp());
        if (ipAddr != null) {
            loginInfo.setCountry(ipAddr.getCountry());
        }
        loginInfo.setLoginTime(new Date());
        ThreadUtil.execute(() -> remoteLogService.saveCasLoginLog(loginInfo, SecurityConstants.INNER));
        response.sendRedirect(casProperties.getAppClientUrl());
    }

    /**
     * 获取访问者IP,特别特别注意，cas 回调的本服务login接口，如果代理了下面的nginx，一定 不能 加以下的nginx配置，否则会导致访问者IP获取不到
     * proxy_set_header Host $http_host;
     * proxy_set_header X-Real-IP $remote_addr;
     * proxy_set_header REMOTE-HOST $remote_addr;
     * proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
     */
    private String getVisitorIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Real-IP");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            int index = ip.indexOf(',');
            if (index != -1) {
                return ip.substring(0, index);
            }
        } else {
            return request.getRemoteAddr();
        }
        return ip;
    }

}
