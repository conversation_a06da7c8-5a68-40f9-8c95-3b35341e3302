package org.biosino.auth.controller;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.auth.cas.CasProperties;
import org.biosino.auth.form.LoginBody;
import org.biosino.auth.form.RegisterBody;
import org.biosino.auth.service.SysLoginService;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.JwtUtils;
import org.biosino.common.core.utils.SecurityUtils;
import org.biosino.common.core.utils.StringUtils;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.security.auth.AuthUtil;
import org.biosino.common.security.service.TokenService;
import org.biosino.system.api.model.CustomUserDetails;
import org.biosino.system.api.model.LoginUser;
import org.biosino.system.api.model.Member;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class TokenController {
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private CasProperties casProperties;

    /**
     * cas登出默认请求网站根路径，需要重定向到前台网页地址
     */
    @CrossOrigin
    @RequestMapping("/")
    public void index(HttpServletResponse response) throws IOException {
        log.debug("sendRedirect==>{}", casProperties.getAppClientUrl());
        response.sendRedirect(casProperties.getAppClientUrl());
    }

    /**
     * cas登录成功后，返回token
     */
    @GetMapping("/casLogin")
    public AjaxResult casLogin() {
        CustomUserDetails customUser = (CustomUserDetails) SecurityUtils.getLoginUser();
        AjaxResult ajax = AjaxResult.success();
        LoginUser loginUser = new LoginUser();
        loginUser.setMember(customUser.getMember());
        Map<String, Object> tokenMap = tokenService.createToken(loginUser);
        // username是账号
        ajax.put("username", customUser.getUsername());
        // name是用户名
        ajax.put("name", customUser.getName());
        ajax.put("member", customUser.getMember());
        ajax.put("userId", customUser.getUserId());
        ajax.put("token", tokenMap.get("access_token"));
        return ajax;
    }

    @GetMapping("/isLogin")
    public R<?> isLogin() {
        Object loginUser = SecurityUtils.getLoginUser();
        return R.ok(!"anonymousUser".equals(loginUser));
    }

    /**
     * 管理和人工审核平台登录
     */
    @PostMapping("login2")
    public R<?> login2(@RequestBody LoginBody form) {
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form);
        // 获取登录token
        return R.ok(tokenService.createToken2(userInfo));
    }


    /**
     * cas登出成功前先删除token
     */
    @DeleteMapping("/tokenLogout")
    public R<?> tokenLogout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotNull(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username);
        }
        return R.ok();
    }

    @DeleteMapping("logout2")
    public R<?> logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<?> refresh(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<?> register(@RequestBody RegisterBody registerBody) {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }

    @RequestMapping("/createAccessToken")
    public R createAccessToken(String memberId) {
        if (StrUtil.isBlank(memberId)) {
            throw new ServiceException("memberId 不能为空");
        }
        LoginUser loginUser = new LoginUser();
        String tokenKey = "temp_" + memberId;

        Member member = new Member();
        member.setId(memberId);

        loginUser.setToken(tokenKey);
        loginUser.setUserId(memberId);
        loginUser.setMember(member);

        tokenService.refreshToken(loginUser);

        Map<String, Object> claimsMap = new HashMap<String, Object>();
        claimsMap.put(SecurityConstants.USER_KEY, tokenKey);
        claimsMap.put(SecurityConstants.DETAILS_USER_ID, memberId);

        return R.ok(JwtUtils.createToken(claimsMap));
    }
}
