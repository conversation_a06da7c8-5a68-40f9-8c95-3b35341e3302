package org.biosino.auth.service;

import cn.hutool.core.util.StrUtil;
import org.biosino.auth.form.LoginBody;
import org.biosino.common.core.constant.CacheConstants;
import org.biosino.common.core.constant.Constants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.UserConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.UserStatus;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.text.Convert;
import org.biosino.common.core.utils.StringUtils;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.redis.service.RedisService;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.RemoteUserService;
import org.biosino.system.api.domain.SysUser;
import org.biosino.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysRecordLogService recordLogService;

    @Autowired
    private RedisService redisService;

    /**
     * 登录
     */
    public LoginUser login(LoginBody loginBody) {
        String username = loginBody.getUsername();
        String password = loginBody.getPassword();
        String system = loginBody.getSystem();

        if (StringUtils.isEmpty(system) && StrUtil.containsAny(system, Constants.SYSTEM_ADMIN, Constants.SYSTEM_QC)) {
            throw new ServiceException("Illegal system, login prohibited");
        }

        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "User password must be filled in");
            throw new ServiceException("User password must be filled in");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "The user password is not within the specified range");
            throw new ServiceException("The user password is not within the specified range");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "The username is not within the specified range");
            throw new ServiceException("The username is not within the specified range");
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "Unfortunately, accessing IP has been blacklisted in the system");
            throw new ServiceException("Unfortunately, accessing IP has been blacklisted in the system");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "Username does not exist");
            throw new ServiceException("Username：" + username + " does not exist");
        }

        if (R.FAIL == userResult.getCode()) {
            throw new ServiceException(userResult.getMsg());
        }

        LoginUser userInfo = userResult.getData();

        Set<String> roles = userInfo.getRoles();
        if (system.equals(Constants.SYSTEM_QC)) {
            if (!roles.contains(Constants.SYSTEM_QC)) {
                throw new ServiceException("Your account does not have permission to log in to the system");
            }
        } else {
            if (roles.contains(Constants.SYSTEM_QC)) {
                throw new ServiceException("Your account does not have permission to log in to the system");
            }
        }

        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "Sorry, your account has been deleted");
            throw new ServiceException("Sorry，your account：" + username + " has been deleted");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "The user has been disable. Please contact the administrator");
            throw new ServiceException("Sorry，your account：" + username + " has been disable");
        }

        passwordService.validate(user, password);
        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "Login successful");
        return userInfo;
    }

    public void logout(String loginName) {
        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "Logout successful");
    }

    /**
     * 注册
     */
    public void register(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ServiceException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode()) {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");
    }
}
