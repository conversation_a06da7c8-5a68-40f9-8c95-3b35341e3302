package org.biosino.auth.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.biosino.system.api.model.CustomUserDetails;
import org.biosino.system.api.model.Member;
import org.springframework.security.cas.authentication.CasAssertionAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.AuthenticationUserDetailsService;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 用于加载用户信息 实现UserDetailsService接口，或者实现AuthenticationUserDetailsService接口
 *
 * <AUTHOR>
@Slf4j
public class CustomUserDetailsService implements AuthenticationUserDetailsService<CasAssertionAuthenticationToken> {


    /**
     * 加载登录用户的信息
     *
     * @param token
     * @return
     * @throws UsernameNotFoundException
     */
    @Override
    public UserDetails loadUserDetails(CasAssertionAuthenticationToken token) throws UsernameNotFoundException {
        log.info("当前登录用户名是：" + token.getName());
        // 登录账户名
        String username = token.getName();
        // cas提供的登录信息
        Map<String, Object> attributes = token.getAssertion().getPrincipal().getAttributes();
        JSONObject jsonObject = new JSONObject(attributes);
        Member member = jsonObject.toJavaObject(Member.class);
        // 将密码置为空，避免泄露
        member.setPassword(CustomUserDetails.NON_EXISTENT_PASSWORD_VALUE);
        // 获取cas登录信息中用户id
        String userId = String.valueOf(attributes.getOrDefault("id", ""));
        // 获取cas登录信息中姓名
        // String name = String.valueOf(attributes.getOrDefault("name", ""));
        String lastName = String.valueOf(attributes.getOrDefault("lastName", ""));
        // 获取cas登录信息中用户状态
        String status = String.valueOf(attributes.getOrDefault("status", "enable"));
        boolean enabled = StrUtil.equals("enable", status);
        Set<GrantedAuthority> authorities = new HashSet<>();

        return new CustomUserDetails(username, CustomUserDetails.NON_EXISTENT_PASSWORD_VALUE,
                enabled,
                authorities,
                userId,
                lastName,
                member,
                status);
    }

}
