package org.biosino.upload.api.vo;

import lombok.Data;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/10
 */
@Data
public class DeleteCheckResultVO {
    private List<String> projNos;
    private List<String> expNos;
    private List<String> runNos;
    private List<String> sapNos;
    private List<String> dataNos;
    private List<String> analNos;
    private List<DeleteErrorMsgVO> errors;
    private Map<String, LinkedHashSet<String>> shareMap;
    private Map<String, LinkedHashSet<String>> reviewMap;
    private Map<String, LinkedHashSet<String>> resourceAuthorizeMap;
}
