package org.biosino.upload.api.factory;


import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.upload.api.RemoteUploadProjectService;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RemoteUploadProjectFallbackFactory implements FallbackFactory<RemoteUploadProjectService> {
    @Override
    public RemoteUploadProjectService create(Throwable throwable) {
        log.error("upload服务调用失败:{}", throwable.getMessage());
        return new RemoteUploadProjectService() {
            @Override
            public R<DeleteCheckResultVO> deleteCheck(String projectNo, String memberId) {
                return R.fail("deleteCheck调用失败:" + throwable.getMessage());
            }

            @Override
            public R deleteProjectAll(String projectNo, String memberId) {
                return R.fail("deleteProjectAll调用失败:" + throwable.getMessage());
            }
        };
    }
}
