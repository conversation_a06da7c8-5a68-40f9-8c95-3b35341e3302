package org.biosino.upload.api;


import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.upload.api.dto.RemoteSelectQueryDTO;
import org.biosino.upload.api.factory.RemoteUploadAnalysisFallbackFactory;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "remoteUploadAnalysisService", value = ServiceNameConstants.NODE_UPLOAD_SERVICE, fallbackFactory = RemoteUploadAnalysisFallbackFactory.class)
public interface RemoteUploadAnalysisService {

    /**
     * 删除预检查
     */
    @GetMapping("/metadata/analysis/deleteCheck")
    R<DeleteCheckResultVO> deleteCheck(@RequestParam("analysisNo") String analysisNo, @RequestParam("memberId") String memberId);

    /**
     * 删除全部的数据
     */
    @RequestMapping("/metadata/analysis/deleteAll")
    R deleteAll(@RequestParam("analysisNo") String analysisNo, @RequestParam("memberId") String memberId);

    @RequestMapping("/metadata/analysis/getTargetOptions")
    TableDataInfo getTargetOptions(@RequestBody RemoteSelectQueryDTO queryDTO);

    @RequestMapping("/metadata/analysis/getPipelineOptions")
    TableDataInfo getPipelineOptions(@RequestBody RemoteSelectQueryDTO queryDTO);

}
