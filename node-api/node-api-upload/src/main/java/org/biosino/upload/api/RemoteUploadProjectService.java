package org.biosino.upload.api;


import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.upload.api.factory.RemoteUploadProjectFallbackFactory;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "remoteUploadProjectService", value = ServiceNameConstants.NODE_UPLOAD_SERVICE, fallbackFactory = RemoteUploadProjectFallbackFactory.class)
public interface RemoteUploadProjectService {

    /**
     * 删除预检查
     */
    @GetMapping("/metadata/project/deleteCheck")
    R<DeleteCheckResultVO> deleteCheck(@RequestParam("projectNo") String projectNo, @RequestParam("memberId") String memberId);

    /**
     * 删除全部的数据
     */
    @RequestMapping("/metadata/project/deleteAll")
    R deleteProjectAll(@RequestParam("projectNo") String projectNo, @RequestParam("memberId") String memberId);
}
