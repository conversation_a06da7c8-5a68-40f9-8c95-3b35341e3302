package org.biosino.upload.api.factory;


import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.upload.api.RemoteUploadExperimentService;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RemoteUploadExperimentFallbackFactory implements FallbackFactory<RemoteUploadExperimentService> {
    @Override
    public RemoteUploadExperimentService create(Throwable throwable) {
        log.error("upload服务调用失败:{}", throwable.getMessage());
        return new RemoteUploadExperimentService() {
            @Override
            public R<DeleteCheckResultVO> deleteCheck(String projectNo, String memberId) {
                return R.fail("deleteCheck调用失败:" + throwable.getMessage());
            }

            @Override
            public R deleteAll(String projectNo, String memberId) {
                return R.fail("deleteAll调用失败:" + throwable.getMessage());
            }
        };
    }
}
