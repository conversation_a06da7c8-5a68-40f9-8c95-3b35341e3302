package org.biosino.upload.api.factory;

import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.upload.api.RemoteUploadSampleService;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> @date 2024/4/28
 */
@Component
@Slf4j
public class RemoteUploadSampleFallbackFactory implements FallbackFactory<RemoteUploadSampleService> {
    @Override
    public RemoteUploadSampleService create(Throwable throwable) {
        log.error("upload服务调用失败:{}", throwable.getMessage());
        return new RemoteUploadSampleService() {
            @Override
            public R<DeleteCheckResultVO> deleteCheck(String sapNo, String memberId) {
                return R.fail("deleteCheck调用失败:" + throwable.getMessage());
            }

            @Override
            public R deleteAll(String sapNo, String memberId) {
                return R.fail("deleteAll调用失败:" + throwable.getMessage());
            }
        };
    }

}
