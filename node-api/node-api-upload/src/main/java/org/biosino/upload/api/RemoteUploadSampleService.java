package org.biosino.upload.api;


import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.upload.api.factory.RemoteUploadProjectFallbackFactory;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "remoteUploadSampleService", value = ServiceNameConstants.NODE_UPLOAD_SERVICE, fallbackFactory = RemoteUploadProjectFallbackFactory.class)
public interface RemoteUploadSampleService {

    /**
     * 删除预检查
     */
    @GetMapping("/metadata/sample/deleteCheck")
    R<DeleteCheckResultVO> deleteCheck(@RequestParam("sapNo") String sapNo, @RequestParam("memberId") String memberId);

    /**
     * 删除全部的数据
     */
    @RequestMapping("/metadata/sample/deleteAll")
    R deleteAll(@RequestParam("sapNo") String sapNo, @RequestParam("memberId") String memberId);
}
