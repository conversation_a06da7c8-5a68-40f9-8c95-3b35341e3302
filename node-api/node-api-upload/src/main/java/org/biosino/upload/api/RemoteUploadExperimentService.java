package org.biosino.upload.api;


import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.upload.api.factory.RemoteUploadExperimentFallbackFactory;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "remoteUploadExperimentService", value = ServiceNameConstants.NODE_UPLOAD_SERVICE, fallbackFactory = RemoteUploadExperimentFallbackFactory.class)
public interface RemoteUploadExperimentService {

    /**
     * 删除预检查
     */
    @GetMapping("/metadata/experiment/deleteCheck")
    R<DeleteCheckResultVO> deleteCheck(@RequestParam("expNo") String expNo, @RequestParam("memberId") String memberId);

    /**
     * 删除全部的数据
     */
    @RequestMapping("/metadata/experiment/deleteAll")
    R deleteAll(@RequestParam("expNo") String expNo, @RequestParam("memberId") String memberId);
}
