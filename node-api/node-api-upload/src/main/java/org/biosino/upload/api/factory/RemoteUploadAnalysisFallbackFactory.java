package org.biosino.upload.api.factory;

import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.upload.api.RemoteUploadAnalysisService;
import org.biosino.upload.api.dto.RemoteSelectQueryDTO;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> <PERSON>
 * @date 2024/4/28
 */
@Component
@Slf4j
public class RemoteUploadAnalysisFallbackFactory implements FallbackFactory<RemoteUploadAnalysisService> {
    @Override
    public RemoteUploadAnalysisService create(Throwable throwable) {
        log.error("upload服务调用失败:{}", throwable.getMessage());
        return new RemoteUploadAnalysisService() {
            @Override
            public R<DeleteCheckResultVO> deleteCheck(String analNo, String memberId) {
                return R.fail("deleteCheck调用失败:" + throwable.getMessage());
            }

            @Override
            public R deleteAll(String analNo, String memberId) {
                return R.fail("deleteAll调用失败:" + throwable.getMessage());
            }

            @Override
            public TableDataInfo getTargetOptions(RemoteSelectQueryDTO queryDTO) {
                return new TableDataInfo();
            }

            @Override
            public TableDataInfo getPipelineOptions(RemoteSelectQueryDTO queryDTO) {
                return new TableDataInfo();
            }
        };
    }

}
