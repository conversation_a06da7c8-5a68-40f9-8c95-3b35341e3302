package org.biosino.upload.api.factory;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.upload.api.RemoteSubmissionService;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RemoteSubmissionFallbackFactory implements FallbackFactory<RemoteSubmissionService> {
    @Override
    public RemoteSubmissionService create(Throwable throwable) {

        log.error("upload服务调用失败:{}", throwable.getMessage());

        return new RemoteSubmissionService() {
            @Override
            public R pass(String subNo, Long auditorId, String auditorName, String source) {
                return R.fail(StrUtil.format("Submission审核通过失败, subNo: {}, Error Msg:{}", subNo, throwable.getMessage()));
            }
        };
    }
}
