package org.biosino.upload.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.upload.api.factory.RemoteSubmissionFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteSubmissionService", value = ServiceNameConstants.NODE_UPLOAD_SERVICE, fallbackFactory = RemoteSubmissionFallbackFactory.class)
public interface RemoteSubmissionService {

    /**
     * 审核通过
     */
    @PostMapping("/metadata/submission/pass/{subNo}/{auditorId}/{auditorName}")
    R pass(@PathVariable("subNo") String subNo, @PathVariable("auditorId") Long auditorId,
           @PathVariable("auditorName") String auditorName, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
