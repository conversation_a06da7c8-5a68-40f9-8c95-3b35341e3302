package org.biosino.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.common.core.annotation.Excel;
import org.biosino.common.core.annotation.Excel.ColumnType;
import org.biosino.common.core.web.domain.BaseEntity;

import java.util.Date;

/**
 * 操作日志记录表 oper_log
 *
 * <AUTHOR>
 */
@Data
public class SysOperLog extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 日志主键
     */
    @Excel(name = "操作序号", cellType = ColumnType.NUMERIC)
    private Long operId;

    /**
     * 系统
     */
    @Excel(name = "系统")
    public String system;

    @Excel(name = "一级模块")
    public String module1;

    @Excel(name = "二级模块")
    public String module2;

    @Excel(name = "三级模块")
    public String module3;

    @Excel(name = "描述信息")
    public String description;

    /**
     * 业务类型（0其它 1新增 2修改 3删除）
     */
    @Excel(name = "业务类型", readConverterExp = "0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据")
    private Integer businessType;

    /**
     * 请求方法
     */
    @Excel(name = "请求方法")
    private String method;

    /**
     * 请求方式
     */
    @Excel(name = "请求方式")
    private String requestMethod;

    /**
     * 操作人员ID
     */
    @Excel(name = "操作人员ID")
    private String operUserId;

    /**
     * 操作人员
     */
    @Excel(name = "操作人员")
    private String operName;

    /**
     * 请求URL
     */
    @Excel(name = "请求地址")
    private String operUrl;

    /**
     * 操作地址
     */
    @Excel(name = "操作地址")
    private String operIp;

    /**
     * 请求args参数
     */
    @Excel(name = "请求args参数")
    private String operArgs;

    /**
     * 请求Params参数
     */
    @Excel(name = "请求Params参数")
    private String operParam;

    /**
     * 返回参数
     */
    @Excel(name = "返回参数")
    private String jsonResult;

    /**
     * 操作状态（0正常 1异常）
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=异常")
    private Integer status;

    /**
     * 错误消息
     */
    @Excel(name = "错误消息")
    private String errorMsg;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operTime;

    /**
     * 消耗时间
     */
    @Excel(name = "消耗时间", suffix = "毫秒")
    private Long costTime;

}
