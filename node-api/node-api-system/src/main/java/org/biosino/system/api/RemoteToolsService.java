package org.biosino.system.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.domain.DbCheckLog;
import org.biosino.system.api.factory.RemoteToolsServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;
import java.util.Map;

@FeignClient(contextId = "remoteToolsService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteToolsServiceFallbackFactory.class)
public interface RemoteToolsService {
    /**
     * 开始检查整个数据库数据一致性
     */
    @GetMapping(value = "/dataIntegralityCheck/checkAllDB")
    public R checkAllDB(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 扫描SFTP目录任务
     */
    @GetMapping(value = "/metadata/data/ftpHomeFile/scanFtpHomeFile")
    public R scanFtpHomeFile(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 校验某些指定data的数据一致性
     */
    @GetMapping(value = "/dataIntegralityCheck/relatedDataCheck")
    public R<Map<String, List<DbCheckLog>>> relatedDataCheck(String[] dataNos, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
