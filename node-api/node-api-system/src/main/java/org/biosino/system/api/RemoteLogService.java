package org.biosino.system.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.domain.MemberLoginInfo;
import org.biosino.system.api.domain.SysLogininfor;
import org.biosino.system.api.domain.SysOperLog;
import org.biosino.system.api.factory.RemoteLogFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 日志服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteLogService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteLogFallbackFactory.class)
public interface RemoteLogService {
    /**
     * 保存系统日志
     *
     * @param sysOperLog 日志实体
     * @param source     请求来源
     * @return 结果
     */
    @PostMapping("/operlog")
    public R<Boolean> saveLog(@RequestBody SysOperLog sysOperLog, @RequestHeader(SecurityConstants.FROM_SOURCE) String source) throws Exception;

    /**
     * 保存访问记录
     *
     * @param sysLogininfor 访问实体
     * @param source        请求来源
     * @return 结果
     */
    @PostMapping("/logininfor")
    public R<Boolean> saveLogininfor(@RequestBody SysLogininfor sysLogininfor, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 保存前台登录日志
     *
     * @param memberLoginInfo 登录信息
     * @param source          请求来源
     * @return 结果
     */
    @PostMapping("/memberLoginInfo/add")
    public R<Boolean> saveCasLoginLog(@RequestBody MemberLoginInfo memberLoginInfo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
