package org.biosino.system.api.domain.sftp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * ftp_file_log表
 */
@Data
@TableName(value = "ftp_file_log")
public class FtpFileLog implements java.io.Serializable {

    @TableId(type = IdType.NONE)
    private String id;

    @TableField(value = "name")
    private String name;

    @TableField(value = "path")
    private String path;

    @TableField(value = "size")
    private Long size;

    @TableField(value = "creator")
    private String creator;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "status")
    private String status;

    // @TableField(value = "md5_file_status")
    // private String md5FileStatus;

    /**
     * 用户提交的md5文件路径
     */
    @TableField(value = "md5_file_path")
    private String md5FilePath;

    /**
     * 记录用户提交的MD5文件中的MD5值
     */
    @TableField(value = "md5_file_content", updateStrategy = FieldStrategy.IGNORED)
    private String md5FileContent;

    /**
     * 记录最近一次更新的MD5的值
     */
    @TableField(value = "md5")
    private String md5;

    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 记录最近一次更新MD5的时间
     */
    @TableField(value = "verify_time")
    private Date verifyTime;

    /**
     * 记录最近一次MD5校验失败的原因
     */
    @TableField(value = "fail_cause")
    private String failCause;

    /**
     * 系统错误原因
     */
    // @TableField(value = "err_msg")
    // private String errMsg;

    @TableField(exist = false)
    private String relativePath;

}
