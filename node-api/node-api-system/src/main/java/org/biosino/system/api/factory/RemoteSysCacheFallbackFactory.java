package org.biosino.system.api.factory;

import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.RemoteSysCacheService;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/2
 */
@Slf4j
@Component
public class RemoteSysCacheFallbackFactory implements FallbackFactory<RemoteSysCacheService> {

    @Override
    public RemoteSysCacheService create(Throwable throwable) {
        log.error("管理后台缓存处理接口调用失败:{}", throwable.getMessage());
        return new RemoteSysCacheService() {
            @Override
            public R<Boolean> refreshFdCache(String source) {
                return R.fail("特殊数据集刷新缓存调用失败:" + throwable.getMessage());
            }
        };
    }
}
