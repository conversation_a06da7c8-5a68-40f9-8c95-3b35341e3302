package org.biosino.system.api.factory;

import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.RemoteNotificationService;
import org.biosino.system.api.dto.EmailWithContentDTO;
import org.biosino.system.api.dto.ExpressInfoDTO;
import org.biosino.system.api.dto.SendEmailDTO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/1/2
 */
@Component
@Slf4j
public class RemoteNotificationFallbackFactory implements FallbackFactory<RemoteNotificationService> {
    @Override
    public RemoteNotificationService create(Throwable throwable) {
        throwable.printStackTrace();

        log.error("NODE邮件通知服务调用失败:{}", throwable.getMessage());

        return new RemoteNotificationService() {
            @Override
            public R sendExpressEmail(ExpressInfoDTO expressInfoDTO, String source) {
                return R.fail("sendExpressEmail调用失败:" + throwable.getMessage());
            }

            @Override
            public R sendEmail(SendEmailDTO dto, String source) {
                return R.fail("sendEmail调用失败:" + throwable.getMessage());
            }

            @Override
            public R sendEmailWithContent(EmailWithContentDTO dto, String source) {
                return R.fail("sendEmailWithContent调用失败:" + throwable.getMessage());
            }
        };
    }
}
