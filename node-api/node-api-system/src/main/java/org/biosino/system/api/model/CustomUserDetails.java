package org.biosino.system.api.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

/**
 * 前台用户信息
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@Getter
@Setter
public class CustomUserDetails extends User {
    private static final long serialVersionUID = -8830439733679545864L;
    public static final String NON_EXISTENT_PASSWORD_VALUE = "NO_PASSWORD";
    /**
     * 用户Id
     */
    private final String userId;
    /**
     * 用户名
     */
    private final String name;
    /**
     * bmdcRegister的用户信息
     */
    private final Member member;
    /**
     * 用户状态
     */
    private final String status;


    public CustomUserDetails(String username, String password, boolean enabled, Collection<? extends GrantedAuthority> authorities, String userId, String name, Member member, String status) {
        super(username, password, enabled, true, true, true, authorities);
        this.userId = userId;
        this.name = name;
        this.member = member;
        this.status = status;
    }

}
