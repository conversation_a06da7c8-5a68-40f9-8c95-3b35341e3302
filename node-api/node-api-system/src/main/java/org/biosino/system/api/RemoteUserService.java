package org.biosino.system.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.domain.SysUser;
import org.biosino.system.api.factory.RemoteUserFallbackFactory;
import org.biosino.system.api.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService {
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source   请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过用户ID获取用户名
     *
     * @param id     用户ID
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/getUsername/{id}")
    public R<SysUser> getUsername(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source  请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询sys_config表，根据参数键名查询参数值
     *
     * @param configKeys 参数键名集合，使用PathVariable传递，集合中的元素需要使用URLEncoder.encode编码，防止特殊字符传参出错
     * @param source
     * @return 返回map中key为参数键名，值为参数值
     */
    @PostMapping("/config/getConfigValuesByKeys/{configKeys}")
    public R<Map<String, String>> getConfigValuesByKeys(@PathVariable("configKeys") List<String> configKeys, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
