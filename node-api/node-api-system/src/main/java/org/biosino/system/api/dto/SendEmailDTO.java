package org.biosino.system.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.common.core.enums.MailTemplate;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendEmailDTO {
    @NotBlank
    private String toEmail;
    @NotNull
    private MailTemplate mailTemplate;
    private Map<String, Object> params;
}
