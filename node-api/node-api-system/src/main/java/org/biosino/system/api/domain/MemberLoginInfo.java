package org.biosino.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/16
 */
@Data
@TableName("member_login_info")
public class MemberLoginInfo {
    @TableId(type = IdType.NONE)
    private String id;
    private String userId;
    private String userEmail;
    private String browser;
    private String ip;
    private String country;
    private Date loginTime;
}
