package org.biosino.system.api.domain.task;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> @date 2024/1/4
 */
@Data
@TableName("transfer_task")
public class TransferTask {

    @TableId(type = IdType.NONE)
    private String id;

    /**
     * 移动文件名
     */
    @TableField(value = "source_name")
    private String sourceName;

    /**
     * 移动文件路径
     */
    @TableField(value = "source_path")
    private String sourcePath;

    /**
     * 执行命令
     */
    private String command;

    /**
     * rsync 输出文件路径
     */
    @TableField(value = "log_path")
    private String logPath;

    /**
     * 存放路径
     */
    @TableField(value = "dest_path")
    private String destPath;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "user_id")
    private String userId;

    private String creator;

    @TableField(value = "start_time")
    private Date startTime;

    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 进度
     */
    private Integer progress = 0;

    /**
     * 速度
     */
    private String speed;

    private String status;

    @TableField(value = "total_size")
    private String totalSize;

    @TableField(value = "average_speed")
    private String averageSpeed;

    @TableField(value = "analyze_task_id")
    private String analyzeTaskId;

    @TableField(value = "run_no")
    private String runNo;

    @TableField(value = "upload_type")
    private String uploadType;

    @TableField(value = "task_type")
    private String taskType;

    @TableField(value = "task_type_id")
    private String taskTypeId;

    @TableField(value = "host")
    private String host;

    @TableField(value = "container_name")
    private String containerName;

    @TableField(value = "sub_task_type")
    private String subTaskType;

    @TableField(value = "submission_id")
    private String submissionId;

    @TableField(value = "data_no")
    private String dataNo;


}
