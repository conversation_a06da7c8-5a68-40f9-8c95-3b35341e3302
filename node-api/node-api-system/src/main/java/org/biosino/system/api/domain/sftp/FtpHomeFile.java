package org.biosino.system.api.domain.sftp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> @date 2024/5/9
 */
@Data
@TableName("ftp_home_file")
public class FtpHomeFile implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private String path;

    private Long size;

    @TableField("creator_email")
    private String creatorEmail;

    @TableField("create_time")
    private Date createTime;
}
