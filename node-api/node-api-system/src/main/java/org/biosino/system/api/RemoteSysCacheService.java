package org.biosino.system.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.factory.RemoteSysCacheFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 管理后台缓存处理接口
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteSysCacheService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteSysCacheFallbackFactory.class)
public interface RemoteSysCacheService {
    /**
     * 特殊数据集刷新缓存
     */
    @GetMapping("/fdWeb/refreshFdCache")
    R<Boolean> refreshFdCache(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
