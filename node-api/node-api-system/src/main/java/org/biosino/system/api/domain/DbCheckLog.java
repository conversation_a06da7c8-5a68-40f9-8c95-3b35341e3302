package org.biosino.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.common.core.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/10
 */
@Data
@TableName("db_check_log")
@AllArgsConstructor
@NoArgsConstructor
public class DbCheckLog implements java.io.Serializable {

    @TableId(type = IdType.AUTO)
    @Excel(name = "ID")
    private Long id;

    @Excel(name = "DB_ID")
    private String dbId;

    @Excel(name = "type")
    private String type;

    @TableField("type_id")
    @Excel(name = "type id")
    private String typeId;

    @TableField("subject_type")
    @Excel(name = "subject_type")
    private String subjectType;

    @TableField("tags")
    @Excel(name = "tags")
    private String tags;

    @Excel(name = "field")
    private String field;

    @Excel(name = "message")
    private String message;

    @Excel(name = "creator")
    private String creator;

    @TableField(exist = false)
    @Excel(name = "creator_email")
    private String creatorEmail;

    @Excel(name = "create_date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
}
