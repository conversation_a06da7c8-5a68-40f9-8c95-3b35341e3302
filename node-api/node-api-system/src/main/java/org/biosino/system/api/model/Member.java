package org.biosino.system.api.model;


import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class Member implements java.io.Serializable {

    @JSONField(name = "id")
    private String id;

    private String email;
    private String password;
    private String firstName;
    private String middleName;
    private String lastName;
    private String name;

    @JSONField(name = "organization")
    private String orgName;

    @JSONField(name = "department")
    private String deptName;

    @JSONField(name = "piname")
    private String piName;
    private String phone;
    private String fax;
    private String street;
    private String city;
    private String stateProvince;
    private String postalCode;

    // 审核状态（waiting【待审核】、fail【审核未通过】、pass【审核通过】）
    private String auditStatus;

    @JSONField(name = "contryRegion")
    private String countryRegion;

    private Date createTime;
    private Date updateTime;
    private String checkCode;
    private Date checkDate;
    // 用户可用状态（disable【禁用】、enable【启用】、inactive【未激活】）
    private String status;
    //右边开始，二进制的第一位表示分析员角色，第二位表示指定分析员角色
    private int role;
    private long authorizeBitMap;

}
