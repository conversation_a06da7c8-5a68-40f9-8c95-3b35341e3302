package org.biosino.system.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EmailWithContentDTO {
    @NotNull
    @Size(min = 1, max = 90)
    private List<String> toEmails;
    @NotBlank
    private String content;
    @NotBlank
    private String title;
}
