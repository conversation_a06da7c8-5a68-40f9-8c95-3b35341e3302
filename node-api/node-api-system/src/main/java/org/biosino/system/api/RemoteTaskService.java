package org.biosino.system.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.factory.RemoteTaskServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(contextId = "remoteTaskService", value = ServiceNameConstants.NODE_TASK_SERVICE, fallbackFactory = RemoteTaskServiceFallbackFactory.class)
public interface RemoteTaskService {
    /**
     * 开始检查整个数据库数据一致性
     */
    @GetMapping(value = "/fastqc/pushFastQcTaskStartMsg")
    public R pushFastQcTaskStartMsg(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping(value = "/fastqc/pushFastQcHpTaskStartMsg")
    public R pushFastQcHpTaskStartMsg(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping(value = "/samtool/pushSamToolTaskStartMsg")
    public R pushSamToolTaskStartMsg(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping(value = "/samtool/pushSamToolHpTaskStartMsg")
    public R pushSamToolHpTaskStartMsg(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
