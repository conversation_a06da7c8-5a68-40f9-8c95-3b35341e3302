package org.biosino.system.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.dto.EmailWithContentDTO;
import org.biosino.system.api.dto.ExpressInfoDTO;
import org.biosino.system.api.dto.SendEmailDTO;
import org.biosino.system.api.factory.RemoteNotificationFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(contextId = "remoteNotificationService", value = ServiceNameConstants.NODE_NOTIFICATION_SERVICE, fallbackFactory = RemoteNotificationFallbackFactory.class)
public interface RemoteNotificationService {

    @PostMapping("/sendExpressEmail")
    R sendExpressEmail(@RequestBody ExpressInfoDTO expressInfoDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/sendEmail")
    R sendEmail(@Validated @RequestBody SendEmailDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/sendEmailWithContent")
    R sendEmailWithContent(@Validated @RequestBody EmailWithContentDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
