package org.biosino.system.api.factory;

import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.domain.R;
import org.biosino.system.api.RemoteToolsService;
import org.biosino.system.api.domain.DbCheckLog;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date 2024/5/23
 */
@Component
@Slf4j
public class RemoteToolsServiceFallbackFactory implements FallbackFactory<RemoteToolsService> {
    @Override
    public RemoteToolsService create(Throwable throwable) {
        log.error("System服务调用失败:{}", throwable.getMessage());
        return new RemoteToolsService() {
            @Override
            public R checkAllDB(String source) {
                return R.fail("checkAllDB调用失败:" + throwable.getMessage());
            }

            @Override
            public R scanFtpHomeFile(String source) {
                return R.fail("scanFtpHomeFile调用失败:" + throwable.getMessage());
            }

            @Override
            public R<Map<String, List<DbCheckLog>>> relatedDataCheck(String[] dataNos, String source) {
                return R.fail("relatedDataCheck调用失败:" + throwable.getMessage());
            }
        };
    }
}
