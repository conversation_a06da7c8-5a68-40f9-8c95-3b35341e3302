package org.biosino.job.api.factory;

import org.biosino.common.core.domain.R;
import org.biosino.job.api.RemoteStatisticsSearchService;
import org.biosino.job.api.vo.statistics.PopularDataVO;
import org.biosino.job.api.vo.statistics.StatisticsMetadataVO;
import org.biosino.job.api.vo.statistics.UserDataVisits;
import org.biosino.job.api.vo.statistics.UserStatisticsVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 统计信息查询服务异常处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteStatisticsSearchServiceFallbackFactory implements FallbackFactory<RemoteStatisticsSearchService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteStatisticsSearchServiceFallbackFactory.class);

    @Override
    public RemoteStatisticsSearchService create(Throwable throwable) {
        log.error("统计信息查询服务调用失败:{}", throwable.getMessage());
        return new RemoteStatisticsSearchService() {

            @Override
            public R<StatisticsMetadataVO> getDataVolumeStatInfo(String source) {
                return R.fail("查询Data Volume统计数据失败:" + throwable.getMessage());
            }

            @Override
            public R<UserStatisticsVO> getUserStatInfo(String memberId, String source) {
                return R.fail("查询用户基本统计数据失败:" + throwable.getMessage());
            }

            @Override
            public R<List<UserDataVisits>> getMyDataActivityStatInfo(String memberId, boolean visits, Integer year, String country, String source) {
                return R.fail("查询My Data Activity统计数据失败:" + throwable.getMessage());
            }

            @Override
            public R<List<String>> getMyDataCountry(String memberId, boolean visits, String source) {
                return R.fail("查询有数据的国家列表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<PopularDataVO>> getPopularDataApi(final String statType, boolean exportFlag, String source) {
                return R.fail("获取热点数据统计信息失败:" + throwable.getMessage());
            }
        };
    }

}
