package org.biosino.job.api.vo.statistics;

import lombok.Data;

/**
 * 用户中心 用户自有数据统计
 *
 * <AUTHOR>
 * @date 2024/5/16
 */
@Data
public class UserStatisticsVO {
    private Long projAccessible = 0L;
    private Long projUnAccessible = 0L;

    private Long expAccessible = 0L;
    private Long expUnAccessible = 0L;

    private Long sapAccessible = 0L;
    private Long sapUnAccessible = 0L;

    private Long runAccessible = 0L;
    private Long runUnAccessible = 0L;

    private Long analAccessible = 0L;
    private Long analUnAccessible = 0L;

    private Long rawDataPrivate = 0L;
    private Long rawDataPrivateSize = 0L;
    private String rawDataPrivateSizeString;
    private Long rawDataRestricted = 0L;
    private Long rawDataRestrictedSize = 0L;
    private String rawDataRestrictedSizeString;
    private Long rawDataPublic = 0L;
    private Long rawDataPublicSize = 0L;
    private String rawDataPublicSizeString;

    private String rawDataAccessibleSizeString;
    private String rawDataUnAccessibleSizeString;
    private String rawDataTotalSizeString;

    private Long analDataPrivate = 0L;
    private Long analDataPrivateSize = 0L;
    private String analDataPrivateSizeString;
    private Long analDataRestricted = 0L;
    private Long analDataRestrictedSize = 0L;
    private String analDataRestrictedSizeString;
    private Long analDataPublic = 0L;
    private Long analDataPublicSize = 0L;
    private String analDataPublicSizeString;

    private String analDataAccessibleSizeString;
    private String analDataUnAccessibleSizeString;
    private String analDataTotalSizeString;

    private String dataTotalSizeString;
}
