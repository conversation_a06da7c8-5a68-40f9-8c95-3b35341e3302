package org.biosino.job.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.job.api.factory.RemoteStatisticsSearchServiceFallbackFactory;
import org.biosino.job.api.vo.statistics.PopularDataVO;
import org.biosino.job.api.vo.statistics.StatisticsMetadataVO;
import org.biosino.job.api.vo.statistics.UserDataVisits;
import org.biosino.job.api.vo.statistics.UserStatisticsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 统计信息查询服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteStatisticsSearchService", value = ServiceNameConstants.NODE_JOB_SERVICE, fallbackFactory = RemoteStatisticsSearchServiceFallbackFactory.class)
public interface RemoteStatisticsSearchService {

    /**
     * 查询Data Volume统计数据
     */
    @GetMapping("/statistics/getDataVolumeStatInfo")
    R<StatisticsMetadataVO> getDataVolumeStatInfo(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询用户基本统计数据
     */
    @GetMapping("/statistics/getUserStatInfo")
    R<UserStatisticsVO> getUserStatInfo(@RequestParam("memberId") String memberId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询My Data Activity统计数据
     */
    @GetMapping("/statistics/getMyDataActivityStatInfo")
    R<List<UserDataVisits>> getMyDataActivityStatInfo(@RequestParam("memberId") String memberId, @RequestParam("visits") boolean visits,
                                                      @RequestParam(value = "year", required = false) Integer year,
                                                      @RequestParam(value = "country", required = false) String country, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询有数据的国家列表
     */
    @GetMapping("/statistics/getMyDataCountry")
    R<List<String>> getMyDataCountry(@RequestParam("memberId") String memberId, @RequestParam("visits") boolean visits,
                                     @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 热点数据统计信息
     */
    @GetMapping("/statistics/getPopularDataApi")
    R<List<PopularDataVO>> getPopularDataApi(@RequestParam("statType") final String statType, @RequestParam("exportFlag") final boolean exportFlag, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
