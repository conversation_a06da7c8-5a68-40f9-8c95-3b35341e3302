package org.biosino.job.api.vo.statistics;

import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class PopularDataVO {
    /**
     * 页面热点数据显示数据量
     */
    public static final int SHOW_ROWS = 5;
    /**
     * 管理后台热点数据导出行数
     */
    public static final int EXPORT_ROWS = 200;

    private String type;
    private List<PopularDataStat> listData;
    private boolean showTags = true;

    @Data
    public static class PopularDataStat {
        private String no;
        private String name;
        private long num;
        private List<Set<String>> tagsGroup;
    }

}
