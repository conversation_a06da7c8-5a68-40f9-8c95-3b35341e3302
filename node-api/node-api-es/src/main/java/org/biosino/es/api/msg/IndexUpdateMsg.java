package org.biosino.es.api.msg;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
//@NoArgsConstructor
//@AllArgsConstructor
public class IndexUpdateMsg {

    private String type;

    private List<String> typeIds;

    // 是否为删除node_related_es操作
    private Boolean deleteRelatedEs = false;


    public IndexUpdateMsg() {
    }

    public IndexUpdateMsg(String type, List<String> typeIds) {
        this(type, typeIds, false);
    }

    public IndexUpdateMsg(String type, List<String> typeIds, boolean deleteRelatedEs) {
        this.type = type;
        this.typeIds = typeIds;
        this.deleteRelatedEs = deleteRelatedEs;
    }
}
