package org.biosino.es.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/8/24
 */
@Data
public class BrowseStatEsDTO implements Serializable {
    private Set<String> prjNoSet = new HashSet<>();
    private Set<String> expNoSet = new HashSet<>();
    private Set<String> sapNoSet = new HashSet<>();
    private Set<String> anaNoSet = new HashSet<>();

    private Set<String> dataNoSet = new HashSet<>();

    private Set<String> expTypes = new HashSet<>();
    private Set<String> sapTypes = new HashSet<>();

    private String currMemberId;
}
