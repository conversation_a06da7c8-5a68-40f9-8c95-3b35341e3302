package org.biosino.es.api.vo.detail;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.BaseSearch;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 数据共享相关模块的查询
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class DataShareSearchVO extends BaseSearch implements Serializable {
    @NotBlank
    private String type;
    @NotBlank
    private String typeNo;
    private String creator;

    // 数据等级
    private String security;

    // Security模块的查询
    private String expNo;
    private String sapNo;
    private String sapName;
    private String sapType;
    private String organism;

    // Request模块的查询
    // expNo、sapNo
    private String runNo;
    private String datNo;
    private String analNo;

    private List<String> dataNos;


    // 下拉框自动补全希望查询的字段和数据
    private String field;
    private String keyword;

    // My Request列表页面查询data列表
    private String requestId;

}
