package org.biosino.es.api.vo.fd;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.BaseSearch;

import java.io.Serializable;
import java.util.List;

/**
 * 单样本多组学 项目查询条件
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class SingleSapQueryVO extends BaseSearch implements Serializable {
    private String sapID;
    private String sapType;
    private String sapName;
    private String expType;

    /**
     * 左侧统计查询条件
     */
    private List<String> leftStatQueries;

    public void setSapID(String sapID) {
        this.sapID = StrUtil.trimToNull(sapID);
    }

    public void setSapType(String sapType) {
        this.sapType = StrUtil.trimToNull(sapType);
    }

    public void setSapName(String sapName) {
        this.sapName = StrUtil.trimToNull(sapName);
    }

    public void setExpType(String expType) {
        this.expType = StrUtil.trimToNull(expType);
    }

}
