package org.biosino.es.api.vo.detail;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.BaseSearch;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ExpSapSearchVO extends BaseSearch implements Serializable {
    private String projNo;
    private String type;
    private String field;

    private String currMemberId;
    private String currMemberEmail;

}
