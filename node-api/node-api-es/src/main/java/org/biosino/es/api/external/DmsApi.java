package org.biosino.es.api.external;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.es.api.RemoteDictService;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class DmsApi {
//    @Value("${node.api.dms-api:https://idc.biosino.org/dms}")
//    public String dmsApiUrl;

    /**
     * 根据名称获取 TaxId
     */
    public List<String> findTaxId(String name, final RemoteDictService remoteDictService) {
        name = StrUtil.trimToNull(name);
        if (name == null) {
            return initList(null);
        }

        final R<List<String>> taxIdR = remoteDictService.getTaxIdByName(name, SecurityConstants.INNER);

        List<String> taxIds = null;
        if (R.isSuccess(taxIdR)) {
            taxIds = taxIdR.getData();
        }
        // 废弃
        /* if (taxId == null) {
            taxId = findTaxIdByName(dmsApiUrl, name);
            if (taxId != null) {
                // 保存从dms获取的Taxonomy数据
                final TaxonomyNodeDTO dto = new TaxonomyNodeDTO();
                dto.setTaxId(taxId);
                dto.setScientificName(name);
                remoteUpdateIndexService.saveNewTaxonomy(dto);
            }
        } */
        return initList(taxIds);
    }

    private <T> List<T> initList(List<T> list) {
        return CollUtil.isEmpty(list) ? new ArrayList<>() : list;
    }

    private String findTaxIdByName(String dmsApiUrl, String name) {
        if (name == null) {
            return null;
        }
        String taxId = null;
        final String result = HttpUtil.get(StrUtil.format(dmsApiUrl + "/ref/dict/brief/entities/by-synonym?abbreviation=Taxonomy&synonym={}", name));
        if (result != null) {
            try {
                final JSONObject jsonObject = JSON.parseObject(result);
                if (Boolean.TRUE.equals(jsonObject.getBoolean("success"))) {
                    final JSONArray data = jsonObject.getJSONArray("data");
                    if (CollUtil.isNotEmpty(data)) {
                        taxId = data.getJSONObject(0).getString("id");
                    }
                }
            } catch (Exception e) {
                log.error("Find taxId by DMS API error: {}, api result:{}", e.getMessage(), result);
            }
        }
        return taxId;
    }

}
