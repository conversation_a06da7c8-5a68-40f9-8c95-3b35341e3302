package org.biosino.es.api.factory;

import com.alibaba.fastjson.JSON;
import org.biosino.common.core.domain.R;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.DataSearchDTO;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.dto.SecurityDTO;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.es.api.vo.detail.DataShareSearchVO;
import org.biosino.es.api.vo.detail.RelatedEsSearchVO;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

/**
 * 同步更新索引异常处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteDataFallbackFactory implements FallbackFactory<RemoteDataService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteDataFallbackFactory.class);


    @Override
    public RemoteDataService create(Throwable throwable) {
        log.error("ES查询Related Data失败:{}", throwable.getMessage());
        return new RemoteDataService() {

            @Override
            public R<List<RelatedDataDTO>> findAllByTypeAndNo(DataListSearchVO searchVO, String source) {
                return R.fail("查询Related Data失败,type: " + searchVO.getType() + ",typeNo:" + searchVO.getTypeNo() + ",Error Msg:" + throwable.getMessage());
            }

            /*@Override
            public R<List<RelatedDataDTO>> findAllByTypeAndNoAndSecurity(DataListSearchVO searchVO, String security, String source) {
                return R.fail("查询Related Data失败,type: " + searchVO.getType() + ",typeNo:" + searchVO.getTypeNo() + ",security:" + security + ",Error Msg:" + throwable.getMessage());
            }*/

            @Override
            public R<EsPageInfo<RelatedDataDTO>> findPageData(DataListSearchVO searchVO, String source) {
                return R.fail("查询Related Data分页数据失败,type: " + searchVO.getType() + ",typeNo:" + searchVO.getTypeNo() + ",Error Msg:" + throwable.getMessage());
            }

            @Override
            public R<String> generateDataExcel(DataListSearchVO searchVO, String source) {
                return R.fail("Failed to generate Data Excel file:" + throwable.getMessage());
            }

            @Override
            public R<EsPageInfo<RelatedDataDTO>> findPublicPageData(DataListSearchVO searchVO, String source) {
                return R.fail("查询Related Data分页数据失败,type: " + searchVO.getType() + ",Error Msg:" + throwable.getMessage());
            }

            @Override
            public R<EsPageInfo<RelatedDataDTO>> findDataShareData(DataShareSearchVO searchVO, String source) {
                return R.fail("查询Related Data分页数据失败,search: " + JSON.toJSONString(searchVO) + ",Error Msg:" + throwable.getMessage());
            }

            @Override
            public R<Set<String>> findSelectData(DataShareSearchVO searchVO, String source) {
                return R.fail("查询Related Data分页数据失败,search: " + JSON.toJSONString(searchVO) + ",Error Msg:" + throwable.getMessage());
            }

            @Override
            public R<List<RelatedDataDTO>> findAllByDataNos(DataSearchDTO searchDTO, String source) {
                return R.fail("查询Related Data失败,dataNos: " + JSON.toJSONString(searchDTO) + ",Error Msg:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> existPublicByDataNos(SecurityDTO dto, String source) {
                return R.fail("查询Related Data失败,dataNos: " + JSON.toJSONString(dto.getDataNos()) + ",Error Msg:" + throwable.getMessage());
            }

            @Override
            public R<Long> countDataByPage(@RequestBody DataListSearchVO searchVO, String source) {
                throwable.printStackTrace();
                return R.fail("查询countDataByPage失败 " + ",Error Msg:" + throwable.getMessage());
            }

            @Override
            public R<List<RelatedDataDTO>> statisticDataByPage(DataListSearchVO searchVO, String source) {
                return R.fail("查询statisticDataByPage失败 " + ",Error Msg:" + throwable.getMessage());
            }

            @Override
            public R<EsPageInfo<RelatedDataDTO>> findRelatedDataPage(RelatedEsSearchVO searchVO, String source) {
                return R.fail("查询Related Data分页数据失败,search: " + JSON.toJSONString(searchVO) + ",Error Msg:" + throwable.getMessage());
            }
        };
    }
}
