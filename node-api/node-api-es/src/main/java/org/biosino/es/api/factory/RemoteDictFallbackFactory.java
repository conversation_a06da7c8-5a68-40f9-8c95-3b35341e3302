package org.biosino.es.api.factory;

import org.biosino.common.core.domain.R;
import org.biosino.es.api.RemoteDictService;
import org.biosino.es.api.dto.TaxonomyNodeDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 字典的检索服务异常处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteDictFallbackFactory implements FallbackFactory<RemoteDictService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteDictFallbackFactory.class);


    @Override
    public RemoteDictService create(Throwable throwable) {
        log.error("ES服务调用失败:{}", throwable.getMessage());
        return new RemoteDictService() {

            @Override
            public R<List<String>> getTaxIdByName(String name, String source) {
                return R.fail("查询TaxId失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> existPlatform(String name, String source) {
                return R.fail("查询Platform是否存在失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> existDisease(String name, String source) {
                return R.fail("查询Disease是否存在失败:" + throwable.getMessage());
            }

            /*@Override
            public R<String> findTaxIdByExistName(String name, String source) {
                return R.fail("查询Taxonomy是否存在失败:" + throwable.getMessage());
            }*/

            @Override
            public R<List<TaxonomyNodeDTO>> getTaxByTaxId(String taxId, String source) {
                return R.fail("查询Taxonomy失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> existBiome(String type, String value, String source) {
                return R.fail("查询Biome是否存在失败:" + throwable.getMessage());
            }
        };
    }
}
