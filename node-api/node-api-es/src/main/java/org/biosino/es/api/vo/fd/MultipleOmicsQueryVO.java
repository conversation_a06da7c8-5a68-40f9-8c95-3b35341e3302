package org.biosino.es.api.vo.fd;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.BaseSearch;

import java.io.Serializable;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * 多组学 项目查询条件
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class MultipleOmicsQueryVO extends BaseSearch implements Serializable {
    private String projID;
    private String projName;
    private String expType;

    /**
     * 左侧统计查询条件
     */
    private List<String> leftStatQueries;

    public void setProjID(String projID) {
        this.projID = StrUtil.trimToNull(projID);
    }

    public void setProjName(String projName) {
        this.projName = StrUtil.trimToNull(projName);
    }

    public void setExpType(String expType) {
        this.expType = StrUtil.trimToNull(expType);
    }

}
