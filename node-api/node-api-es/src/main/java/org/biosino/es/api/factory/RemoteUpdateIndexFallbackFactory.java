package org.biosino.es.api.factory;

import org.biosino.common.core.domain.R;
import org.biosino.es.api.RemoteUpdateIndexService;
import org.biosino.es.api.dto.TaxonomyNodeDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 同步更新索引异常处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteUpdateIndexFallbackFactory implements FallbackFactory<RemoteUpdateIndexService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteUpdateIndexFallbackFactory.class);


    @Override
    public RemoteUpdateIndexService create(Throwable throwable) {
        log.error("ES同步索引服务调用失败:{}", throwable.getMessage());
        return new RemoteUpdateIndexService() {

            @Override
            public R<Boolean> createProjectIndex(String no, String source) {
                return R.fail("同步项目" + no + "索引失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> createAnalysisIndex(String no, String source) {
                return R.fail("同步分析" + no + "索引失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> createIndex(String token, String source) {
                return R.fail("同步browse界面索引失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> createRelatedNodeEs(String token, String source) {
                return R.fail("同步data list索引失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> createOtherNodeEs(String token, String source) {
                return R.fail("同步Taxonomy等ES索引失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> saveNewTaxonomy(TaxonomyNodeDTO dto) {
                return R.fail("保存从dms获取的Taxonomy数据失败:" + throwable.getMessage());
            }
        };
    }
}
