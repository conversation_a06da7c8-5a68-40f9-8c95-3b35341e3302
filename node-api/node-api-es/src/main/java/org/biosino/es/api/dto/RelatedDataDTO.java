package org.biosino.es.api.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.common.core.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class RelatedDataDTO implements Serializable {

    @Excel(name = "Project ID")
    private String projNo;
    @Excel(name = "Project Name")
    private String projName;
    private String projDesc;

    @Excel(name = "Experiment ID")
    private String expNo;
    @Excel(name = "Experiment Name")
    private String expName;
    @Excel(name = "Experiment Type")
    private String expType;
    private String expDesc;

    @Excel(name = "Sample ID")
    private String sapNo;
    @Excel(name = "Sample Name")
    private String sapName;
    @Excel(name = "Sample Type")
    private String sapType;
    @Excel(name = "Organism")
    private String organism;
    private String sapDesc;

    @Excel(name = "Run ID")
    private String runNo;
    @Excel(name = "Run Name")
    private String runName;

    private String analNo;
    private String analName;
    private String analType;
    private String customAnalysisType;

    @Excel(name = "Data ID")
    private String datNo;
    @Excel(name = "Data Name")
    private String name;
    @Excel(name = "Data Type")
    private String dataType;
    @Excel(name = "Security")
    private String security;
    private String md5;
    private Long fileSize;
    @Excel(name = "File Size")
    private String readableFileSize;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date uploadTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date submissionDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date publicDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateDate;

    @Excel(name = "Creator ID")
    private String creator;

    private Boolean accessible = false;
    private Boolean shared = false;
    private Boolean requested = false;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expireDate;

}
