package org.biosino.es.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.es.api.dto.DataSearchDTO;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.dto.SecurityDTO;
import org.biosino.es.api.factory.RemoteDataFallbackFactory;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.es.api.vo.detail.DataShareSearchVO;
import org.biosino.es.api.vo.detail.RelatedEsSearchVO;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;
import java.util.Set;

/**
 * 同步更新索引
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDataService", value = ServiceNameConstants.NODE_ES_SERVICE, fallbackFactory = RemoteDataFallbackFactory.class)
public interface RemoteDataService {

    @PostMapping("/findAllByTypeAndNo")
    R<List<RelatedDataDTO>> findAllByTypeAndNo(@RequestBody DataListSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

//    @PostMapping("/findAllByTypeAndNoAndSecurity")
//    R<List<RelatedDataDTO>> findAllByTypeAndNoAndSecurity(@RequestBody DataListSearchVO searchVO, @RequestParam(value = "security") String security, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/findPageData")
    R<EsPageInfo<RelatedDataDTO>> findPageData(@RequestBody DataListSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 生成Data数据Excel文件
     */
    @PostMapping("/generateDataExcel")
    R<String> generateDataExcel(@RequestBody DataListSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/findPublicPageData")
    R<EsPageInfo<RelatedDataDTO>> findPublicPageData(@RequestBody DataListSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/findDataShareData")
    R<EsPageInfo<RelatedDataDTO>> findDataShareData(@RequestBody DataShareSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/findSelectData")
    R<Set<String>> findSelectData(@RequestBody DataShareSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/findAllByDataNos")
    R<List<RelatedDataDTO>> findAllByDataNos(@RequestBody DataSearchDTO searchDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/existPublicByDataNos")
    R<Boolean> existPublicByDataNos(@RequestBody SecurityDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/countDataByPage")
    R<Long> countDataByPage(@RequestBody DataListSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/statisticDataByPage")
    R<List<RelatedDataDTO>> statisticDataByPage(@RequestBody DataListSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * system服务调用的查询接口
     */
    @PostMapping("/findRelatedDataPage")
    R<EsPageInfo<RelatedDataDTO>> findRelatedDataPage(@RequestBody RelatedEsSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
