package org.biosino.es.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.dto.es.EsTreeItemDTO;
import org.dromara.easyes.core.biz.EsPageInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 特殊数据集，多组学(多样本)数据
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FdMultipleResDTO implements Serializable {
    // 列表数据
    private EsPageInfo<FeatureDataPrjDTO> pageInfo;
    // 树数据
    private List<EsTreeItemDTO> esTreeItems;

    private List<String> defaultCheckedKeys;
}
