package org.biosino.es.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.domain.dto.es.StatDTO;
import org.biosino.es.api.dto.*;
import org.biosino.es.api.factory.RemoteNodeESSearchServiceFallbackFactory;
import org.biosino.es.api.vo.BrowseStatResVO;
import org.biosino.es.api.vo.detail.ExpSapSearchVO;
import org.biosino.es.api.vo.fd.MultipleOmicsQueryVO;
import org.biosino.es.api.vo.fd.MultipleSampleQueryVO;
import org.biosino.es.api.vo.fd.SingleSapQueryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 同步更新索引
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteNodeESSearchService", value = ServiceNameConstants.NODE_ES_SERVICE, fallbackFactory = RemoteNodeESSearchServiceFallbackFactory.class)
public interface RemoteNodeESSearchService {

    /**
     * 根据项目编号模糊检索所有项目编号
     */
    @GetMapping("/nodeES/searchPrjId/{keyword}")
    R<List<String>> searchPrjId(@PathVariable("keyword") String keyword, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据项目编号统计数据
     */
    @PostMapping("/nodeES/statByPrj")
    R<Map<String, StatDTO>> statByPrj(@RequestBody Set<String> prjNos, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 查询多组学项目数据
     */
    @PostMapping("/nodeES/searchMultipleOmics")
    R<FdMultipleResDTO> searchMultipleOmics(@RequestBody MultipleOmicsQueryVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据项目编号集合，查询多组学项目数据
     */
    @PostMapping("/nodeES/searchMultOmicByPrjNos")
    R<List<FeatureDataPrjDTO>> searchMultOmicByPrjNos(@RequestBody List<String> prjNos, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询多样本资源项目数据
     */
    @PostMapping("/nodeES/searchMultipleSample")
    R<FdMultipleResDTO> searchMultipleSample(@RequestBody MultipleSampleQueryVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据项目编号集合，查询多样本项目数据
     */
    @PostMapping("/nodeES/searchMultSampleByPrjNos")
    R<List<FeatureDataPrjDTO>> searchMultSampleByPrjNos(@RequestBody List<String> prjNos, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询单样本多组学项目数据
     */
    @PostMapping("/nodeES/searchSingleSap")
    R<FdMultipleResDTO> searchSingleSap(@RequestBody SingleSapQueryVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据样本编号集合，查询单样本多组学样本数据
     */
    @PostMapping("/nodeES/searchSingleSapcByNos")
    R<List<FeatureDataPrjDTO>> searchSingleSapcByNos(@RequestBody List<String> nos, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询特殊数据集首页组学统计数据
     */
    @GetMapping("/nodeES/expStatInfo")
    R<Map<String, StatDTO>> expStatInfo(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据项目编号，查询样本和对应组学类型
     */
    @PostMapping("/nodeES/findSapNoAndExpTypesByPrjNos")
    R<FdSampleDTO> findSapNoAndExpTypesByPrjNos(@RequestBody FdQueryDTO search, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询node_es，根据项目编号、数据类型，查询对应字段统计数据
     */
    @PostMapping("/nodeES/countByTypeAndField")
    R<List<FieldCountDTO>> countByTypeAndField(@RequestBody ExpSapSearchVO searchVO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据项目编号查询所有样本编号
     */
    @GetMapping("/nodeES/searchAllSapNoByPrjId")
    R<ProjSapNosDTO> searchAllSapNoByPrjNo(@RequestParam(value = "prjNo") String prjNo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据编号对应关联类型数据
     */
    @GetMapping("/nodeES/searchTypeData")
    R<Map<String, NodeEsTypeDTO>> searchTypeData(@RequestParam(value = "typeIds") List<String> typeIds, @RequestParam(value = "type") String type,
                                                 @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据编号集合和类型查询对应数据总大小
     */
    @PostMapping("/nodeES/findAllSizeByNosAndType")
    R<Long> findAllSizeByNosAndType(@RequestBody FileSizeSearchDTO searchDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取样本-实验组合统计数据
     */
    @GetMapping("/nodeES/statExpBySap")
    R<SampleExpStatDTO> statExpBySap(@RequestParam(value = "sapType") String sapType,
                                     @RequestParam(value = "expType") String expType, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取多组学项目统计数据
     */
    @GetMapping("/nodeES/statMultiExp")
    R<MultiExpStatDTO> statMultiExp(@RequestParam(value = "searchSample") Boolean searchSample,
                                    @RequestParam(value = "sapType") String sapType, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询前端浏览页统计数据
     */
    @PostMapping("/nodeES/findBrowseStatNum")
    R<Map<String, BrowseStatResVO>> findBrowseStatNum(@RequestBody BrowseStatEsDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据sample的biome_curated字段首页的数据统计
     */
    @GetMapping("/nodeES/statByBiomeCurated")
    R<Map<String, StatDTO>> statByBiomeCurated(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
