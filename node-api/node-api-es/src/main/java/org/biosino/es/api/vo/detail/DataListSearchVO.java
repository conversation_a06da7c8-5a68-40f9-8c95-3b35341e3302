package org.biosino.es.api.vo.detail;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.BaseSearch;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class DataListSearchVO extends BaseSearch implements Serializable {
    private String type;
    private String typeNo;
    private String currMemberId;
    private String currMemberEmail;

    private String searchName;

    private Boolean fastqcFinished;

    private boolean findAll;

    private Set<String> dataNos;

    private String startTime;
    private String endTime;

    private boolean findFilePath = false;

    /**
     * 是否必须存在data no
     */
    private boolean mustHasData = true;

    private String security;

    public void setSearchName(String searchName) {
        this.searchName = StrUtil.trimToNull(searchName);
    }
}
