package org.biosino.es.api.vo.detail;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.BaseSearch;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class RelatedEsSearchVO extends BaseSearch implements Serializable {

    public static final int MAX_PAGE_SIZE = 50000;

    private List<String> projNos;

    private List<String> expNos;

    private List<String> sapNos;

    private String expType;

    private String sapType;

    private String organism;

    private List<String> security;

    private String dataType;

    private int pageSize;

    // 对外metadata api接口新增的条件
    private List<String> runNos;

    private List<String> analNos;

    private List<String> dataNos;

    private String creator;

    private Date beginTime;

    private Date endTime;

    @Override
    public void setPageSize(int pageSize) {
        this.pageSize = Math.min(pageSize, MAX_PAGE_SIZE);
    }
}
