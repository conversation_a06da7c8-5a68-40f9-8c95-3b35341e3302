package org.biosino.es.api.factory;

import org.biosino.common.core.domain.R;
import org.biosino.common.core.domain.dto.es.StatDTO;
import org.biosino.es.api.RemoteNodeESSearchService;
import org.biosino.es.api.dto.*;
import org.biosino.es.api.vo.BrowseStatResVO;
import org.biosino.es.api.vo.detail.ExpSapSearchVO;
import org.biosino.es.api.vo.fd.MultipleOmicsQueryVO;
import org.biosino.es.api.vo.fd.MultipleSampleQueryVO;
import org.biosino.es.api.vo.fd.SingleSapQueryVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 同步更新索引异常处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteNodeESSearchServiceFallbackFactory implements FallbackFactory<RemoteNodeESSearchService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteNodeESSearchServiceFallbackFactory.class);

    @Override
    public RemoteNodeESSearchService create(Throwable throwable) {
        log.error("ES查询服务调用失败:{}", throwable.getMessage());
        return new RemoteNodeESSearchService() {

            @Override
            public R<List<String>> searchPrjId(String keyword, String source) {
                return R.fail("查询项目编号失败:" + throwable.getMessage());
            }

            @Override
            public R<Map<String, StatDTO>> statByPrj(Set<String> prjNos, String source) {
                return R.fail("根据项目编号统计数据失败:" + throwable.getMessage());
            }

            @Override
            public R<FdMultipleResDTO> searchMultipleOmics(MultipleOmicsQueryVO searchVO, String source) {
                return R.fail("查询多组学项目数据失败:" + throwable.getMessage());
            }

            @Override
            public R<FdMultipleResDTO> searchMultipleSample(MultipleSampleQueryVO searchVO, String source) {
                return R.fail("查询多样本资源项目数据失败:" + throwable.getMessage());
            }

            @Override
            public R<List<FeatureDataPrjDTO>> searchMultOmicByPrjNos(List<String> prjNos, String source) {
                return R.fail("根据项目编号集合，查询多组学项目数据失败:" + throwable.getMessage());
            }

            @Override
            public R<List<FeatureDataPrjDTO>> searchMultSampleByPrjNos(List<String> prjNos, String source) {
                return R.fail("根据项目编号集合，查询多样本项目数据失败:" + throwable.getMessage());
            }

            @Override
            public R<FdMultipleResDTO> searchSingleSap(SingleSapQueryVO searchVO, String source) {
                return R.fail("查询单样本多组学项目数据失败:" + throwable.getMessage());
            }

            @Override
            public R<List<FeatureDataPrjDTO>> searchSingleSapcByNos(List<String> nos, String source) {
                return R.fail("根据样本编号集合，查询单样本多组学样本数据失败:" + throwable.getMessage());
            }

            @Override
            public R<Map<String, StatDTO>> expStatInfo(String source) {
                return R.fail("查询特殊数据集首页组学统计数据失败:" + throwable.getMessage());
            }

            @Override
            public R<FdSampleDTO> findSapNoAndExpTypesByPrjNos(FdQueryDTO search, String source) {
                return R.fail("根据项目编号，查询样本和对应组学类型失败:" + throwable.getMessage());
            }

            @Override
            public R<List<FieldCountDTO>> countByTypeAndField(ExpSapSearchVO searchVO, String source) {
                return R.fail("根据项目编号、数据类型，查询对应字段统计数据失败:" + throwable.getMessage());
            }

            @Override
            public R<ProjSapNosDTO> searchAllSapNoByPrjNo(String prjNo, String source) {
                return R.fail("根据项目编号查询所有样本编号失败:" + throwable.getMessage());
            }

            @Override
            public R<Map<String, NodeEsTypeDTO>> searchTypeData(List<String> typeIds, String type, String source) {
                return R.fail("根据编号对应关联类型数据失败:" + throwable.getMessage());
            }

            @Override
            public R<Long> findAllSizeByNosAndType(FileSizeSearchDTO searchDTO, String source) {
                return R.fail("根据编号集合和类型查询对应数据总大小失败:" + throwable.getMessage());
            }

            @Override
            public R<SampleExpStatDTO> statExpBySap(String sapType, String expType, String source) {
                return R.fail("获取样本-实验组合统计数据失败:" + throwable.getMessage());
            }

            @Override
            public R<MultiExpStatDTO> statMultiExp(Boolean searchSample, String sapType, String source) {
                return R.fail("获取多组学项目统计数据失败:" + throwable.getMessage());
            }

            @Override
            public R<Map<String, BrowseStatResVO>> findBrowseStatNum(BrowseStatEsDTO dto, String source) {
                return R.fail("查询前端浏览页统计数据失败：" + throwable.getMessage());
            }

            @Override
            public R<Map<String, StatDTO>> statByBiomeCurated(String source) {
                return R.fail("查询水圈统计数据失败：" + throwable.getMessage());
            }
        };
    }
}
