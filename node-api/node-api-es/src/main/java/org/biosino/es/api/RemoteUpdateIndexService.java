package org.biosino.es.api;

import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.biosino.es.api.dto.TaxonomyNodeDTO;
import org.biosino.es.api.factory.RemoteUpdateIndexFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 同步更新索引
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUpdateIndexService", value = ServiceNameConstants.NODE_ES_SERVICE, fallbackFactory = RemoteUpdateIndexFallbackFactory.class)
public interface RemoteUpdateIndexService {

    /**
     * 更新指定项目的索引数据
     */
    @GetMapping("/nodeES/createProjectIndex/{projectNo}")
    R<Boolean> createProjectIndex(@PathVariable("projectNo") String projectNo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 更新指定分析的索引数据
     */
    @GetMapping("/nodeES/createAnalysisIndex/{analysisNo}")
    R<Boolean> createAnalysisIndex(@PathVariable("analysisNo") String analysisNo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 创建browse界面索引
     */
    @GetMapping("/nodeES/createIndex")
    R<Boolean> createIndex(@RequestParam("token") String token, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 创建data list索引
     */
    @GetMapping("/nodeES/createRelatedNodeEs")
    R<Boolean> createRelatedNodeEs(@RequestParam("token") String token, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 创建Taxonomy等ES索引
     */
    @GetMapping("/nodeES/createOtherNodeEs")
    R<Boolean> createOtherNodeEs(@RequestParam("token") String token, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 保存从dms获取的Taxonomy数据
     */
    @PostMapping("/nodeES/saveNewTaxonomy")
    R<Boolean> saveNewTaxonomy(@RequestBody TaxonomyNodeDTO dto);

}
