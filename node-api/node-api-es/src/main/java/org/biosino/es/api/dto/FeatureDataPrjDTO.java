package org.biosino.es.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashSet;

/**
 * 多组学--项目
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class FeatureDataPrjDTO implements Serializable {
    private String typeId;

    private String sampleType;
    private LinkedHashSet<String> relaSampleType;
    private LinkedHashSet<String> relaExpType;

    private String name;
    private String description;

    //    private String subFirstName;
//    private String subLastName;
    private String subName;

    private Date modifiedDate;
}
