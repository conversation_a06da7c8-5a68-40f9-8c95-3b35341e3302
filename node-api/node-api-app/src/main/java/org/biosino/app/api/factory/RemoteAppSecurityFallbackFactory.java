package org.biosino.app.api.factory;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.app.api.RemoteAppSecurityService;
import org.biosino.app.api.dto.UpdateSecurityDTO;
import org.biosino.common.core.domain.R;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class RemoteAppSecurityFallbackFactory implements FallbackFactory<RemoteAppSecurityService> {
    @Override
    public RemoteAppSecurityService create(Throwable throwable) {
        log.error("app服务调用失败:{}", throwable.getMessage());
        return new RemoteAppSecurityService() {
            @Override
            public R<List<String>> updateDataSecurity(UpdateSecurityDTO updateSecurityDTO) {
                return R.fail(StrUtil.format("更新安全等级失败, Error Msg:{}", throwable.getMessage()));
            }
        };
    }
}
