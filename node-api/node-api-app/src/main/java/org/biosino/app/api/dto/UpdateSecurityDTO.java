package org.biosino.app.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 更新安全等级参数
 *
 * <AUTHOR>
 */
@Data
public class UpdateSecurityDTO {

    @NotBlank
    private String securityType;

    private String pubDate;

    @NotNull
    private List<String> dataNos;

    // 用户选择的备案选项
    private String recordRadio;
    // 用户输入的人类遗传资源备案编号
    private String recordValue;

    private Boolean admin = false;
}
