package org.biosino.app.api.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
@Data
public class DataDTO {
    private String projNo;
    private String projName;
    private String projDesc;

    private String expNo;
    private String expName;
    private String expType;
    private String expDesc;

    private String sapNo;
    private String sapName;
    private String sapType;
    private String organism;
    private String sapDesc;

    private String runNo;
    private String runName;

    private String analNo;
    private String analName;
    private String analType;

    private String datNo;
    private String name;
    private String dataType;
    private String security;
    private String md5;
    private Long fileSize;
    private String readableFileSize;
    private Date uploadTime;
    private Date submissionDate;
    private String creator;

    private Date expireDate;
    private Boolean accessible = false;
    private Boolean shared = false;
    private Boolean requested = false;
}
