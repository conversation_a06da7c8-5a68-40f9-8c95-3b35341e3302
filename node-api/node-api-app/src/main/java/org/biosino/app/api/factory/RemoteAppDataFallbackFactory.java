package org.biosino.app.api.factory;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.app.api.RemoteAppDataService;
import org.biosino.app.api.dto.DataDTO;
import org.biosino.common.core.domain.R;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class RemoteAppDataFallbackFactory implements FallbackFactory<RemoteAppDataService> {
    @Override
    public RemoteAppDataService create(Throwable throwable) {
        log.error("app服务调用失败:{}", throwable.getMessage());
        return new RemoteAppDataService() {
            @Override
            public R<List<DataDTO>> getProjectDataList(String projNo) {
                return R.fail(StrUtil.format("查询Project下的Data失败, projNo: {}, Error Msg:{}", projNo, throwable.getMessage()));
            }

            @Override
            public R<List<DataDTO>> getExperimentDataList(String expNo) {
                return R.fail(StrUtil.format("查询Experiment下的Data失败, expNo: {}, Error Msg:{}", expNo, throwable.getMessage()));
            }

            @Override
            public R<List<DataDTO>> getSampleDataList(String sapNo) {
                return R.fail(StrUtil.format("查询Sample下的Data失败, sapNo: {}, Error Msg:{}", sapNo, throwable.getMessage()));
            }

            @Override
            public R<List<DataDTO>> getAnalysisDataList(String analNo) {
                return R.fail(StrUtil.format("查询Analysis下的Data失败, analNo: {}, Error Msg:{}", analNo, throwable.getMessage()));
            }
        };
    }
}
