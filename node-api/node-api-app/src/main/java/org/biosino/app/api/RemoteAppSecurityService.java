package org.biosino.app.api;

import org.biosino.app.api.dto.UpdateSecurityDTO;
import org.biosino.app.api.factory.RemoteAppSecurityFallbackFactory;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(contextId = "remoteSecurityService", value = ServiceNameConstants.NODE_APP_SERVICE, fallbackFactory = RemoteAppSecurityFallbackFactory.class)
public interface RemoteAppSecurityService {


    @PostMapping("/security/updateDataSecurity")
    R<List<String>> updateDataSecurity(@RequestBody UpdateSecurityDTO updateSecurityDTO);
}
