package org.biosino.app.api;

import org.biosino.app.api.dto.DataDTO;
import org.biosino.app.api.factory.RemoteAppDataFallbackFactory;
import org.biosino.common.core.constant.ServiceNameConstants;
import org.biosino.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(contextId = "remoteAppDataService", value = ServiceNameConstants.NODE_APP_SERVICE, fallbackFactory = RemoteAppDataFallbackFactory.class)
public interface RemoteAppDataService {
    @GetMapping("/project/getDataList/{projNo}")
    R<List<DataDTO>> getProjectDataList(@PathVariable("projNo") String projNo);

    @GetMapping("/experiment/getDataList/{expNo}")
    R<List<DataDTO>> getExperimentDataList(@PathVariable("expNo") String expNo);

    @GetMapping("/sample/getDataList/{sapNo}")
    R<List<DataDTO>> getSampleDataList(@PathVariable("sapNo") String sapNo);

    @GetMapping("/analysis/getDataList/{analNo}")
    R<List<DataDTO>> getAnalysisDataList(@PathVariable("analNo") String analNo);

}
