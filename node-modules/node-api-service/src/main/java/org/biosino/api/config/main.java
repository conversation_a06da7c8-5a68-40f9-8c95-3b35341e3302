package org.biosino.api.config;

import cn.hutool.crypto.SecureUtil;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> @date 2025/8/15
 */
public class main {
    private static final String API_SUB_HEADER = "subsystem";
    private static final String API_T_HEADER = "token";

    private static final String API_AES_SALT = "**-NODE@PICB-***";
    private static final String API_AES_TOKEN = "pdms";

    public static void main(String[] args) {
        String key = SecureUtil.aes(API_AES_SALT.getBytes(StandardCharsets.UTF_8)).encryptHex(API_AES_TOKEN);

        System.out.println(key);
    }
}
