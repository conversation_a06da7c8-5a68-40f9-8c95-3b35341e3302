package org.biosino.api.repository;

import org.biosino.api.dto.MetadataSearchDTO;
import org.biosino.common.mongo.entity.Sample;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
public interface SampleCustomRepository {

    Class<Sample> clz();


    List<Sample> findAllAttrBySapNos(Collection<String> sapNos);

    Page<Sample> findPage(MetadataSearchDTO dto);
}
