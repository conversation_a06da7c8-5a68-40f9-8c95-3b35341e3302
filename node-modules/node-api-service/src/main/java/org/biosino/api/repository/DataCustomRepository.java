package org.biosino.api.repository;


import org.biosino.api.dto.MetadataSearchDTO;
import org.biosino.common.mongo.entity.Data;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface DataCustomRepository {

    List<Data> findAllFilePathByDataNos(Collection<String> dataNos);

    Page<Data> findPage(MetadataSearchDTO dto);

    Page<Data> findDeletePage(MetadataSearchDTO queryDTO);

    Optional<Data> findByDatNo(String dataNo);
}
