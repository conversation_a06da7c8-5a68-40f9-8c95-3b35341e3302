package org.biosino.api.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.biosino.api.dto.SyncToNodeFileDTO;
import org.biosino.api.dto.VipMapSyncTaskDTO;
import org.biosino.api.repository.*;
import org.biosino.api.vo.ProjectVipMapVO;
import org.biosino.api.vo.RunFileVO;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.ShareProject;
import org.biosino.common.mongo.entity.other.SyncFile;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.rabbitmq.constant.RoutingKeyConstant;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/6/24
 */
@Service
@RequiredArgsConstructor
public class VipMapService {

    private final ProjectRepository projectRepository;

    private final SampleRepository sampleRepository;

    private final DataRepository dataRepository;

    private final ShareRepository shareRepository;

    private final RunRepository runRepository;

    private final VipmapSyncTaskRepository vipmapSyncTaskRepository;

    private final RemoteMemberService remoteMemberService;

    private final RemoteDataService remoteDataService;

    private final MessageSender messageSender;

    @Value("#{${vipmap.analysis-mapping}}")
    private Map<String, String> vipmapAnalysisTypeMap;

    public List<ProjectVipMapVO> getProjectByNo(String email, List<String> projectNos) {
        List<ProjectVipMapVO> result = new ArrayList<>();
        List<Project> projects = projectRepository.findAllByProjNos(projectNos);
        if (CollectionUtils.isEmpty(projects)) {
            return result;
        }

        R<MemberDTO> r = remoteMemberService.getMemberInfoByEmail(email, "FtpUser", SecurityConstants.INNER);
        if (R.isError(r) || r.getData() == null) {
            throw new ServiceException("Please specify email.");
        }
        MemberDTO member = r.getData();
        List<String> shareProNos = new ArrayList<>();
        List<Share> shareList = shareRepository.findAllByShareTo(member.getEmail());
        for (Share share : shareList) {
            if (CollUtil.isNotEmpty(share.getProjects())) {
                shareProNos.addAll(share.getProjects().stream().map(ShareProject::getProjectNo).distinct().collect(Collectors.toList()));
            }
        }

        for (Project project : projects) {
            ProjectVipMapVO vo = new ProjectVipMapVO();
            vo.setSecurity(project.getVisibleStatus());
            if (StrUtil.equals(project.getVisibleStatus(), VisibleStatusEnum.Accessible.name())) {
                vo.setAccess(true);
            } else if (StrUtil.equals(member.getEmail(), project.getCreator())) {
                vo.setAccess(true);
            } else if (CollUtil.isNotEmpty(shareProNos) && shareProNos.contains(project.getProjectNo())) {
                vo.setAccess(true);
            } else {
                vo.setAccess(false);
            }

            vo.setProjectNo(project.getProjectNo());
            vo.setName(project.getName());
            vo.setDescription(project.getDescription());
            vo.setUsedIds(project.getUsedIds());
            result.add(vo);
        }

        return result;

    }

    public List<RunFileVO> getFileInfo(String projectNo, String email) throws ServiceException {
        if (StringUtils.isBlank(projectNo)) {
            throw new ServiceException("Please specify Project ID.");
        }
        List<RunFileVO> result = new ArrayList<>();
        MemberDTO member;
        if (StringUtils.isBlank(email)) {
            throw new ServiceException("Please specify email.");
        } else {
            R<MemberDTO> r = remoteMemberService.getMemberInfoByEmail(email, "FtpUser", SecurityConstants.INNER);
            if (R.isError(r) || r.getData() == null) {
                throw new ServiceException("Please specify email.");
            }
            member = r.getData();
        }
        Project project = projectRepository.findFirstByProjNo(projectNo).orElseThrow(() -> new ServiceException("Not found project: " + projectNo));

        if (StrUtil.equals(project.getVisibleStatus(), VisibleStatusEnum.Unaccessible.name()) && !shareRepository.existShareByTypeNoAndEmail(AuthorizeType.project.name(), projectNo, member.getEmail())) {
            throw new ServiceException("Project Unaccessible: " + projectNo);
        }


        DataListSearchVO searchVO = new DataListSearchVO();
        searchVO.setType(AuthorizeType.project.name());
        searchVO.setTypeNo(projectNo);
        searchVO.setFindAll(true);
        searchVO.setCurrMemberId(member.getId());
        searchVO.setCurrMemberEmail(member.getEmail());
        R<EsPageInfo<RelatedDataDTO>> r = remoteDataService.findPageData(searchVO, SecurityConstants.INNER);

        if (R.isError(r) || r.getData() == null) {
            throw new ServiceException("Failed to get file info.");
        }
        EsPageInfo<RelatedDataDTO> pageData = r.getData();
        List<RelatedDataDTO> dataList = pageData.getList();
        List<String> dataNos = dataList.stream().map(RelatedDataDTO::getDatNo).distinct().collect(Collectors.toList());
        Map<String, String> dataNoToFilePathMap = dataRepository.findAllFilePathByDataNos(dataNos).stream().collect(Collectors.toMap(Data::getDatNo, Data::getFilePath, (existingValue, newValue) -> existingValue));
        List<String> sapNos = dataList.stream().map(RelatedDataDTO::getSapNo).distinct().collect(Collectors.toList());
        Map<String, Sample> sapNoToSampleMap = sampleRepository.findAllAttrBySapNos(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));
        for (RelatedDataDTO relatedDataDTO : dataList) {
            if (relatedDataDTO.getAccessible().equals(Boolean.FALSE)) {
                continue;
            }
            RunFileVO vo = new RunFileVO();
            vo.setProjectNo(projectNo);
            vo.setExperimentName(relatedDataDTO.getExpName());
            vo.setExperimentType(relatedDataDTO.getExpType());
            vo.setSampleName(relatedDataDTO.getSapName());
            vo.setOrganism(relatedDataDTO.getOrganism());
            vo.setSecurity(relatedDataDTO.getSecurity());
            vo.setRunNo(relatedDataDTO.getRunNo());
            vo.setRunName(relatedDataDTO.getRunName());
            vo.setDataNo(relatedDataDTO.getDatNo());
            vo.setFileName(relatedDataDTO.getName());
            vo.setFilePath(dataNoToFilePathMap.get(relatedDataDTO.getDatNo()));
            vo.setFileSize(relatedDataDTO.getFileSize());
            vo.setMd5(relatedDataDTO.getMd5());
            vo.setAttributes(sapNoToSampleMap.get(relatedDataDTO.getSapNo()).getAttributes());
            vo.setCustomAttr(sapNoToSampleMap.get(relatedDataDTO.getSapNo()).getCustomAttr());
            result.add(vo);
        }

        return result;
    }

    public void createSyncToNodeTask(VipMapSyncTaskDTO dto) {
        // 校验用户信息是否正确
        R<MemberDTO> r = remoteMemberService.getOneMemberByMemberId(dto.getUserId(), "FtpUser", SecurityConstants.INNER);
        if (R.isError(r) || r.getData() == null) {
            throw new ServiceException("Failed to get user info.");
        }
        // 1. 校验
        // 检验分析类型是否存在
        if (!vipmapAnalysisTypeMap.containsKey(dto.getAnalysisType())) {
            throw new ServiceException("Analysis type not found: " + dto.getAnalysisType());
        }
        List<SyncToNodeFileDTO> syncFileList = dto.getSyncFiles();
        for (SyncToNodeFileDTO item : syncFileList) {
            File file = FileUtil.file(vipmapAnalysisTypeMap.get(dto.getAnalysisType()), dto.getTaskId(), item.getFilePath());
            // 校验文件是否在磁盘中存在
            if (!file.exists()) {
                throw new ServiceException("File not found: " + item.getFilePath());
            }
            // 检验是否填写信息
            if (StrUtil.isBlank(item.getRunNo()) && StrUtil.isBlank(item.getOtherTargetName()) && StrUtil.isBlank(item.getOtherTargetLink())) {
                throw new ServiceException("Please specify \"runNo\" or \"otherTargetName and otherTargetLink\".");
            }
            // 如果填写的是RunNo信息，校验runNo是否合法，且能访问得到
            if (StrUtil.isNotBlank(item.getRunNo())) {
                Run run = runRepository.findFirstByRunNo(item.getRunNo()).orElseThrow(() -> new ServiceException("Run not found: " + item.getRunNo()));
                if (StrUtil.equals(run.getVisibleStatus(), VisibleStatusEnum.Unaccessible.name()) &&
                        StrUtil.equals(run.getCreator(), dto.getUserId())) {
                    throw new ServiceException("You can not access the run: " + item.getRunNo());
                }
            }
        }

        // 2.创建任务
        VipMapSyncTask vipMapSyncTask = new VipMapSyncTask();
        vipMapSyncTask.setTaskId(dto.getTaskId());
        vipMapSyncTask.setAnalysisType(dto.getAnalysisType());
        vipMapSyncTask.setCreator(dto.getUserId());
        vipMapSyncTask.setCreateDate(new Date());
        vipMapSyncTask.setSyncFiles(syncFileList.stream().map(
                x -> {
                    SyncFile item = new SyncFile();
                    item.setFilePath(x.getFilePath());
                    item.setRunNo(x.getRunNo());
                    item.setOtherTargetName(x.getOtherTargetName());
                    item.setOtherTargetLink(x.getOtherTargetLink());
                    item.setStatus("syncing");
                    return item;
                }
        ).collect(Collectors.toList()));

        VipMapSyncTask save = vipmapSyncTaskRepository.save(vipMapSyncTask);

        // 发送消息到消息队列,让task组件来处理
        messageSender.sendDelayMsg(RoutingKeyConstant.vipmap_sync_task_routing_key, save);
    }

    public List<VipMapSyncTask> findSyncTaskByTaskId(String taskId, String analysisType) {
        if (StrUtil.isBlank(taskId) || StrUtil.isBlank(analysisType)) {
            throw new ServiceException("Task id or analysis type cannot be empty.");
        }
        return vipmapSyncTaskRepository.findByTaskIdAndAnalysisType(taskId, analysisType);
    }
}
