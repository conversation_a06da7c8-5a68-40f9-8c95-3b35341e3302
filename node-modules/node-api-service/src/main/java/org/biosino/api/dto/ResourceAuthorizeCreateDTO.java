package org.biosino.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 数据请求弹框查询条件
 *
 * <AUTHOR>
 */
@Data
public class ResourceAuthorizeCreateDTO {

    @NotBlank
    private String username;

    @NotBlank
    private String password;

    @NotBlank
    private String type;
    @NotEmpty
    private List<String> typeNos;
    private List<String> dataNo;

    // 请求信息
    private String description;
}
