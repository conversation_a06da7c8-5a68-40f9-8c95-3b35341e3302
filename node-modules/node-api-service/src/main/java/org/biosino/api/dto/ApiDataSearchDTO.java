package org.biosino.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.BaseSearch;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ApiDataSearchDTO extends BaseSearch {

    @ApiModelProperty(value = "Project 编号")
    private String projNo;

    @ApiModelProperty(value = "Experiment 编号")
    private String expNo;

    @ApiModelProperty(value = "Sample 编号")
    private String sapNo;

    @ApiModelProperty(value = "Run 编号")
    private String runNo;

    @ApiModelProperty(value = "Analysis 编号")
    private String analNo;

    @ApiModelProperty(value = "Data 编号")
    private String dataNo;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "更新时间的开始时间 范围")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;

    @ApiModelProperty(value = "更新时间的结束时间 范围")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
}
