package org.biosino.api.service;

import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.ApiRequestLog;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/7/19
 */
@Service
@RequiredArgsConstructor
public class AsyncApiLogService {
    private final MongoTemplate mongoTemplate;

    @Async
    public void saveApiRequestLog(ApiRequestLog apiRequestLog) {
        mongoTemplate.save(apiRequestLog);
    }
}
