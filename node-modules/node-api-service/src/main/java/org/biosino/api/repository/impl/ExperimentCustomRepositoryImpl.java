package org.biosino.api.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.api.dto.MetadataSearchDTO;
import org.biosino.api.repository.ExperimentCustomRepository;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Experiment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@RequiredArgsConstructor
public class ExperimentCustomRepositoryImpl implements ExperimentCustomRepository {

    private final MongoTemplate mongoTemplate;


    private static List<Criteria> baseConditions() {
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(Criteria.where("ownership").is(OwnershipEnum.self_support.getDesc()));
        return condition;
    }

    @Override
    public Page<Experiment> findPage(MetadataSearchDTO queryDTO) {
        List<Criteria> conditions = baseConditions();

        // 必须是要审核通过的
        conditions.add(Criteria.where("audited").is(AuditEnum.audited.name()));

        if (CollUtil.isNotEmpty(queryDTO.getNos())) {
            conditions.add(new Criteria().orOperator(
                    Criteria.where("exp_no").in(queryDTO.getNos()),
                    Criteria.where("used_ids").in(queryDTO.getNos())
            ));
        }

        if (StrUtil.isNotBlank(queryDTO.getCreator())) {
            conditions.add(Criteria.where("creator").is(queryDTO.getCreator()));
        }

        if (StrUtil.isNotBlank(queryDTO.getTag())) {
            conditions.add(Criteria.where("source_project").in(queryDTO.getTag()));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            conditions.add(Criteria.where("update_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            conditions.add(Criteria.where("update_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }

        Query query = new Query(new Criteria().andOperator(conditions));

        // 查询数据量
        long total = mongoTemplate.count(query, Experiment.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Experiment> content = mongoTemplate.find(query, Experiment.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

}
