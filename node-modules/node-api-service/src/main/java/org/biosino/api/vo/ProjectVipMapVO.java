package org.biosino.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@Data
@ApiModel
public class ProjectVipMapVO {

    @ApiModelProperty("项目编号")
    private String projectNo;

    @ApiModelProperty("项目名称")
    private String name;

    @ApiModelProperty("安全等级")
    private String security;

    @ApiModelProperty("项目是否可以访问")
    private boolean access;

    @ApiModelProperty("项目描述")
    private String description;

    @ApiModelProperty("项目曾用id")
    private List<String> usedIds;
}
