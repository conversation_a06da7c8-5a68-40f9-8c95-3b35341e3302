package org.biosino.api.repository;

import org.biosino.api.dto.MetadataSearchDTO;
import org.biosino.common.mongo.entity.Project;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface ProjectCustomRepository {

    List<Project> findAllByProjNos(List<String> projectNos);

    Optional<Project> findFirstByProjNo(String projectNo);

    Page<Project> findPage(MetadataSearchDTO dto);
}
