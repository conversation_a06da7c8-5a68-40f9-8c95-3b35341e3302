package org.biosino.api.repository;


import org.biosino.api.dto.MetadataSearchDTO;
import org.biosino.common.mongo.entity.Run;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RunCustomRepository {

    Optional<Run> findFirstByRunNo(String runNo);

    Page<Run> findPage(MetadataSearchDTO dto);
}
