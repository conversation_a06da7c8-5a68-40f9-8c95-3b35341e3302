package org.biosino.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.biosino.common.mongo.dto.BaseQuery;

import java.util.List;

/**
 * @a uthor <PERSON>e <PERSON>
 * @date 2024/10/17
 */
@Data
@ApiModel("metadata查询请求")
public class MetadataSearchDTO extends BaseQuery {

    @ApiModelProperty(value = "编号列表")
    private List<String> nos;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "标签")
    private String tag;

    // sample相关
    @ApiModelProperty(value = "Sample的subjectId")
    private String subjectId;

}
