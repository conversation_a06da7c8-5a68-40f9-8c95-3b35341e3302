package org.biosino.api.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.api.repository.ResourceAuthorizeCustomRepository;
import org.biosino.common.core.enums.RequestStatusEnum;
import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@RequiredArgsConstructor
public class ResourceAuthorizeCustomRepositoryImpl implements ResourceAuthorizeCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public boolean existsRequestRecord(String memberId, String type, String typeNo, RequestStatusEnum statusEnum) {

        Query query = new Query(Criteria.where("authorize_to").is(memberId)
                .and("type").is(type).and("type_id").is(typeNo)
                .and("status").is(statusEnum.getDesc()));

        return mongoTemplate.exists(query, ResourceAuthorize.class);
    }
}
