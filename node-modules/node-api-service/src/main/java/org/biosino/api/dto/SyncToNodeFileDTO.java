package org.biosino.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Data
@ApiModel("文件同步项")
public class SyncToNodeFileDTO {

    @ApiModelProperty(value = "文件名", required = true)
    private String filename;

    @ApiModelProperty(value = "文件在ViPMAP文件路径", required = true)
    private String filePath;

    @ApiModelProperty(value = "Analysis填写Target为RunNo")
    private String runNo;

    @ApiModelProperty(value = "Analysis填写的TargetName")
    private String otherTargetName;

    @ApiModelProperty(value = "Analysis填写的TargetLink")
    private String otherTargetLink;

    @ApiModelProperty(value = "文件同步当前的状态")
    private String status;

}
