package org.biosino.api.repository;

import org.biosino.common.mongo.entity.VipMapSyncTask;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/12
 */
@Repository
public interface VipmapSyncTaskRepository extends MongoRepository<VipMapSyncTask, String> {
    List<VipMapSyncTask> findByTaskIdAndAnalysisType(String taskId, String analysisType);
}
