package org.biosino.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/11
 */
@Data
@ApiModel("ViPMAP同步任务请求体")
public class VipMapSyncTaskDTO {

    @ApiModelProperty(value = "ViPMAP的分析类型", required = true)
    @NotBlank
    private String analysisType;

    @ApiModelProperty(value = "ViPMAP的任务编号", required = true)
    @NotBlank
    private String taskId;

    @ApiModelProperty(value = "同步文件列表", required = true)
    @NotNull
    private List<SyncToNodeFileDTO> syncFiles;

    @ApiModelProperty(value = "BMDC用户名", required = true)
    @NotBlank
    private String username;

    @ApiModelProperty(value = "BMDC用户ID", required = true)
    @NotBlank
    private String userId;
}
