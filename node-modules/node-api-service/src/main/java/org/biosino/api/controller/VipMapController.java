package org.biosino.api.controller;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.biosino.api.dto.VipMapSyncTaskDTO;
import org.biosino.api.service.VipMapService;
import org.biosino.api.vo.ProjectVipMapVO;
import org.biosino.api.vo.RunFileVO;
import org.biosino.common.core.domain.R;
import org.biosino.common.mongo.entity.VipMapSyncTask;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24
 */
@RestController
@RequestMapping("/vipmap")
@RequiredArgsConstructor
@Api(tags = "VipMap API", description = "ViPMAP调用的api接口")
public class VipMapController {

    private final VipMapService vipMapService;

    @GetMapping("/getProjectByNo")
    @ApiOperation(value = "根据项目编号获取项目信息")
    public R<List<ProjectVipMapVO>> getProjectByNo(@ApiParam(value = "用户的邮箱", required = true) String email, @ApiParam(value = "查询的项目编号", required = true) String[] projectNos) {
        List<ProjectVipMapVO> result = vipMapService.getProjectByNo(email, CollUtil.toList(projectNos));
        return R.ok(result);
    }

    @GetMapping("/getFileInfo/{projectNo}")
    @ApiOperation(value = "根据项目编号获取项目的Data文件信息")
    public R<List<RunFileVO>> getFileInfo(@PathVariable String projectNo, @ApiParam(value = "用户的邮箱", required = true) String email) {
        List<RunFileVO> result = vipMapService.getFileInfo(projectNo, email);
        return R.ok(result);
    }

    @GetMapping("/findSyncTaskByTaskId")
    @ApiOperation(value = "根据任务编号和任务类型查询同步任务")
    public R<List<VipMapSyncTask>> findSyncTaskByTaskId(@ApiParam(value = "ViPMAP中的任务编号", required = true) @RequestParam("taskId") String taskId, @ApiParam(value = "ViPMAP的分析类型", required = true) @RequestParam("analysisType") String analysisType) {
        List<VipMapSyncTask> result = vipMapService.findSyncTaskByTaskId(taskId, analysisType);
        return R.ok(result);
    }

    @PostMapping("/syncToNode")
    @ApiOperation(value = "开始将文件同步至node")
    public R syncToNode(@RequestBody @Validated VipMapSyncTaskDTO dto) {
        vipMapService.createSyncToNodeTask(dto);
        return R.ok();
    }
}
