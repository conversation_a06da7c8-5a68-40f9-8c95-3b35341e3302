package org.biosino.api.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class TokenMvcConfigurer implements WebMvcConfigurer {

    private final TokenVerifyInterceptor tokenVerifyInterceptor;

    public TokenMvcConfigurer(@Lazy TokenVerifyInterceptor tokenVerifyInterceptor) {
        this.tokenVerifyInterceptor = tokenVerifyInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tokenVerifyInterceptor).addPathPatterns("/vipmap/**", "/metadata/**", "/node/**");
    }

}
