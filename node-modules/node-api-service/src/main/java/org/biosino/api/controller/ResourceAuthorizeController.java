package org.biosino.api.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.api.dto.ResourceAuthorizeCreateDTO;
import org.biosino.api.service.ResourceAuthorizeService;
import org.biosino.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/4/15
 */
@RestController
@RequestMapping("/node/resourceAuthorize")
@RequiredArgsConstructor
public class ResourceAuthorizeController {

    private final ResourceAuthorizeService resourceAuthorizeService;

    @RequestMapping("/saveBatch")
    public R saveRequestData(@Validated @RequestBody ResourceAuthorizeCreateDTO dto) {
        resourceAuthorizeService.saveBatch(dto);
        return R.ok();
    }

}
