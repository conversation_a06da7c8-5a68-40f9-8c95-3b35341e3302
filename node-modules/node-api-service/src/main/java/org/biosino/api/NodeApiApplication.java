package org.biosino.api;

import org.biosino.common.security.annotation.EnableCustomConfig;
import org.biosino.common.security.annotation.EnableRyFeignClients;
import org.biosino.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR> @date 2024/5/27
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@ComponentScan(basePackages = {"org.biosino.common.mongo.entity", "org.biosino.api"})
public class NodeApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(NodeApiApplication.class, args);
        System.out.println("Node Api模块启动成功");
    }
}
