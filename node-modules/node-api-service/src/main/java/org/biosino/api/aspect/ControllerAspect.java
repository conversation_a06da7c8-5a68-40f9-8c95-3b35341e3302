package org.biosino.api.aspect;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.CodeSignature;
import org.biosino.api.service.AsyncApiLogService;
import org.biosino.common.core.utils.ServletUtils;
import org.biosino.common.core.utils.StringUtils;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.log.enums.BusinessStatus;
import org.biosino.common.mongo.entity.ApiRequestLog;
import org.springframework.core.NamedThreadLocal;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/19
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class ControllerAspect {

    /**
     * 计算操作消耗时间
     */
    private static final ThreadLocal<Long> TIME_THREADLOCAL = new NamedThreadLocal<Long>("Cost Time");

    /**
     * 声明切点，后续使用时直接使用 controllerMethod() 即可
     * 等价于不声明直接使用  @Around( pointcut="execution(* org.biosino.api.controller..*.*(..))")
     */
    @Pointcut("execution(* org.biosino.api.controller..*.*(..))")
    public void controllerMethod() {
    }

    private final AsyncApiLogService asyncApiLogService;

    /**
     * 处理请求前执行
     */
    @Before(value = "controllerMethod()")
    public void boBefore(JoinPoint joinPoint) {
        TIME_THREADLOCAL.set(System.currentTimeMillis());
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "controllerMethod()", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Object jsonResult) {
        handleLog(joinPoint, null, jsonResult);
    }


    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "controllerMethod()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e) {
        handleLog(joinPoint, e, null);
    }

    private void handleLog(JoinPoint joinPoint, Exception e, Object jsonResult) {
        try {
            ApiRequestLog requestLog = new ApiRequestLog();

            requestLog.setRequestIp(IpUtils.getIpAddr());
            requestLog.setStatus(BusinessStatus.SUCCESS.ordinal());

            requestLog.setRequestUrl(StringUtils.substring(Objects.requireNonNull(ServletUtils.getRequest()).getRequestURI(), 0, 255));
            if (e != null) {
                requestLog.setStatus(BusinessStatus.FAIL.ordinal());
                requestLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
            }
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            requestLog.setMethod(className + "." + methodName + "()");
            // 设置请求方式
            requestLog.setRequestMethod(ServletUtils.getRequest().getMethod());

            // 方法接收到的参数
            String params = getArgs(joinPoint);
            requestLog.setMethodArgs(params);

            // 设置参数
            Map<?, ?> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
            requestLog.setRequestParam(JSON.toJSONString(paramsMap));

            requestLog.setJsonResult(JSON.toJSONString(jsonResult));
            requestLog.setRequestTime(new Date());

            // 设置消耗时间
            requestLog.setCostTime(System.currentTimeMillis() - TIME_THREADLOCAL.get());
            // 保存日志到数据库
            asyncApiLogService.saveApiRequestLog(requestLog);
        } catch (Exception exp) {
            log.error("异常信息:{}", exp.getMessage());
        } finally {
            TIME_THREADLOCAL.remove();
        }
    }

    public String getArgs(JoinPoint joinPoint) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        CodeSignature signature = (CodeSignature) joinPoint.getSignature();
        String[] names = signature.getParameterNames();
        Object[] value = joinPoint.getArgs();
        for (int i = 0; i < names.length; i++) {
            map.put(names[i], value[i]);
        }
        return JSON.toJSONString(map);
    }

}
