package org.biosino.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "project下的文件列表")
public class RunFileVO {

    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    @ApiModelProperty(value = "实验名称")
    private String experimentName;

    @ApiModelProperty(value = "实验类型")
    private String experimentType;

    @ApiModelProperty(value = "样本名称")
    private String sampleName;

    @ApiModelProperty(value = "物种")
    private String organism;

    @ApiModelProperty(value = "attributes")
    private Map<String, String> attributes;

    @ApiModelProperty(value = "自定义的attributes")
    private Map<String, String> customAttr;

    @ApiModelProperty(value = "安全等级")
    private String security;

    @ApiModelProperty(value = "Run No")
    private String runNo;

    @ApiModelProperty(value = "Run Name")
    private String runName;

    @ApiModelProperty(value = "Data ID")
    private String dataNo;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    @ApiModelProperty(value = "文件对于的MD5值")
    private String md5;

}
