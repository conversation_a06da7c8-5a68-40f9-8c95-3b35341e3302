package org.biosino.api.vo.metadata;

import lombok.Data;
import org.biosino.common.mongo.entity.other.Submitter;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Data
public class SampleVO {

    private String sapNo;

    private String name;

    private String description;

    private String protocol;

    private String taxId;

    private String organism;

    private String tissue;

    private List<String> relatedLinks;

    private String creator;

    private Date createDate;

    private Date updateDate;

    private Submitter submitter;

    private String subjectType;

    private Map<String, String> attributes = new LinkedHashMap<>();

    private Map<String, String> customAttr = new LinkedHashMap<>();

    private Map<String, String> customAttrDesc = new LinkedHashMap<>();

    private List<String> usedIds;

    private List<String> sourceProject;

    private String visibleStatus;

    private List<PublishVO> references;
}
