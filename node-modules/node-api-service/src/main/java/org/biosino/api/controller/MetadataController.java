package org.biosino.api.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.biosino.api.dto.ApiDataSearchDTO;
import org.biosino.api.dto.MetadataSearchDTO;
import org.biosino.api.service.MetadataService;
import org.biosino.api.vo.metadata.*;
import org.biosino.common.core.domain.R;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> @date 2024/10/17
 */
@RestController
@RequestMapping("/metadata")
@RequiredArgsConstructor
@Api(tags = "Metadata API", description = "对外查询Metadata的api接口")
public class MetadataController {

    private final MetadataService metadataService;

    @GetMapping("/project/list")
    @ApiOperation(value = "查询Project List")
    public R<List<ProjectVO>> listProject(MetadataSearchDTO dto) {
        List<ProjectVO> result = metadataService.listProject(dto);
        return R.ok(result);
    }

    @GetMapping("/experiment/list")
    @ApiOperation(value = "查询Experiment List")
    public R<List<ExperimentVO>> listExperiment(MetadataSearchDTO dto) {
        List<ExperimentVO> result = metadataService.listExperiment(dto);
        return R.ok(result);
    }

    @GetMapping("/sample/list")
    @ApiOperation(value = "查询Sample List")
    public R<List<SampleVO>> listSample(MetadataSearchDTO dto) {
        List<SampleVO> result = metadataService.listSample(dto);
        return R.ok(result);
    }

    @GetMapping("/run/list")
    @ApiOperation(value = "查询Run List")
    public R<List<RunVO>> listRun(MetadataSearchDTO dto) {
        List<RunVO> result = metadataService.listRun(dto);
        return R.ok(result);
    }

    @GetMapping("/analysis/list")
    @ApiOperation(value = "查询Analysis List")
    public R<List<AnalysisVO>> listAnalysis(MetadataSearchDTO dto) {
        List<AnalysisVO> result = metadataService.listAnalysis(dto);
        return R.ok(result);
    }


    @GetMapping("/data/list")
    @ApiOperation(value = "查询存在的Data List")
    public R<List<DataVO>> listData(MetadataSearchDTO dto) {
        List<DataVO> result = metadataService.listData(dto);
        return R.ok(result);
    }

    @GetMapping("/data/listDelete")
    @ApiOperation(value = "查询删除的Data List")
    public R<List<DataVO>> listDelete(MetadataSearchDTO dto) {
        List<DataVO> result = metadataService.listDeleteData(dto);
        return R.ok(result);
    }

    @GetMapping("/relatedData")
    @ApiOperation(value = "查询Es中Data List")
    public R<List<RelatedDataDTO>> listRelatedData(ApiDataSearchDTO dto) {
        List<RelatedDataDTO> result = metadataService.listRelatedData(dto);
        return R.ok(result);
    }
}
