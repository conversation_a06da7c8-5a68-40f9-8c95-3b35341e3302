<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.biosino.node</groupId>
        <artifactId>node-modules</artifactId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>node-api-service</artifactId>

    <description>
        node-modules-api 模块
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Swagger UI -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.fox.version}</version>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- Node Common DataSource -->
        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-common-datasource</artifactId>
        </dependency>

        <!-- Node Common DataScope -->
        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-common-datascope</artifactId>
        </dependency>

        <!-- Node Common Log -->
        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-common-log</artifactId>
        </dependency>

        <!-- Node Common Swagger -->
        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-common-mongo</artifactId>
        </dependency>

        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-api-es</artifactId>
        </dependency>

        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-api-app</artifactId>
        </dependency>

        <!-- elasticsearch 索引 -->
        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-common-es</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <!-- Node Common RabbitMQ -->
        <dependency>
            <groupId>org.biosino.node</groupId>
            <artifactId>node-common-rabbitmq</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>