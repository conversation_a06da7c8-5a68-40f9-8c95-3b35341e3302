package org.biosino.member.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.utils.AESUtil;
import org.biosino.member.config.Constants;
import org.biosino.system.api.dto.MemberDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BmdcRegist 接口服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BmdcRegistService {

    @Autowired
    private Constants constants;

    /**
     * 根据用户ID查询用户详细信息
     *
     * @param memberId         用户ID
     * @param currentUserEmail NodeadminUser、FtpUser或者当前已登录用户ID
     */
    public MemberDTO getOneMemberByMemberId(String memberId, String currentUserEmail) {
        if (StrUtil.isBlank(memberId) || StrUtil.isBlank(currentUserEmail)) {
            return null;
        }
        try {
            String encodeMemberId = AESUtil.encrypt(memberId, null, constants.getQueryKey());
            String url = constants.getUrl() + "/apiTools/getMemberInfoById/" + encodeMemberId;
            log.info("Info in getOneMemberByMemberId: url = {}", url);

            String result = getRequestData(url, currentUserEmail);
            String msg = getMsgBody(result);
            if (StrUtil.isBlank(msg)) {
                return null;
            }
            return JSON.parseObject(msg, MemberDTO.class);
        } catch (Exception e) {
            log.error("getOneMemberByMemberId接口请求失败：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public MemberDTO getMemberInfoByEmail(String memberEmail, String currentUserEmail) {
        if (StrUtil.isBlank(memberEmail) || StrUtil.isBlank(currentUserEmail)) {
            return null;
        }
        try {
            String encodeMemberId = AESUtil.encrypt(memberEmail, null, constants.getQueryKey());
            String url = constants.getUrl() + "/apiTools/getMemberInfoByEmail/" + encodeMemberId;
            log.info("Info in getMemberInfoByEmail: url = {}", url);

            String result = getRequestData(url, currentUserEmail);
            String msg = getMsgBody(result);
            if (StrUtil.isBlank(msg)) {
                return null;
            }
            return JSON.parseObject(msg, MemberDTO.class);
        } catch (Exception e) {
            log.error("getMemberInfoByEmail接口请求失败：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public List<MemberDTO> getMemberListByMemberEmailLike(String email, String currentUserEmail) {
        if (StrUtil.isBlank(email) || StrUtil.isBlank(currentUserEmail)) {
            return null;
        }
        try {
            List<MemberDTO> memberList = new ArrayList<>();
            String encodeMemberId = AESUtil.encrypt(email, null, constants.getQueryKey());
            String url = constants.getUrl() + "/apiTools/getMemberListByEmailLike/" + encodeMemberId;
            log.info("Info in getMemberListByEmailLike: url = {}", url);

            String result = getRequestData(url, currentUserEmail);
            String msg = getMsgBody(result);
            if (StrUtil.isBlank(msg)) {
                return null;
            }
            JSONObject memberListJson = JSON.parseObject(msg);
            if (memberListJson.containsKey("member_list")) {
                JSONArray memberListJsonResult = memberListJson.getJSONArray("member_list");
                memberList = JSONObject.parseArray(JSONObject.toJSONString(memberListJsonResult), MemberDTO.class);
            }
            return memberList;
        } catch (Exception e) {
            log.error("getMemberListByEmailLike接口请求失败：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public List<String> getAllMemberEmailList(String currentUserEmail) {
        if (StrUtil.isBlank(currentUserEmail)) {
            return null;
        }
        try {
            List<String> emailList = new ArrayList<>();
            String url = constants.getUrl() + "/apiTools/getAllMemberEmailList";
            log.info("Info in getAllMemberEmailList: url = {}", url);

            String result = getRequestData(url, currentUserEmail);
            String msg = getMsgBody(result);
            if (StrUtil.isBlank(msg)) {
                return null;
            }
            JSONObject emailListJson = JSON.parseObject(msg);
            if (emailListJson.containsKey("email_list")) {
                JSONArray emailListJsonResult = emailListJson.getJSONArray("email_list");
                emailList = emailListJsonResult.stream().map(Object::toString).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            }
            return emailList;
        } catch (Exception e) {
            log.error("getAllMemberEmailList接口请求失败：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public Long getNodeMemberByCreateDateRange(Date startDate, Date endDate, String currentUserEmail) {
        if (startDate == null || endDate == null || StrUtil.isBlank(currentUserEmail)) {
            return null;
        }
        try {
            long memberCount = 0;
            JSONObject dateRangeJson = new JSONObject();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateRangeJson.put("startDate", formatter.format(startDate));
            dateRangeJson.put("endDate", formatter.format(endDate));
            String encodeDateRange = AESUtil.encrypt(dateRangeJson.toJSONString(), null, constants.getQueryKey());
            String url = constants.getUrl() + "/apiTools/getNodeMemberByCreateDateRange" + "/" + encodeDateRange;
            log.info("Info in getNodeMemberByCreateDateRange: url = {}", url);

            String result = getRequestData(url, currentUserEmail);
            String msg = getMsg(result);
            if (StrUtil.isBlank(msg)) {
                return 0L;
            }
            JSONObject msgJSON = JSON.parseObject(msg);
            if (msgJSON.containsKey("memberCount")) {
                memberCount = msgJSON.getInteger("memberCount");
            }
            return memberCount;
        } catch (Exception e) {
            log.error("getNodeMemberByCreateDateRange接口请求失败：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public List<String> getNodeMemberEmailList(String currentUserEmail) {
        if (StrUtil.isBlank(currentUserEmail)) {
            return null;
        }
        try {
            List<String> emailList = new ArrayList<>();
            String url = constants.getUrl() + "/apiTools/getNodeMemberEmailList";
            log.info("Info in getNodeMemberEmailList: url = {}", url);

            String result = getRequestData(url, currentUserEmail);
            String msg = getMsgBody(result);
            if (StrUtil.isBlank(msg)) {
                return null;
            }
            JSONObject emailListJson = JSON.parseObject(msg);
            if (emailListJson.containsKey("email_list")) {
                JSONArray emailListJsonResult = emailListJson.getJSONArray("email_list");
                emailList = emailListJsonResult.stream().map(Object::toString).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
            }
            return emailList;
        } catch (Exception e) {
            log.error("getNodeMemberEmailList接口请求失败：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public List<MemberDTO> getNodeMemberInfoList(String currentUserEmail) {
        if (StrUtil.isBlank(currentUserEmail)) {
            return null;
        }
        try {
            List<MemberDTO> memberList = new ArrayList<>();
            String url = constants.getUrl() + "/apiTools/getNodeMemberInfoList";
            log.info("Info in getNodeMemberInfoList: url = {}", url);

            String result = getRequestData(url, currentUserEmail);
            String msg = getMsgBody(result);
            if (StrUtil.isBlank(msg)) {
                return null;
            }
            JSONObject memberListJSOn = JSON.parseObject(msg);
            if (memberListJSOn.containsKey("member_list")) {
                JSONArray emailListJsonResult = memberListJSOn.getJSONArray("member_list");
                memberList = emailListJsonResult.stream().map(x -> com.alibaba.fastjson2.JSON.parseObject(x.toString(), MemberDTO.class)).distinct().collect(Collectors.toList());
            }
            return memberList;
        } catch (Exception e) {
            log.error("getNodeMemberInfoList接口请求失败：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public String getRequestData(String url, String currentUserEmail) throws Exception {
        URL realUrl = new URL(url);
        URLConnection connection = realUrl.openConnection();
        connection.setRequestProperty("token", AESUtil.encrypt(currentUserEmail));
        connection.setRequestProperty("subsystem", AESUtil.encrypt("node"));
        connection.setRequestProperty("accept", "*/*");
        connection.setRequestProperty("connection", "Keep-Alive");
        connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
        connection.setConnectTimeout(10 * 1000);
        connection.setReadTimeout(30 * 1000);
        connection.connect();

        Map<String, List<String>> map = connection.getHeaderFields();
        // 遍历所有的响应头字段
        for (String key : map.keySet()) {
            log.debug("{}--->{}", key, map.get(key));
        }
        InputStream inputStream = null;
        BufferedReader bufferedReader = null;
        try {
            inputStream = connection.getInputStream();
            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            return bufferedReader.lines().collect(Collectors.joining());
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(bufferedReader);
        }
    }

    /**
     * 解析返回的加密字符串
     */
    private String getMsgBody(String msg) throws Exception {
        if (StrUtil.isBlank(msg)) {
            return null;
        }
        String resultMsg = getMsg(msg);
        if (StrUtil.isBlank(resultMsg)) {
            return null;
        }
        return AESUtil.decrypt(resultMsg, null, constants.getQueryKey());
    }

    private String getMsg(String result) {
        JSONObject responseJson = JSON.parseObject(result);
        if (!responseJson.containsKey("success")) {
            return null;
        }
        if (!responseJson.getBoolean("success")) {
            return null;
        }
        if (responseJson.containsKey("msg")) {
            return responseJson.getString("msg");
        }
        return null;
    }


    private final static Map<String, String> memberIdToEmailMap = new LinkedHashMap<>();

    /**
     * 加载node中所有用户的数据
     * 启动时执行，每周一早上9点执行
     */
    @PostConstruct
    @Scheduled(cron = "0 0 9 * * Mon")
    public void loadAllMemberIdToEmailMap() {
        List<MemberDTO> allMember = getNodeMemberInfoList("FtpUser");
        for (MemberDTO memberDTO : allMember) {
            memberIdToEmailMap.put(memberDTO.getId(), memberDTO.getEmail());
        }
    }

    public Map<String, String> getMemberIdToEmailMapByMemberIds(Collection<String> memberIds, String currentUserEmail) {
        Map<String, String> result = new LinkedHashMap<>();
        if (StrUtil.isBlank(currentUserEmail)) {
            return result;
        }
        if (CollUtil.isEmpty(memberIds)) {
            return result;
        }

        for (String memberId : memberIds) {
            if (StrUtil.isBlank(memberId)) {
                continue;
            }
            if (memberIdToEmailMap.containsKey(memberId)) {
                result.put(memberId, memberIdToEmailMap.get(memberId));
            } else {
                MemberDTO member = getOneMemberByMemberId(memberId, currentUserEmail);
                if (member != null) {
                    memberIdToEmailMap.put(memberId, member.getEmail());
                    result.put(memberId, member.getEmail());
                } else {
                    memberIdToEmailMap.put(memberId, null);
                    result.put(memberId, null);
                }
            }
        }
        return result;
    }
}
