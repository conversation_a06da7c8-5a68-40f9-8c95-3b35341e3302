package org.biosino.member;

import org.biosino.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@EnableCustomSwagger2
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class NodeMemberApplication {
    public static void main(String[] args) {
        SpringApplication.run(NodeMemberApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  NODE-CAS用户服务模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
