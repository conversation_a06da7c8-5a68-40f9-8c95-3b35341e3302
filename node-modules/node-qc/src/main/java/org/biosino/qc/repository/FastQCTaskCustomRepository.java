package org.biosino.qc.repository;

import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.qc.dto.FastQCTaskQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface FastQCTaskCustomRepository {
    Page<FastQCTask> findFastQCTaskPage(FastQCTaskQueryDTO queryDTO);

    Map<String, Long> getQcStatusStatByDataNoIn(List<String> dataList);
}
