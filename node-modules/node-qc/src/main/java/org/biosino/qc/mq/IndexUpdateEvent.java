package org.biosino.qc.mq;

import org.biosino.common.core.enums.AuthorizeType;
import org.springframework.context.ApplicationEvent;
import org.springframework.util.CollectionUtils;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class IndexUpdateEvent extends ApplicationEvent {

    private AuthorizeType type;
    private String[] typeIds;

    public IndexUpdateEvent(Object source, AuthorizeType type, String... typeIds) {
        super(source);
        this.type = type;
        this.typeIds = typeIds;
    }

    public IndexUpdateEvent(Object source, AuthorizeType type, Collection<String> typeIds) {
        super(source);
        this.type = type;
        this.typeIds = CollectionUtils.isEmpty(typeIds) ? new String[]{} : typeIds.toArray(new String[]{});
    }

    public AuthorizeType getType() {
        return type;
    }

    public String[] getTypeIds() {
        return typeIds;
    }
}
