package org.biosino.qc.controller;

import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.mongo.dto.BaseQuery;
import org.biosino.qc.service.StatisticsService;
import org.biosino.qc.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 数据提交-数据列表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/statistics")
public class StatisticsController extends BaseController {

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 1 Data Submission Volume
     * 统计规则：提交且已通过审核的数据量情况
     */
    @GetMapping("/dataVolume")
    public AjaxResult dataVolume(BaseQuery dto) {
        DataVolumeVO vo = statisticsService.dataVolume(dto);
        return AjaxResult.success(vo);
    }

    /**
     * 2 Audit Overview
     * 统计规则：
     * Submission Review Times: audit_log的条数 / 提交的总数（audit_log的条数 + 待审核的submission记录数）
     * Implementation Audit Frequency: audit_log中各num总数 /  提交的总数（audit_log中各num总数 + 待审核submission记录中各num总数）
     * Success Submission: submission中成功的记录数 / submission中除了删除的记录数
     */
    @GetMapping("/auditOverview")
    public AjaxResult auditOverview(BaseQuery dto) {
        AuditOverviewVO vo = statisticsService.auditOverview(dto);
        return AjaxResult.success(vo);
    }

    /**
     * 3 Audit Detail 堆叠图
     */
    @GetMapping("/auditDetailData")
    public AjaxResult auditDetailData(BaseQuery dto) {
        List<AuditDetailVO> vo = statisticsService.auditDetailData(dto);
        return AjaxResult.success(vo);
    }

    /**
     * 导出 Audit Detail数据
     */
    @PostMapping("/exportAuditDetailData")
    public void exportAuditDetailData(HttpServletRequest request, HttpServletResponse response) throws IOException {
        statisticsService.exportAuditData(request, response);
    }

    /**
     * 4 Audit Data 双层饼图
     */
    @GetMapping("/auditData")
    public AjaxResult auditData(BaseQuery dto) {
        AuditDataVO vo = statisticsService.auditData(dto);
        return AjaxResult.success(vo);
    }

    /**
     * 5 Quality Control Log
     */
    @GetMapping("/qualityLog")
    public AjaxResult qualityLog(BaseQuery dto) {
        QualityLogVO vo = statisticsService.qualityLog(dto);
        return AjaxResult.success(vo);
    }
}
