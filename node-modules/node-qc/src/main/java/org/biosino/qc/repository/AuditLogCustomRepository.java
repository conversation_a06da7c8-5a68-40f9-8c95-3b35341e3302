package org.biosino.qc.repository;

import org.biosino.common.mongo.dto.BaseQuery;
import org.biosino.common.mongo.entity.AuditLog;
import org.biosino.qc.dto.AuditLogDTO;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface AuditLogCustomRepository {

    PageImpl<AuditLog> findAllPage(AuditLogDTO dto);

    List<AuditLog> findAllNum(BaseQuery dto);

    List<AuditLog> findAll(BaseQuery dto);

    long countByStatus(BaseQuery dto, String status);

    long countTotalByStatus(BaseQuery dto, String status);
}
