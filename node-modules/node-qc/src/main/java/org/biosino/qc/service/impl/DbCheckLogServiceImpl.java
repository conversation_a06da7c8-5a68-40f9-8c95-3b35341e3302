package org.biosino.qc.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.qc.dto.DbCheckLogQueryDTO;
import org.biosino.qc.mapper.DbCheckLogMapper;
import org.biosino.qc.service.IDbCheckLogService;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.domain.DbCheckLog;
import org.biosino.system.api.dto.MemberDTO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Li
 * @date 2024/5/11
 */
@Service
@RequiredArgsConstructor
public class DbCheckLogServiceImpl extends ServiceImpl<DbCheckLogMapper, DbCheckLog> implements IDbCheckLogService {

    private final RemoteMemberService remoteMemberService;

    @Override
    public List<DbCheckLog> selectLogList(DbCheckLogQueryDTO queryDTO) {
        if (StrUtil.isNotBlank(queryDTO.getCreatorEmail())) {
            R<MemberDTO> rMember = remoteMemberService.getMemberInfoByEmail(queryDTO.getCreatorEmail(), "FtpUser", SecurityConstants.INNER);
            if (R.isError(rMember) || rMember.getData() == null) {
                throw new ServiceException(StrUtil.format("用户 {} 未找到", queryDTO.getCreatorEmail()));
            }
            queryDTO.setCreator(rMember.getData().getId());
        }

        String typeNostr = queryDTO.getTypeNos();
        List<String> typeNos = new ArrayList<>();
        if (StrUtil.isNotBlank(typeNostr)) {
            typeNos = CollUtil.newArrayList(typeNostr.split("\\s+"));
        }

        LambdaQueryWrapper<DbCheckLog> qw = Wrappers.<DbCheckLog>lambdaQuery()
                .eq(StrUtil.isNotBlank(queryDTO.getType()), DbCheckLog::getType, queryDTO.getType())
                .eq(StrUtil.isNotBlank(queryDTO.getCreator()), DbCheckLog::getCreator, queryDTO.getCreator())
                .eq(StrUtil.isNotBlank(queryDTO.getSubjectType()), DbCheckLog::getSubjectType, queryDTO.getSubjectType())
                .like(StrUtil.isNotBlank(queryDTO.getTags()), DbCheckLog::getTags, queryDTO.getTags())
                .in(CollUtil.isNotEmpty(typeNos), DbCheckLog::getTypeId, typeNos)
                .ge(queryDTO.getBeginTime() != null, DbCheckLog::getCreateDate, queryDTO.getBeginTime() != null ? DateUtil.beginOfDay(queryDTO.getBeginTime()) : null)
                .le(queryDTO.getEndTime() != null, DbCheckLog::getCreateDate, queryDTO.getEndTime() != null ? DateUtil.endOfDay(queryDTO.getEndTime()) : null)
                .last(queryDTO.getLimit() != null, " limit " + queryDTO.getLimit());
        if (StrUtil.isNotBlank(queryDTO.getFilterField())) {
            if (queryDTO.getHasFilterField()) {
                qw.eq(DbCheckLog::getField, queryDTO.getFilterField());
            } else {
                qw.ne(DbCheckLog::getField, queryDTO.getFilterField());
            }
        }
        return this.list(qw);

    }
}
