package org.biosino.qc.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AuditOverviewVO {

    // audit_log的条数
    private long subReviews = 0;
    // 提交的总数（audit_log的条数 + 待审核的submission记录数）
    private long subTotal = 0;

    // audit_log中各num总数
    private long frequencyItem = 0;
    // 提交的总数（audit_log中各num总数 + 待审核submission记录中各num总数）
    private long frequencyTotal = 0;

    // submission中成功的记录数
    private long subSuccess = 0;
    // submission中除了删除的记录数
    private long submissionTotal = 0;

}
