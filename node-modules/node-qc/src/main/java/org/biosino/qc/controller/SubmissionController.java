package org.biosino.qc.controller;

import org.biosino.common.core.domain.R;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.log.annotation.Log;
import org.biosino.common.log.enums.BusinessType;
import org.biosino.common.log.enums.SytemEnum;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.qc.dto.FastQCTaskQueryDTO;
import org.biosino.qc.dto.RejectDTO;
import org.biosino.qc.dto.SubmissionDTO;
import org.biosino.qc.service.SubmissionService;
import org.biosino.qc.vo.SubmissionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据提交-数据列表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/submission")
public class SubmissionController extends BaseController {

    @Autowired
    private SubmissionService submissionService;

    /**
     * 查询当前审核员审核记录
     */
    @GetMapping("/list")
    public TableDataInfo list(SubmissionDTO dto) {
        Page<SubmissionVO> page = submissionService.list(dto);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 开始审核
     */
    @PostMapping("/startReview/{subNo}")
    @Log(system = SytemEnum.QC, businessType = BusinessType.UPDATE, module1 = "Submission Audit", desc = "Start Review")
    public R startReview(@PathVariable String subNo) {
        submissionService.startReview(subNo);
        return R.ok();
    }

    /**
     * 审核通过
     */
    @PostMapping("/pass/{subNo}")
    @Log(system = SytemEnum.QC, businessType = BusinessType.UPDATE, module1 = "Submission Audit", desc = "Pass")
    public R pass(@PathVariable String subNo) {
        String pass = submissionService.pass(subNo);
        return R.ok(pass);
    }

    /**
     * 驳回
     */
    @PostMapping("/reject")
    @Log(system = SytemEnum.QC, businessType = BusinessType.UPDATE, module1 = "Submission Audit", desc = "Reject")
    public AjaxResult reject(@RequestBody RejectDTO rejectDTO) {
        submissionService.reject(rejectDTO);
        return AjaxResult.success();
    }

    /**
     * 查询当前submission下fastqc的情况
     */
    @GetMapping("/getDataQCInfoList")
    public TableDataInfo getDataQCInfoList(@Validated FastQCTaskQueryDTO queryDTO) {
        Page<FastQCTask> page = submissionService.getDataQCInfoList(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 查询当前submission下fastqc状态统计
     */
    @GetMapping("/getQcStatusStat")
    public AjaxResult getQCStatData(String subNo) {
        Map<String, Long> result = submissionService.getQcStatusStat(subNo);
        return AjaxResult.success(result);
    }

    /**
     * 查询当前submission下bam的情况
     */
    @GetMapping("/getSamToolQcList")
    public TableDataInfo getSamToolQcList(@Validated FastQCTaskQueryDTO queryDTO) {
        Page<SamToolTask> page = submissionService.getSamToolInfoList(queryDTO);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }

    /**
     * 查询当前submission下samtool状态统计
     */
    @GetMapping("/getSamToolQcStatusStat")
    public AjaxResult getSamToolQcStatusStat(String subNo) {
        Map<String, Long> result = submissionService.getSamToolQcStatusStat(subNo);
        return AjaxResult.success(result);
    }
}
