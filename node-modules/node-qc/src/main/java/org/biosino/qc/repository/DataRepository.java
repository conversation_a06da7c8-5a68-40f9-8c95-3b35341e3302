package org.biosino.qc.repository;

import org.biosino.common.mongo.entity.Data;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface DataRepository extends MongoRepository<Data, String>, DataCustomRepository {
    Optional<Data> findByDatNo(String datNo);

    List<Data> findAllByRunNoIn(Collection<String> runNos);

    List<Data> findAllByDatNoIn(Collection<String> dataNos);

}
