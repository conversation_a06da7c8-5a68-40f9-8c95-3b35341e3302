package org.biosino.qc.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.mongo.entity.Data;
import org.biosino.qc.repository.DataCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

/**
 * <AUTHOR>
 * @date 2024/1/4
 */
@RequiredArgsConstructor
public class DataCustomRepositoryImpl implements DataCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Data findByNo(String datNo) {
        return mongoTemplate.findOne(Query.query(Criteria.where("dat_no").is(datNo)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())), Data.class);
    }

}
