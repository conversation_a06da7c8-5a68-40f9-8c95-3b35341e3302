package org.biosino.qc.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.mongo.entity.AuditLog;
import org.biosino.qc.dto.AuditLogDTO;
import org.biosino.qc.repository.AuditLogRepository;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuditLogService {

    private final AuditLogRepository auditLogRepository;

    public PageImpl<AuditLog> list(AuditLogDTO dto) {
        return auditLogRepository.findAllPage(dto);
    }

}
