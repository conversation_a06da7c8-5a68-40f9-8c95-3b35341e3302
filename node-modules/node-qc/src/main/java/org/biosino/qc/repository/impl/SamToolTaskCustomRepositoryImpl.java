package org.biosino.qc.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.qc.dto.FastQCTaskQueryDTO;
import org.biosino.qc.repository.SamToolTaskCustomRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR> <PERSON>
 * @date 2024/8/28
 */
@RequiredArgsConstructor
public class SamToolTaskCustomRepositoryImpl implements SamToolTaskCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Page<SamToolTask> findPage(FastQCTaskQueryDTO queryDTO) {
        if (CollUtil.isEmpty(queryDTO.getDataNos())) {
            return new PageImpl<>(new ArrayList<>());
        }
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("data_no").in(queryDTO.getDataNos()));

        if (StrUtil.isNotBlank(queryDTO.getDataNo())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getDataNo() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(new Criteria().orOperator(Criteria.where("data_no").regex(pattern),
                    Criteria.where("data_file_name").regex(pattern)));
        }
        if (StrUtil.isNotBlank(queryDTO.getStatus())) {
            criteriaList.add(Criteria.where("status").is(queryDTO.getStatus()));
        }

        Query query = new Query(new Criteria().andOperator(criteriaList));

        // 查询数据量
        long total = mongoTemplate.count(query, SamToolTask.class);
        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<SamToolTask> content = mongoTemplate.find(query, SamToolTask.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    @Override
    public Map<String, Long> getStatusStatByDataNoIn(List<String> dataNos) {
        if (CollUtil.isEmpty(dataNos)) {
            Collections.emptyMap();
        }
        LinkedHashMap<String, Long> result = new LinkedHashMap<>();
        for (FastQCTaskStatusEnum value : FastQCTaskStatusEnum.values()) {
            result.put(value.name(), countByStatusAndDataNoIn(value.name(), dataNos));
        }
        return result;

    }

    private Long countByStatusAndDataNoIn(String status, List<String> dataNos) {
        return mongoTemplate.count(new Query(Criteria.where("status").is(status).and("data_no").in(dataNos)),
                SamToolTask.class);
    }
}
