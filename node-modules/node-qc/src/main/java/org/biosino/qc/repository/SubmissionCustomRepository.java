package org.biosino.qc.repository;

import org.biosino.common.mongo.dto.BaseQuery;
import org.biosino.common.mongo.entity.AuditLog;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.qc.dto.SubmissionDTO;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SubmissionCustomRepository {

    PageImpl<Submission> findAllPage(SubmissionDTO dto);

    List<Submission> findAllNum(BaseQuery dto);

    long countByStatus(BaseQuery dto, String status);

    long countByNotStatus(BaseQuery dto, String status);

    long countTotalByStatus(BaseQuery dto, String status);

}
