package org.biosino.qc.controller;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.qc.dto.DbCheckLogQueryDTO;
import org.biosino.qc.service.IDbCheckLogService;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.RemoteToolsService;
import org.biosino.system.api.domain.DbCheckLog;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据一致性检查
 *
 * <AUTHOR> Li
 * @date 2024/5/13
 */
@RestController
@RequestMapping("/dataIntegralityCheck")
@RequiredArgsConstructor
public class DataIntegralityCheckController extends BaseController {
    private final IDbCheckLogService dbCheckLogService;
    private final RemoteToolsService remoteToolsService;
    private final RemoteMemberService remoteMemberService;

    @RequestMapping("/checkLogList")
    public TableDataInfo checkLogList(DbCheckLogQueryDTO queryDTO) {
        startPage();
        List<DbCheckLog> list = dbCheckLogService.selectLogList(queryDTO);
        List<String> memberIds = list.stream().map(DbCheckLog::getCreator).distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        Map<String, String> memberIdToEmailMap = remoteMemberService.getMemberIdToEmailMapByMemberIds(memberIds, "FtpUser", SecurityConstants.INNER).getData();

        for (DbCheckLog log : list) {
            log.setCreatorEmail(memberIdToEmailMap.getOrDefault(log.getCreator(), null));
        }

        return getDataTable(list);
    }

    @RequestMapping("/checkAllDB")
    public AjaxResult checkAllDB() {
        R r = remoteToolsService.checkAllDB(SecurityConstants.INNER);
        if (R.isError(r)) {
            return AjaxResult.error(r.getMsg());
        }
        return AjaxResult.success();
    }

    @RequestMapping("/export")
    public void export(HttpServletResponse response, DbCheckLogQueryDTO queryDTO) {
        queryDTO.setLimit(50000);
        List<DbCheckLog> list = dbCheckLogService.selectLogList(queryDTO);

        List<String> memberIds = list.stream().map(DbCheckLog::getCreator).distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        Map<String, String> memberIdToEmailMap = remoteMemberService.getMemberIdToEmailMapByMemberIds(memberIds, "FtpUser", SecurityConstants.INNER).getData();

        for (DbCheckLog log : list) {
            log.setCreatorEmail(memberIdToEmailMap.getOrDefault(log.getCreator(), null));
        }

        ExcelUtil<DbCheckLog> util = new ExcelUtil<>(DbCheckLog.class);
        util.exportExcel(response, list, "sheet1");
    }
}
