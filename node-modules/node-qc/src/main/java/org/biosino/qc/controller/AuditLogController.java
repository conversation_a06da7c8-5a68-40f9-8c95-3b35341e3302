package org.biosino.qc.controller;

import org.biosino.common.core.web.controller.BaseController;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.mongo.entity.AuditLog;
import org.biosino.qc.dto.AuditLogDTO;
import org.biosino.qc.service.AuditLogService;
import org.biosino.qc.vo.SubmissionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * 数据提交-数据列表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log")
public class AuditLogController extends BaseController {

    @Autowired
    private AuditLogService auditLogService;

    /**
     * 查询当前审核员审核记录
     */
    @GetMapping("/list")
    public TableDataInfo list(AuditLogDTO dto) {
        Page<AuditLog> page = auditLogService.list(dto);
        return new TableDataInfo(page.getContent(), (int) page.getTotalElements());
    }
}
