package org.biosino.qc.mq;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.ArrayUtils;
import org.biosino.common.core.enums.ExchangeEnum;
import org.biosino.common.core.enums.RouterKeyEnum;
import org.biosino.es.api.msg.IndexUpdateMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Component
public class IndexUpdateListener implements ApplicationListener<IndexUpdateEvent> {

    private final Logger log = LoggerFactory.getLogger(IndexUpdateListener.class);

    private final RabbitTemplate rabbitTemplate;

    public IndexUpdateListener(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    @Override
    public void onApplicationEvent(IndexUpdateEvent event) {

        if (event.getType() == null || ArrayUtils.isEmpty(event.getTypeIds())) {
            return;
        }

        IndexUpdateMsg indexUpdateMsg = new IndexUpdateMsg();
        indexUpdateMsg.setType(event.getType().name());
        indexUpdateMsg.setTypeIds(Arrays.asList(event.getTypeIds()));

        this.rabbitTemplate.convertAndSend(ExchangeEnum.node_delay.name(), RouterKeyEnum.es_index_key.name(), indexUpdateMsg);

        log.info(String.format("Sender: content=%s; exchange=%s; exchangeKey=%s", JSON.toJSONString(indexUpdateMsg), ExchangeEnum.node_delay.name(), RouterKeyEnum.es_index_key.name()));
    }
}
