package org.biosino.qc.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.MailTemplate;
import org.biosino.common.core.enums.SubmissionDataTypeEnum;
import org.biosino.common.core.enums.SubmissionStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.AuditLog;
import org.biosino.common.mongo.entity.FastQCTask;
import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.redis.service.RedisService;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.qc.dto.FastQCTaskQueryDTO;
import org.biosino.qc.dto.RejectDTO;
import org.biosino.qc.dto.SubmissionDTO;
import org.biosino.qc.enums.AuditLogEnum;
import org.biosino.qc.repository.AuditLogRepository;
import org.biosino.qc.repository.FastQCTaskRepository;
import org.biosino.qc.repository.SamToolTaskRepository;
import org.biosino.qc.repository.SubmissionRepository;
import org.biosino.qc.vo.SubmissionVO;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.RemoteNotificationService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.dto.SendEmailDTO;
import org.biosino.upload.api.RemoteSubmissionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubmissionService {

    private final AuditLogRepository auditLogRepository;
    private final SubmissionRepository submissionRepository;
    private final FastQCTaskRepository fastQCTaskRepository;
    private final RemoteMemberService remoteMemberService;
    private final RemoteSubmissionService remoteSubmissionService;
    private final RemoteNotificationService remoteNotificationService;
    private final RedisService redisService;

    private final static String PASS_SUBMISSION_TASK_KEY = "pass_submission_task_key_";
    private final SamToolTaskRepository samToolTaskRepository;

    public Page<SubmissionVO> list(SubmissionDTO dto) {

        if (StrUtil.isNotBlank(dto.getCreatorEmail())) {
            String memberId = getMemberInfoByEmail(dto.getCreatorEmail().trim());
            dto.setCreator(memberId);
        }

        PageImpl<Submission> page = submissionRepository.findAllPage(dto);

        List<String> creators = page.getContent().stream().map(Submission::getCreator).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 获取memberId到email对映射关系
        Map<String, String> memberIdToEmailMap = getMemberIdToEmailMap(creators);

        return page.map(submission -> {
            SubmissionVO submissionVO = new SubmissionVO();
            BeanUtil.copyProperties(submission, submissionVO);

            Submitter submitter = submission.getSubmitter();
            String submitterName = StrUtil.join(" ", submitter.getFirstName(), submitter.getLastName());
            submissionVO.setSubmitter(submitterName);

            submissionVO.setCreatorEmail(memberIdToEmailMap.get(submission.getCreator()));

            String lockKey = PASS_SUBMISSION_TASK_KEY + submission.getSubNo();
            Boolean inStorage = redisService.hasKey(lockKey);
            submissionVO.setProcessing(inStorage);
            return submissionVO;
        });
    }

    public String getMemberInfoByEmail(String email) {
        R<MemberDTO> rMember = remoteMemberService.getMemberInfoByEmail(email, "FtpUser", SecurityConstants.INNER);
        if (R.isError(rMember) || rMember.getData() == null) {
            throw new ServiceException(StrUtil.format("用户 {} 未找到", email));
        }
        return rMember.getData().getId();
    }

    public Map<String, String> getMemberIdToEmailMap(List<String> memberIds) {
        Map<String, String> memberIdToEmailMap = new HashMap<>();
        for (String memberId : memberIds) {
            MemberDTO member = getOneMemberByMemberId(memberId);
            memberIdToEmailMap.put(memberId, member.getEmail());
        }
        return memberIdToEmailMap;
    }

    public MemberDTO getOneMemberByMemberId(String memberId) {
        R<MemberDTO> rMember = remoteMemberService.getOneMemberByMemberId(memberId, "FtpUser", SecurityConstants.INNER);
        if (R.isError(rMember)) {
            throw new ServiceException(rMember.getMsg());
        }
        return rMember.getData();
    }

    private Submission getSubmission(String subNo) {
        return submissionRepository.findTopBySubNo(subNo).orElseThrow(() -> new ServiceException("未找到提交记录"));
    }

    /**
     * 审核通过
     */
    public String pass(String subNo) {
        Submission submission = getSubmission(subNo);

        String status = submission.getStatus();
        if (!SubmissionStatusEnum.reviewing.name().equals(status)) {
            throw new ServiceException("当前提交状态未处于审核中，无法通过");
        }

        Long oldAuditorId = submission.getAuditorId();
        Long auditorId = SecurityUtils.getUserId();

        // 如果有auditorId则要判断再次审核的人员是否是当前用户
        if (oldAuditorId != null && !oldAuditorId.equals(auditorId)) {
            return "没有权限";
        }

        // 调用upload服务，通过提交
        R pass = remoteSubmissionService.pass(subNo, auditorId, SecurityUtils.getUsername(), SecurityConstants.INNER);
        if (R.isError(pass)) {
            log.error(pass.getMsg());
            throw new ServiceException(pass.getMsg());
        }
        Object passData = pass.getData();
        if (passData != null) {
            return passData.toString();
        }

        return null;
    }

    /**
     * 驳回
     */
    public void reject(RejectDTO rejectDTO) {
        String subNo = rejectDTO.getSubNo();
        Submission submission = getSubmission(subNo);

        String status = submission.getStatus();
        if (!SubmissionStatusEnum.reviewing.name().equals(status)) {
            throw new ServiceException("当前提交状态未处于审核中，无法拒绝");
        }

        Long auditorId = submission.getAuditorId();
        Long userId = SecurityUtils.getUserId();

        // 如果有auditorId则要判断再次审核的人员是否是当前用户
        if (auditorId != null && !auditorId.equals(userId)) {
            throw new ServiceException("没有权限");
        }

        submission = getSubmission(subNo);
        submission.setAuditorId(userId);
        submission.setAuditor(SecurityUtils.getUsername());
        submission.setRejectReason(rejectDTO.getReason());
        submission.setStatus(SubmissionStatusEnum.rejected.name());
        submission.setAuditTime(new Date());

        submissionRepository.save(submission);

        // 记录审核日志
        AuditLog auditLog = new AuditLog();

        BeanUtil.copyProperties(submission, auditLog);

        auditLog.setId(null);
        auditLog.setStatus(AuditLogEnum.reject.name());
        auditLog.setCreateTime(new Date());
        auditLogRepository.insert(auditLog);

        // 邮件告知用户审核未通过
        Submission finalSubmission = submission;
        ThreadUtil.execAsync(() -> {
            R<MemberDTO> nodeUser = remoteMemberService.getOneMemberByMemberId(finalSubmission.getCreator(), "FtpUser", SecurityConstants.INNER);
            if (R.isError(nodeUser)) {
                throw new ServiceException("用户服务异常，请联系管理员");
            }

            Map<String, Object> params = new HashMap<>();
            params.put("lastName", nodeUser.getData().getLastName());
            params.put("submissionId", finalSubmission.getSubNo());

            try {
                final SendEmailDTO sendEmailDTO = new SendEmailDTO();
                sendEmailDTO.setToEmail(nodeUser.getData().getEmail());
                sendEmailDTO.setParams(params);
                sendEmailDTO.setMailTemplate(MailTemplate.Audit_Reject);
                remoteNotificationService.sendEmail(sendEmailDTO, SecurityConstants.INNER);
            } catch (Exception e) {
                log.error("邮件发送失败 email: {} mailTemplate:{} params: {}", nodeUser.getData().getEmail(), MailTemplate.Audit_Reject, JSONObject.toJSONString(params), e);
            }
        });
    }

    public Page<FastQCTask> getDataQCInfoList(FastQCTaskQueryDTO queryDTO) {
        Submission submission = getSubmission(queryDTO.getSubNo());

        List<String> dataList = new ArrayList<>();
        // 如果submission是rawData才有FastQc
        if (StrUtil.equals(SubmissionDataTypeEnum.rawData.name(), submission.getDataType())) {
            if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
                dataList.addAll(submission.getRawDataNos());
            }
            if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
                dataList.addAll(submission.getRawDataMultipleNos());
            }
        }
        queryDTO.setDataNos(dataList);
        return fastQCTaskRepository.findFastQCTaskPage(queryDTO);
    }

    public Page<SamToolTask> getSamToolInfoList(FastQCTaskQueryDTO queryDTO) {
        Submission submission = getSubmission(queryDTO.getSubNo());

        List<String> dataList = new ArrayList<>();
        // 如果submission是rawData才有FastQc
        if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
            dataList.addAll(submission.getRawDataNos());
        }
        if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
            dataList.addAll(submission.getRawDataMultipleNos());
        }
        if (CollUtil.isNotEmpty(submission.getAnalysisDataNos())) {
            dataList.addAll(submission.getAnalysisDataNos());
        }
        if (CollUtil.isNotEmpty(submission.getAnalDataMultipleNos())) {
            dataList.addAll(submission.getAnalDataMultipleNos());
        }
        queryDTO.setDataNos(dataList);
        return samToolTaskRepository.findPage(queryDTO);
    }


    public Map<String, Long> getQcStatusStat(String subNo) {
        if (StrUtil.isBlank(subNo)) {
            throw new ServiceException("参数错误");
        }
        Submission submission = getSubmission(subNo);

        List<String> dataList = new ArrayList<>();
        // 如果submission是rawData才有FastQc
        if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
            dataList.addAll(submission.getRawDataNos());
        }
        if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
            dataList.addAll(submission.getRawDataMultipleNos());
        }
        if (CollUtil.isNotEmpty(submission.getAnalysisDataNos())) {
            dataList.addAll(submission.getAnalysisDataNos());
        }
        if (CollUtil.isNotEmpty(submission.getAnalDataMultipleNos())) {
            dataList.addAll(submission.getAnalDataMultipleNos());
        }
        return fastQCTaskRepository.getQcStatusStatByDataNoIn(dataList);
    }

    public Map<String, Long> getSamToolQcStatusStat(String subNo) {
        if (StrUtil.isBlank(subNo)) {
            throw new ServiceException("参数错误");
        }
        Submission submission = getSubmission(subNo);

        List<String> dataList = new ArrayList<>();
        // 如果submission是rawData才有FastQc
        if (CollUtil.isNotEmpty(submission.getRawDataNos())) {
            dataList.addAll(submission.getRawDataNos());
        }
        if (CollUtil.isNotEmpty(submission.getRawDataMultipleNos())) {
            dataList.addAll(submission.getRawDataMultipleNos());
        }
        if (CollUtil.isNotEmpty(submission.getAnalysisDataNos())) {
            dataList.addAll(submission.getAnalysisDataNos());
        }
        if (CollUtil.isNotEmpty(submission.getAnalDataMultipleNos())) {
            dataList.addAll(submission.getAnalDataMultipleNos());
        }

        return samToolTaskRepository.getStatusStatByDataNoIn(dataList);
    }

    public void startReview(String subNo) {
        Submission submission = getSubmission(subNo);

        String status = submission.getStatus();
        if (!SubmissionStatusEnum.waiting.name().equals(status)) {
            throw new ServiceException("当前提交状态不在等待审核中，无法开始审核");
        }

        Long auditorId = submission.getAuditorId();
        Long userId = SecurityUtils.getUserId();

        // 如果有auditorId则要判断再次审核的人员是否是当前用户
        if (auditorId != null && !auditorId.equals(userId)) {
            throw new ServiceException("没有权限");
        }

        submission = getSubmission(subNo);
        submission.setAuditorId(userId);
        submission.setAuditor(SecurityUtils.getUsername());
        submission.setStatus(SubmissionStatusEnum.reviewing.name());
        submission.setAuditTime(new Date());

        submissionRepository.save(submission);

        // 记录审核日志
        AuditLog auditLog = new AuditLog();

        BeanUtil.copyProperties(submission, auditLog);

        auditLog.setId(null);
        auditLog.setStatus(AuditLogEnum.startReview.name());
        auditLog.setCreateTime(new Date());
        auditLogRepository.insert(auditLog);
    }
}
