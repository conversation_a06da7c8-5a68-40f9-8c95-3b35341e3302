package org.biosino.qc;

import org.biosino.common.security.annotation.EnableCustomConfig;
import org.biosino.common.security.annotation.EnableRyFeignClients;
import org.biosino.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Node 数据人工审核模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@ComponentScan(basePackages = {"org.biosino.common.mongo.entity", "org.biosino.qc"})
public class NodeQCApplication {
    public static void main(String[] args) {
        SpringApplication.run(NodeQCApplication.class, args);
        System.out.println("Node QC模块启动成功");
    }
}
