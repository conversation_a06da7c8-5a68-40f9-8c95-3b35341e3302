package org.biosino.qc.repository;

import org.biosino.common.mongo.entity.FastQCTask;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface FastQCTaskRepository extends MongoRepository<FastQCTask, String>, FastQCTaskCustomRepository {

    List<FastQCTask> findByDataNoIn(Collection<String> dataNos);
}
