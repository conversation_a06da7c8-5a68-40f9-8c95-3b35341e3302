package org.biosino.qc.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.dto.BaseQuery;
import org.biosino.common.mongo.entity.AuditLog;
import org.biosino.qc.dto.AuditLogDTO;
import org.biosino.qc.repository.AuditLogCustomRepository;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class AuditLogCustomRepositoryImpl implements AuditLogCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public PageImpl<AuditLog> findAllPage(AuditLogDTO dto) {

        List<Criteria> criteriaList = new ArrayList<>();

        if (StrUtil.isNotBlank(dto.getSubNo())) {
            criteriaList.add(Criteria.where("sub_no").regex(".*?" + dto.getSubNo() + ".*?", "i"));
        }

        if (StrUtil.isNotBlank(dto.getDataType())) {
            criteriaList.add(Criteria.where("data_type").is(dto.getDataType()));
        }

        if (StrUtil.isNotBlank(dto.getStatus())) {
            criteriaList.add(Criteria.where("status").is(dto.getStatus()));
        }

        if (dto.getAuditor() != null) {
            criteriaList.add(Criteria.where("auditor_id").is(dto.getAuditor()));
        }

        queryDate(dto, criteriaList);

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        Pageable pageable = dto.getPageable();
        // 加入查询条件
        long total = mongoTemplate.count(query, AuditLog.class);
        query.with(pageable);

        List<AuditLog> content = mongoTemplate.find(query, AuditLog.class);
        return new PageImpl<>(content, dto.getPageable(), total);
    }

    @Override
    public List<AuditLog> findAllNum(BaseQuery dto) {
        List<Criteria> criteriaList = new ArrayList<>();

        queryDate(dto, criteriaList);

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        // 只取需要的字段，防止内存溢出
        query.fields().include("proj_num").include("exp_num").include("sap_num").include("anal_num")
                .include("run_num").include("publish_num").include("data_num").include("data_size");

        return mongoTemplate.find(query, AuditLog.class);
    }

    @Override
    public List<AuditLog> findAll(BaseQuery dto) {
        List<Criteria> criteriaList = new ArrayList<>();

        queryDate(dto, criteriaList);

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }
        return mongoTemplate.find(query, AuditLog.class);
    }


    @Override
    public long countByStatus(BaseQuery dto, String status) {
        List<Criteria> criteriaList = new ArrayList<>();

        if (StrUtil.isNotBlank(status)) {
            criteriaList.add(Criteria.where("status").is(status));
        }

        queryDate(dto, criteriaList);

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }

        return mongoTemplate.count(query, AuditLog.class);
    }

    @Override
    public long countTotalByStatus(BaseQuery dto, String status) {
        List<Criteria> criteriaList = new ArrayList<>();

        if (StrUtil.isNotBlank(status)) {
            criteriaList.add(Criteria.where("status").is(status));
        }

        queryDate(dto, criteriaList);

        Query query = new Query();
        if (CollUtil.isNotEmpty(criteriaList)) {
            query = new Query(new Criteria().andOperator(criteriaList));
        }

        query.fields().include("total");

        List<AuditLog> all = mongoTemplate.find(query, AuditLog.class);

        if (CollUtil.isEmpty(all)) {
            return 0L;
        }

        return all.stream().mapToLong(AuditLog::getTotal).sum();
    }

    private static void queryDate(BaseQuery dto, List<Criteria> criteriaList) {
        if (ObjectUtil.isNotEmpty(dto.getBeginTime()) && ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where("create_time").gte(DateUtil.beginOfDay(dto.getBeginTime())).lte(DateUtil.endOfDay(dto.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getBeginTime())) {
            criteriaList.add(Criteria.where("create_time").gte(DateUtil.beginOfDay(dto.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(dto.getEndTime())) {
            criteriaList.add(Criteria.where("create_time").lte(DateUtil.endOfDay(dto.getEndTime())));
        }
    }
}
