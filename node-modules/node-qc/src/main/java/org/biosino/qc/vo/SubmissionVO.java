package org.biosino.qc.vo;

import lombok.Data;
import org.biosino.common.mongo.entity.other.RejectReason;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SubmissionVO {

    private String subNo;

    private String dataType;

    // 各种实体的数量
    private Integer projNum = 0;
    private Integer expNum = 0;
    private Integer sapNum = 0;
    private Integer runNum = 0;
    private Integer analNum = 0;
    private Integer dataNum = 0;

    private String projNo;
    private String expSingleNo;
    private String sapSingleNo;
    private String analSingleNo;

    private String submitter;
    private Long auditorId;
    private String auditor;
    private String creator;
    private String creatorEmail;

    private List<RejectReason> rejectReason;

    private Date createTime;
    private Date submitTime;
    private Date auditTime;
    private Date updateTime;
    private String status;

    // 数据入库中
    private boolean processing;

    private String publishId;
}
