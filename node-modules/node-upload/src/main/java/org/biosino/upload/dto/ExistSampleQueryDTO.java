package org.biosino.upload.dto;

import lombok.Data;
import org.biosino.common.mongo.dto.BaseQuery;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/30
 */
@Data
public class ExistSampleQueryDTO extends BaseQuery {
    private String sampleNo;
    private String sampleName;
    @NotBlank
    private String sampleType;
    private String organism;
    private String tissue;
    private String attrName;
    private String attrValue;
    private String creator;

    private List<String> sampleNos;
}
