package org.biosino.upload.vo;

import lombok.Data;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class SampleVO {

    private String sapNo;
    // sample type
    private String subjectType;
    private String name;
    private String description;
    private String protocol;

    private String organism;
    private String tissue;

    private Map<String, String> attributes = new LinkedHashMap<>();

    private List<Map<String, String>> customAttributes;

    private List<String> relatedLinks;

    private List<PublishVO> publish;

    private Date createDate;

}
