package org.biosino.upload.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PublishVO {

    private String id;

    // journal（需注意：一期页面的字段和数据库不对应）
    private String publication;

    private String doi;

    private String pmid;

    // title
    private String articleName;

    private String reference;

    private List<TypeInfo> typeInfoList;

    @Data
    public static class TypeInfo {

        private String type;
        private String typeNo;
        private String typeName;
        private String articleName;
        private String creator;
        private String email;
        private String username;
        private Date createDate;
    }

}
