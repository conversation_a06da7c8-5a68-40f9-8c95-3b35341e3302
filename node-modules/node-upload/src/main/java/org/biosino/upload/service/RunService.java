package org.biosino.upload.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.biosino.upload.api.vo.DeleteErrorMsgVO;
import org.biosino.upload.dto.ArchivedSelectQueryDTO;
import org.biosino.upload.dto.SelectOption;
import org.biosino.upload.repository.*;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/16
 */
@Service
@RequiredArgsConstructor
public class RunService extends BaseService {
    private final RunRepository runRepository;
    private final ExperimentRepository experimentRepository;
    private final ProjectRepository projectRepository;
    private final SampleRepository sampleRepository;
    private final DataRepository dataRepository;
    private final MessageSender messageSender;

    public List<SelectOption> getRunOptions() {
        List<Run> list = runRepository.findAllByCreator(SecurityUtils.getMemberId());
        return list.stream().map(x -> {
            SelectOption option = new SelectOption();
            option.setValue(x.getRunNo());
            if (x.getRunNo().startsWith(SequenceType.RUN.getPrefix())) {
                option.setLabel(x.getRunNo() + " (" + x.getName() + ")");
            } else {
                option.setLabel(x.getName());
            }
            option.setParams(new HashMap<String, Object>() {
                {
                    put("expNo", x.getExpNo());
                    put("sapNo", x.getSapNo());
                }
            });
            return option;
        }).collect(Collectors.toList());
    }

    public Page<SelectOption> getRunOptionsByPage(ArchivedSelectQueryDTO queryDTO) {
        queryDTO.setCreator(SecurityUtils.getMemberId());
        Page<Run> page = runRepository.findAllByPage(queryDTO);

        // 找出experiment信息
        Set<String> expNos = page.getContent().stream().map(Run::getExpNo).collect(Collectors.toSet());
        List<Experiment> experimentList = experimentRepository.findAllByExpNoIn(expNos);
        Map<String, Experiment> experimentMap = new HashMap<>();
        experimentList.forEach(x -> experimentMap.put(x.getExpNo(), x));

        // 找出project信息
        Set<String> projectNos = experimentList.stream().map(Experiment::getProjectNo).collect(Collectors.toSet());
        List<Project> projectList = projectRepository.findAllByProjectNoIn(projectNos);
        Map<String, Project> projectMap = new HashMap<>();
        projectList.forEach(x -> projectMap.put(x.getProjectNo(), x));

        Set<String> sapNos = page.getContent().stream().map(Run::getSapNo).collect(Collectors.toSet());
        List<Sample> sampleList = sampleRepository.findAllBySapNoIn(sapNos);
        Map<String, Sample> sampleMap = new HashMap<>();
        sampleList.forEach(x -> sampleMap.put(x.getSapNo(), x));

        return page.map(x -> {
            SelectOption option = new SelectOption();
            option.setValue(x.getRunNo());
            if (x.getRunNo().startsWith(SequenceType.RUN.getPrefix())) {
                option.setLabel(x.getRunNo() + " (" + x.getName() + ")");
            } else {
                option.setLabel(x.getName());
            }
            // 找出当前run下的Experiment,Project,,Sample
            Experiment experiment = experimentMap.get(x.getExpNo());
            Project project = projectMap.get(experiment.getProjectNo());
            Sample sample = sampleMap.get(x.getSapNo());

            SelectOption experimentOption = new SelectOption();
            experimentOption.setValue(experiment.getExpNo());
            if (experiment.getExpNo().startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                experimentOption.setLabel(experiment.getExpNo() + " (" + experiment.getName() + ")");
            } else {
                experimentOption.setLabel(experiment.getName());
            }

            SelectOption projectOption = new SelectOption();
            projectOption.setValue(project.getProjectNo());
            if (project.getProjectNo().startsWith(SequenceType.PROJECT.getPrefix())) {
                projectOption.setLabel(project.getProjectNo() + " (" + project.getName() + ")");
            } else {
                projectOption.setLabel(project.getName());
            }

            SelectOption sampleOption = new SelectOption();
            sampleOption.setValue(sample.getSapNo());
            if (sample.getSapNo().startsWith(SequenceType.SAMPLE.getPrefix())) {
                sampleOption.setLabel(sample.getSapNo() + " (" + sample.getName() + ")");
            } else {
                sampleOption.setLabel(sample.getName());
            }

            option.setParams(new HashMap<String, Object>() {
                {
                    put("project", projectOption);
                    put("experiment", experimentOption);
                    put("sample", sampleOption);
                }
            });
            return option;
        });
    }

    public String validateRunName(String name) {
        if (StringUtil.isBlank(name)) {
            return "The Run name cannot be blank";
        }
        boolean validated = runRepository.validateRunName(SecurityUtils.getMemberId(), name);
        if (!validated) {
            return "The Run name already exists";
        }
        return null;
    }

    public DeleteCheckResultVO deleteCheck(String runNo) {
        Run run = runRepository.findFirstByRunNo(runNo).orElseThrow(() -> new ServiceException("not found run: " + runNo));
        // 当前用户是否是owner
        if (!StrUtil.equals(run.getCreator(), SecurityUtils.getMemberId())) {
            throw new ServiceException("No Permission!");
        }

        // 找到dataList
        List<Data> dataList = dataRepository.findAllByRunNoIn(CollUtil.newArrayList(runNo));
        List<String> dataNos = dataList.stream().map(Data::getDatNo).distinct().collect(Collectors.toList());

        Set<DeleteErrorMsgVO> errors = new HashSet<>();

        if (run.getTempData() != null) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(runNo);
            vo.setNo(run.getTempData().getRunNo());
            vo.setType(AuthorizeType.run.name());
            vo.setName(run.getTempData().getName());
            vo.setSubNo(run.getTempData().getSubNo());
            errors.add(vo);
        }

        for (Data data : dataList) {
            if (data.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(runNo);
                vo.setNo(data.getTempData().getDatNo());
                vo.setType(AuthorizeType.data.name());
                vo.setName(data.getTempData().getName());
                vo.setSubNo(data.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        List<Data> tempDataList = dataRepository.findTempByRunNoIn(CollUtil.newArrayList(runNo));

        for (Data data : tempDataList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(runNo);
            vo.setNo(data.getTempData().getDatNo());
            vo.setType(AuthorizeType.data.name());
            vo.setName(data.getTempData().getName());
            vo.setSubNo(data.getTempData().getSubNo());
            errors.add(vo);
        }

        DeleteCheckResultVO resultVO = new DeleteCheckResultVO();
        resultVO.setRunNos(CollUtil.newArrayList(runNo));
        resultVO.setDataNos(dataNos);
        resultVO.setErrors(new ArrayList<>(errors));
        return resultVO;
    }

    public void deleteRunAll(String runNo) {
        Run run = runRepository.findFirstByRunNo(runNo).orElseThrow(() -> new ServiceException("not found run: " + runNo));
        // 当前用户是否是owner
        if (!StrUtil.equals(run.getCreator(), SecurityUtils.getMemberId())) {
            throw new ServiceException("No Permission!");
        }

        DeleteCheckResultVO checkResultVO = deleteCheck(runNo);
        // 删除数据
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("The Run cannot be deleted because it is associated with other data");
        }
        // 添加删除的日志
        addUserCenterDeleteLog(runNo, AuthorizeType.run.name(), checkResultVO);

        // 将visible_status更新为delete
        runRepository.updateToDeleteAllByRunNoIn(checkResultVO.getRunNos());
        dataRepository.updateToDeleteAllByDatNoIn(checkResultVO.getDataNos());

        // 通知删除es索引
        updateEsData(AuthorizeType.run.name(), runNo);
    }
}
