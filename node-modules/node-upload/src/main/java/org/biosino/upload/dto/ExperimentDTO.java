package org.biosino.upload.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class ExperimentDTO {
    @NotBlank
    private String subNo;

    private String expNo;

    @NotBlank
    private String projectNo;

    @NotBlank
    private String expType;

    @NotBlank
    private String name;
    private String description;
    private String protocol;

    private Map<String, Object> attributes = new LinkedHashMap<>();

    private List<String> relatedLinks;

    private List<PublishDTO> publish;

    public void setName(String name) {
        this.name = StrUtil.trim(name);
    }
}
