package org.biosino.upload.tool.excelparseutil.imp.sample;


import lombok.Getter;
import lombok.Setter;
import org.biosino.common.core.enums.dict.BaseAttrType;
import org.biosino.common.core.enums.dict.ExcelCellType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.upload.tool.excelparseutil.imp.ExcelParser;

import java.io.File;
import java.util.LinkedHashMap;
import java.util.Map;

import static org.biosino.common.mongo.entity.ExpSampleType.*;

/**
 * 样本excel解析
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SampleExcelParser extends ExcelParser {

    public SampleExcelParser(File excelFile, ExpSampleType expSampleType) throws ServiceException {
        super(excelFile, expSampleType);
    }

    @Override
    protected Map<String, Integer> getDefaultConf() {
        Map<String, Integer> param = new LinkedHashMap<>();
        param.put("sample", 12);
        return param;
    }

    @Override
    public Map<String, Map<String, ExcelCellType>> definedTitle() {

        Map<String, Map<String, ExcelCellType>> titles = new LinkedHashMap<>();

        //词条列表
        // sample
        Map<String, ExcelCellType> sample = new LinkedHashMap<>();
        sample.put(SAMPLE_ID, ExcelCellType.string_);
        sample.put(SAMPLE_NAME, ExcelCellType.string_);
        if (!BaseAttrType.none.name().equals(getExpSampleType().getOrganism())) {
            sample.put(ORGANISM, ExcelCellType.string_);
        }
        if (!BaseAttrType.none.name().equals(getExpSampleType().getDesc())) {
            sample.put(DESCRIPTION, ExcelCellType.string_);
        }
        sample.put(RELATED_LINKS, ExcelCellType.string_);
//        sample.put("security", ExcelCellType.string_);
        if (!BaseAttrType.none.name().equals(getExpSampleType().getTissue())) {
            sample.put(TISSUE, ExcelCellType.string_);
        }
        initTitleByAttr(sample, getAttrMap());
        if (!BaseAttrType.none.name().equals(getExpSampleType().getProtocol())) {
            sample.put(PROTOCOL, ExcelCellType.string_);
        }

        titles.put("sample", sample);
        return titles;
    }
}
