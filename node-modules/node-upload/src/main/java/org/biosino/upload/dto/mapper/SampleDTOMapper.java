package org.biosino.upload.dto.mapper;

import org.biosino.common.mongo.entity.Sample;
import org.biosino.upload.dto.SampleDTO;
import org.biosino.upload.vo.SampleVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 样本对象拷贝
 *
 * <AUTHOR>
 */
@Mapper
public interface SampleDTOMapper {
    SampleDTOMapper INSTANCE = Mappers.getMapper(SampleDTOMapper.class);

    @Mapping(target = "tempData", ignore = true)
    Sample copy(Sample sourceSap);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tempData", ignore = true)
    void tempToDb(Sample sourceSap, @MappingTarget Sample Target);

    void copyToDb(SampleDTO sourceDto, @MappingTarget Sample target);

    void copyToVo(Sample sap, @MappingTarget SampleVO target);
}
