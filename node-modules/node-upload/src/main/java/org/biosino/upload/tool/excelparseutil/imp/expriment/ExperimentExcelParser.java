package org.biosino.upload.tool.excelparseutil.imp.expriment;


import lombok.Getter;
import lombok.Setter;
import org.biosino.common.core.enums.dict.BaseAttrType;
import org.biosino.common.core.enums.dict.ExcelCellType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.upload.tool.excelparseutil.imp.ExcelParser;

import java.io.File;
import java.util.LinkedHashMap;
import java.util.Map;

import static org.biosino.common.mongo.entity.ExpSampleType.*;

/**
 * 实验excel解析
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ExperimentExcelParser extends ExcelParser {

    public ExperimentExcelParser(File excelFile, ExpSampleType expSampleType) throws ServiceException {
        super(excelFile, expSampleType);
    }

    @Override
    protected Map<String, Integer> getDefaultConf() {
        Map<String, Integer> param = new LinkedHashMap<>();
        param.put("experiment", 12);
        return param;
    }

    @Override
    public Map<String, Map<String, ExcelCellType>> definedTitle() {

        Map<String, Map<String, ExcelCellType>> titles = new LinkedHashMap<>();

        //词条列表
        //experiment
        Map<String, ExcelCellType> experiment = initExpTitleRule(getAttrMap(), getExpSampleType());

        //所有sheet名
        titles.put("sequencing", experiment);

        return titles;
    }

    /**
     * 实验数据，标题验证规则
     *
     * @param attrMap
     * @return
     */
    public static Map<String, ExcelCellType> initExpTitleRule(final Map<String, ExpSampleType.Attributes> attrMap, ExpSampleType expSampleType) {
        Map<String, ExcelCellType> experiment = new LinkedHashMap<>();
        //词条列表
        //experiment
        experiment.put(EXPERIMENT_ID, ExcelCellType.string_);
        experiment.put(EXPERIMENT_NAME, ExcelCellType.string_);
        experiment.put(PROJECT_ID, ExcelCellType.string_);
        experiment.put(PROJECT_NAME, ExcelCellType.string_);
        experiment.put(RELATED_LINKS, ExcelCellType.string_);
        if (!BaseAttrType.none.name().equals(expSampleType.getDesc())) {
            experiment.put(DESCRIPTION, ExcelCellType.string_);
        }
        if (!BaseAttrType.none.name().equals(expSampleType.getProtocol())) {
            experiment.put(PROTOCOL, ExcelCellType.string_);
        }
        initTitleByAttr(experiment, attrMap);
        return experiment;
    }

}
