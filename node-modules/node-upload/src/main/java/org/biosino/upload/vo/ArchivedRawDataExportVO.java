package org.biosino.upload.vo;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/2/3
 */
@Data
public class ArchivedRawDataExportVO {
    @Excel(name = "Data ID")
    private String datNo;

    @Excel(name = "File Name")
    private String name;

    @Excel(name = "Project ID")
    private String projectNo;

    @Excel(name = "Project Name")
    private String projectName;

    @Excel(name = "Experiment ID")
    private String expNo;

    @Excel(name = "Experiment Name")
    private String expName;

    @Excel(name = "Sample ID")
    private String sapNo;

    @Excel(name = "Sample Name")
    private String sapName;

    @Excel(name = "Run ID")
    private String runNo;

    @Excel(name = "Run Name")
    private String runName;

    @Excel(name = "Upload Date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @Excel(name = "md5")
    private String md5;

    @Excel(name = "File Size")
    private String readableFileSize;
}
