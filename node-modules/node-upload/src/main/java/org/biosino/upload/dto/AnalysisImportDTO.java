package org.biosino.upload.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR> @date 2024/1/23
 */
@Data
public class AnalysisImportDTO {
    private Integer rowIndex;

    @JsonProperty("analysis_id")
    @JSONField(name = "analysis_id")
    private String analysisId;

    @JsonProperty("analysis_name")
    @JSONField(name = "analysis_name")
    private String analysisName;

    @JsonProperty("index")
    @JSONField(name = "index")
    private Integer index;

    @JsonProperty("program")
    @J<PERSON>NField(name = "program")
    private String program;

    @JsonProperty("link")
    @JSONField(name = "link")
    private String link;

    @JsonProperty("version")
    @JSONField(name = "version")
    private String version;

    @JsonProperty("note")
    @JSONField(name = "note")
    private String note;

    @JsonProperty("output_file")
    @JSONField(name = "output_file")
    private String outputFile;

    @JsonProperty("description")
    @J<PERSON>NField(name = "description")
    private String description;

    @JsonProperty("analysis_type")
    @JSONField(name = "analysis_type")
    private String analysisType;

    @JsonProperty("other_analysis_type")
    @JSONField(name = "other_analysis_type")
    private String customAnalysisType;

    @JsonProperty("target_project")
    @JSONField(name = "target_project")
    private String targetProject;

    @JsonProperty("target_experiment")
    @JSONField(name = "target_experiment")
    private String targetExperiment;

    @JsonProperty("target_sample")
    @JSONField(name = "target_sample")
    private String targetSample;

    @JsonProperty("target_analysis")
    @JSONField(name = "target_analysis")
    private String targetAnalysis;

    @JsonProperty("target_run")
    @JSONField(name = "target_run")
    private String targetRun;

    @JsonProperty("target_data")
    @JSONField(name = "target_data")
    private String targetData;

    @JsonProperty("target_other_name")
    @JSONField(name = "target_other_name")
    private String targetOtherName;

    @JsonProperty("target_other_link")
    @JSONField(name = "target_other_link")
    private String targetOtherLink;
}
