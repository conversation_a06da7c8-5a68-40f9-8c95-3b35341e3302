package org.biosino.upload.tool.excelparseutil.imp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.biosino.common.core.enums.dict.ExcelCellType;
import org.biosino.common.core.enums.dict.ExpSampleDataType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.upload.tool.excelparseutil.CellData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;

/**
 * Created by yhju on 2018/3/6.
 */
public abstract class ExcelParser {
    public final static String RELATED_LINKS_SEPARATOR = ";";
    private final static String TEMPLATE_ERROR = "Template title modify error";

    @Getter
    public ExpSampleType expSampleType;

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

//    private static final String[] DATE_PATTERNS = {"yyyy-MM-dd", "yyyyMMdd", "yyyy/MM/dd", "yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss", "yyyyMMdd HH:mm:ss", "yyyy-MM", "yyyy",
//            DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.getPattern()};

    // 新版要求至少到日
    private static final String[] DATE_PATTERNS = {"yyyy-MM-dd", "yyyyMMdd", "yyyy/MM/dd", "yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss", "yyyyMMdd HH:mm:ss",
            DateFormatUtils.ISO_8601_EXTENDED_DATETIME_FORMAT.getPattern(), DateFormatUtils.ISO_8601_EXTENDED_DATETIME_TIME_ZONE_FORMAT.getPattern()};

    private List<String> errorMessages = new ArrayList<>();
    private File excelFile;
    private Map<String, Map<String, ExcelCellType>> definedTitle;
    private Map<String, Integer> param;

    public ExcelParser(File excelFile) {
        this(excelFile, null);
    }

    public ExcelParser(File excelFile, ExpSampleType expSampleType) {
        this(excelFile, null, expSampleType);
    }

    public ExcelParser(File excelFile, Map<String, Integer> param, ExpSampleType expSampleType) throws ServiceException {
        this(excelFile, param, null, expSampleType);
    }

    public ExcelParser(File excelFile, Map<String, Integer> param, Map<String, ExpSampleType.Attributes> rangeMap, ExpSampleType expSampleType) throws ServiceException {
        if (excelFile == null || !excelFile.exists() || !excelFile.isFile()) {
            throw new ServiceException("Excel file not found");
        }
        this.expSampleType = expSampleType;
        // map的key为sheet名字，value为当前sheet的标题行号
        this.param = new LinkedHashMap<>();
        if (param == null || param.isEmpty()) {
            this.param = getDefaultConf();
        } else {
            for (String key : param.keySet()) {
                this.param.put(key, param.get(key));
            }
        }
        if (!this.param.containsKey("default")) {
            this.param.put("default", 12);
        }

        this.excelFile = excelFile;
        this.attrMap = rangeMap;
        this.definedTitle = definedTitle();
    }

    @Setter
    @Getter
    private Map<String, ExpSampleType.Attributes> attrMap;
    private boolean delFile;

    public boolean isDelFile() {
        return delFile;
    }

    public void setDelFile(boolean delFile) {
        this.delFile = delFile;
    }

    protected abstract Map<String, Integer> getDefaultConf();

    public abstract Map<String, Map<String, ExcelCellType>> definedTitle();

    public Map<String, List<Map<String, CellData>>> parse(Map<String, ExpSampleType.Attributes> rangeMap) throws ServiceException {
        return parse(rangeMap, false);
    }

    public Map<String, List<Map<String, CellData>>> parse(Map<String, ExpSampleType.Attributes> rangeMap, final boolean delFile) throws ServiceException {
        Workbook workbook = null;
        Map<String, List<Map<String, CellData>>> result = null;
        try {
            setAttrMap(rangeMap);
            this.definedTitle = definedTitle();
            setDelFile(delFile);
            workbook = new XSSFWorkbook(FileUtils.openInputStream(excelFile));
            result = parse(workbook, rangeMap);
        } catch (FileNotFoundException e) {
            errorMessages.add("No parse file found");
            logger.error(e.getMessage(), e);
        } catch (ServiceException e) {
            if (!TEMPLATE_ERROR.equals(e.getMessage())) {
                errorMessages.add(e.getMessage());
            }
            logger.debug(e.getMessage());
        } catch (Exception e) {
            errorMessages.add("Parsing failed, please check file format");
            logger.error(e.getMessage(), e);
        } finally {
            IoUtil.close(workbook);
            if (delFile) {
                this.excelFile.delete();
            }
        }
        if (CollUtil.isNotEmpty(errorMessages)) {
            //<br/> <hr>
            throw new ServiceException(StringUtils.join(errorMessages, "<hr>"));
        }
        return result;
    }

    public Map<String, List<Map<String, CellData>>> parse(Workbook workbook, Map<String, ExpSampleType.Attributes> rangeMap) throws ServiceException {
        Map<String, List<Map<String, CellData>>> data = new LinkedHashMap<>();
        int sheetNum = workbook.getNumberOfSheets();
        if (sheetNum == 0) {
            return data;
        }
//        Map<String,List<String>> valueDomainMap = org.scbit.lsbi.node.upload.tool.FileUtil.readExcelValueDomainProperty("excel_value_domain.properties");
//      for (int i = 0; i < sheetNum; i++) {
        for (int i = 0; i < 1; i++) {
            Sheet sheet = workbook.getSheetAt(i);
            List<Map<String, CellData>> sheetData = parse(sheet, rangeMap);
            data.put(sheet.getSheetName(), sheetData);

        }
        return data;
    }

    private List<Map<String, CellData>> parse(Sheet sheet, Map<String, ExpSampleType.Attributes> rangeMap) throws ServiceException {
        int titleRowNum = this.param.get(sheet.getSheetName()) == null ? this.param.get("default") : this.param.get(sheet.getSheetName());
        List<Map<String, CellData>> data = new ArrayList<>();
        int rowNum = sheet.getLastRowNum();
        if (rowNum <= titleRowNum) {
            return data;
        }
        Map<String, Integer> title = getSheetTitle(sheet.getRow(titleRowNum));
        for (int i = (titleRowNum + 1); i <= rowNum; i++) {
            Row row = sheet.getRow(i);
            Map<String, CellData> rowData = parse(title, row, rangeMap);
            if (rowData == null || rowData.size() == 0) {
                continue;
            }
            if (!validateRow(sheet, rowData)) {
                continue;
            }
            data.add(rowData);
        }
        return data;
    }

    private Map<String, CellData> parse(Map<String, Integer> title, Row row, Map<String, ExpSampleType.Attributes> rangeMap) {
        if (isEmptyRow(row)) {
            return null;
        }
        Map<String, CellData> map = new ExcelLinkedHashMap();
        for (String key : title.keySet()) {
            Cell cell = row.getCell(title.get(key));
            if (cell == null) {
                map.put(key, new CellData(row.getSheet().getSheetName(), row.getRowNum(), title.get(key), ""));
//                logger.warn("字段“" + key + "”在Excel中未定义");
                continue;
            }
            String data = getCellString(cell);
            if (!isDelFile()) {
                // 新版本上传excel时不验证下拉框是否有效，提高效率
                if (rangeMap != null) {
                    if (rangeMap.containsKey(key)) {
                        List<String> valueDomain = rangeMap.get(key).getRangeStrList();
                        if (valueDomain != null && !valueDomain.contains(data)) {
                            errorMessages.add(row.getSheet().getSheetName() + " ,row: " + row.getRowNum() + " ,column: " + title.get(key) + " ,value: " + data + "  Not within the scope of selection");
                        }
                    }
                }
            }

            CellData cellData = new CellData(row.getSheet().getSheetName(), cell.getCellType().name(), row.getRowNum(), title.get(key), data);
            map.put(key, cellData);
        }
        return map;
    }

    private Map<String, Integer> getSheetTitle(Row row) throws ServiceException {
        if (isEmptyRow(row)) {
            throw new ServiceException("can't find sheet，sheet：" + row.getSheet().getSheetName());
        }
        Map<String, Integer> titles = new LinkedHashMap<>();
        int n = row.getPhysicalNumberOfCells();
        for (int i = 0; i < n; i++) {
            String title = getCellString(row.getCell(i));
            if (StringUtils.isBlank(title)) {
                continue;
            }
            titles.put(StringUtils.trim(title), i);
        }
//        Map<String, ExcelCellType> cellTypeMap = this.definedTitle.get(row.getSheet().getSheetName());
        Map<String, ExcelCellType> cellTypeMap = this.definedTitle.values().iterator().next();
        if (cellTypeMap == null) {
            return titles;
        }
        for (String key : cellTypeMap.keySet()) {
            if (!titles.containsKey(key)) {
                errorMessages.add("Title not found: " + key + ", Do not modify the template title, sheet name：" + row.getSheet().getSheetName());
//                throw new ServiceException("Title not found: " + key + ", Do not modify the template title, sheet name：" + row.getSheet().getSheetName());
            }
        }
        if (CollUtil.isNotEmpty(errorMessages)) {
            throw new ServiceException(TEMPLATE_ERROR);
        }
        return titles;
    }

    protected boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }
        int n = row.getLastCellNum();
        boolean flag = true;
        for (int i = 0; i <= n; i++) {
            if (StringUtils.isNotBlank(getCellString(row.getCell(i)))) {
                flag = false;
                break;
            }
        }
        return flag;
    }


    private boolean validateRow(Sheet sheet, Map<String, CellData> rowData) {
        if (rowData == null || rowData.size() == 0) {
            return true;
        }
//        Map<String, ExcelCellType> titles = definedTitle.get(sheet.getSheetName());
        Map<String, ExcelCellType> titles = definedTitle.values().iterator().next();
        if (titles == null) {
            return true;
        }
        final List<String> errorInfo = new ArrayList<>();
        for (String key : rowData.keySet()) {
            CellData data = rowData.get(key);
            if (StringUtils.isBlank(data.getValue())) {
                continue;
            }
            ExcelCellType excelCellType = titles.get(key);
            if (excelCellType == null) {
                continue;
            }
            try {
                switch (excelCellType) {
                    case string_:
                        break;
                    case integer_:
                        Integer.parseInt(data.getValue());
                        break;
                    case long_:
                        Long.parseLong(data.getValue());
                        break;
                    case double_:
                        Double.parseDouble(data.getValue());
                        break;
                    case date_:
                        parseDateStr(data.getValue());
                        /*try {
                            DateUtils.parseDate(data.getValue(), "yyyy-MM-dd", "yyyyMMdd", "yyyy/MM/dd", "yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss", "yyyyMMdd HH:mm:ss", "yyyy-MM", "yyyy");
                        } catch (Exception exception) {
                            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
                            sdf1.parse(data.getValue());
                        }*/
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                String error = "data format is wrong，sheet: %s, row: %s, cell: %s, value: %s";
                errorInfo.add(String.format(error, data.getSheetName(), data.getRow() + 1, data.getCell() + 1, data.getValue()));
            }
        }
        if (!errorInfo.isEmpty()) {
            errorMessages.addAll(errorInfo);
            return false;
        }
        return true;
    }

    /**
     * 校验日期格式，错误日期抛出异常
     *
     * @param dateStr
     * @return
     * @throws ParseException
     */
    public static Date parseDateStr(String dateStr) throws ParseException {
//        return DateUtils.parseDate(dateStr, DATE_PATTERNS);
        // 使用严格的日期解析方法
        return DateUtils.parseDateStrictly(dateStr, null, DATE_PATTERNS);
    }

    protected String getCellString(Cell cell) {
        if (cell == null) {
            return StrUtil.EMPTY;
        }
        String cellValue = StrUtil.EMPTY;
        try {
            switch (cell.getCellType()) {
                case STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        Date dateCellValue = cell.getDateCellValue();
                        cellValue = DateUtils.formatDateTime(dateCellValue);
                    } else {
                        NumberFormat format = NumberFormat.getInstance();
                        format.setGroupingUsed(false);

                        format.setMaximumFractionDigits(100);
                        cellValue = format.format(cell.getNumericCellValue());
                    }
                    break;
                case FORMULA:
                    cell.setCellType(org.apache.poi.ss.usermodel.CellType.STRING);
                    cellValue = cell.getStringCellValue();
                    break;
                case BOOLEAN:
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            logger.error("{} 行{}列，单元格错误{}", cell.getRowIndex(), cell.getColumnIndex(), cell.getNumericCellValue());
        }
//        return StringUtils.stripToEmpty(cellValue);
        return StrUtil.trimToEmpty(cellValue);
    }

    public List<String> getErrorMessages() {
        return errorMessages;
    }

    public static final void initTitleByAttr(Map<String, ExcelCellType> experiment, final Map<String, ExpSampleType.Attributes> attrMap) {
        if (CollUtil.isNotEmpty(attrMap)) {
            final Map<String, ExcelCellType> cellTypeMap = ExpSampleDataType.findCellTypeMap();
            for (Map.Entry<String, ExpSampleType.Attributes> entry : attrMap.entrySet()) {
                experiment.put(entry.getKey(), cellTypeMap.getOrDefault(entry.getValue().getDataType(), ExcelCellType.string_));
            }
        }
    }

}

// 自定义一个Map用以存储解析到的CellData
class ExcelLinkedHashMap extends LinkedHashMap {
    @Override
    public CellData get(Object key) {
        Object o = super.get(key);
        if (o == null) {
            return new CellData();
        }
        if (!(o instanceof CellData)) {
            return new CellData();
        }
        return (CellData) o;
    }


}


