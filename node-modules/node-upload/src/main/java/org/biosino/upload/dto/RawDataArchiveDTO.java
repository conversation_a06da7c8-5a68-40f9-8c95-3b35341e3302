package org.biosino.upload.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.upload.dto.abs.BaseArchiveDTO;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/1/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RawDataArchiveDTO extends BaseArchiveDTO {

    private boolean createNewRun;
    @NotBlank
    private String projectNo;
    @NotBlank
    private String expNo;
    @NotBlank
    private String sapNo;
    private String runNo;

    private String runName;
    private String runDescription;

    public void setRunName(String runName) {
        this.runName = StrUtil.trim(runName);
    }
}
