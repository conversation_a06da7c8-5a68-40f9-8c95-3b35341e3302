package org.biosino.upload.dto.mapper;

import org.biosino.common.mongo.entity.Publish;
import org.biosino.upload.dto.PublishDTO;
import org.biosino.upload.vo.PublishVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 实验对象拷贝
 *
 * <AUTHOR>
 */
@Mapper
public interface PublishDTOMapper {
    PublishDTOMapper INSTANCE = Mappers.getMapper(PublishDTOMapper.class);

    @Mapping(target = "tempData", ignore = true)
    Publish copy(Publish publish);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tempData", ignore = true)
    void tempToDb(Publish source, @MappingTarget Publish target);

    void copyToDb(PublishDTO sourceDto, @MappingTarget Publish publish);

    void copyToVo(Publish publish, @MappingTarget PublishVO result);
}
