package org.biosino.upload.tool.excelparseutil.imp.analysis;

import org.biosino.common.core.enums.dict.ExcelCellType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.upload.tool.excelparseutil.imp.ExcelParser;

import java.io.File;
import java.util.LinkedHashMap;
import java.util.Map;

public class AnalysisParse extends ExcelParser {

    public AnalysisParse(File excelFile) {
        super(excelFile);
    }

    public AnalysisParse(File excelFile, Map<String, Integer> param) throws ServiceException {
        super(excelFile, param, null);
    }

    @Override
    protected Map<String, Integer> getDefaultConf() {
        Map<String, Integer> param = new LinkedHashMap<>();
        param.put("analysis", 12);
        return param;
    }

    @Override
    public Map<String, Map<String, ExcelCellType>> definedTitle() {

        Map<String, Map<String, ExcelCellType>> titles = new LinkedHashMap<>();

        // 词条列表
        // analysis
        Map<String, ExcelCellType> analysis = new LinkedHashMap<>();
        analysis.put("analysis_id", ExcelCellType.string_);
        analysis.put("analysis_name", ExcelCellType.string_);
        analysis.put("index", ExcelCellType.integer_);
        analysis.put("program", ExcelCellType.string_);
        analysis.put("link", ExcelCellType.string_);
        analysis.put("version", ExcelCellType.string_);
        analysis.put("note", ExcelCellType.string_);
        analysis.put("output_file", ExcelCellType.string_);
        analysis.put("description", ExcelCellType.string_);
        analysis.put("analysis_type", ExcelCellType.string_);
        analysis.put("other_analysis_type", ExcelCellType.string_);
        analysis.put("target_project", ExcelCellType.string_);
        analysis.put("target_experiment", ExcelCellType.string_);
        analysis.put("target_sample", ExcelCellType.string_);
        analysis.put("target_analysis", ExcelCellType.string_);
        analysis.put("target_run", ExcelCellType.string_);
        analysis.put("target_data", ExcelCellType.string_);
        analysis.put("target_other_name", ExcelCellType.string_);
        analysis.put("target_other_link", ExcelCellType.string_);


        // 所有sheet名
        titles.put("analysis", analysis);

        return titles;
    }
}
