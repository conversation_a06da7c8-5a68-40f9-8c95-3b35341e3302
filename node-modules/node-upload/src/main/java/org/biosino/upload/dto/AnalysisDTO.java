package org.biosino.upload.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.mongo.entity.other.CustomTarget;
import org.biosino.common.mongo.entity.other.Pipeline;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/11
 */
@Data
public class AnalysisDTO {
    @NotBlank
    private String subNo;

    private String analysisNo;

    @NotBlank
    private String name;

    private String description;

    @NotBlank
    private String analysisType;

    private String customAnalysisType;

    private List<Pipeline> pipeline;

    private List<AnalysisTarget> target;

    private List<CustomTarget> customTarget;

    private List<PublishDTO> publish;

    public void setName(String name) {
        this.name = StrUtil.trim(name);
    }
}
