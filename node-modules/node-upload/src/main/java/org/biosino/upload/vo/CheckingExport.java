package org.biosino.upload.vo;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/6
 */
@Data
public class CheckingExport {
    @Excel(name = "File Name")
    private String name;

    @Excel(name = "File Path")
    private String relativePath;

    @Excel(name = "Upload Date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date uploadDate;

    @Excel(name = "File Size")
    private String readableSize;

    @Excel(name = "Status", readConverterExp = "排队中=queuing,校验中=checking,校验失败=checkFail,校验成功=checkSuccess")
    private String status;

    @Excel(name = "Fail Cause")
    private String failCause;

}
