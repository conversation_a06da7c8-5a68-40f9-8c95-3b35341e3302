package org.biosino.upload.dto.mapper;

import org.biosino.common.mongo.entity.Experiment;
import org.biosino.upload.dto.ExperimentDTO;
import org.biosino.upload.vo.ExperimentVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 实验对象拷贝
 *
 * <AUTHOR>
 */
@Mapper
public interface ExperimentDTOMapper {
    ExperimentDTOMapper INSTANCE = Mappers.getMapper(ExperimentDTOMapper.class);

    @Mapping(target = "tempData", ignore = true)
    Experiment copy(Experiment sourceExp);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tempData", ignore = true)
    void tempToDb(Experiment sourceExp, @MappingTarget Experiment experiment);

    void copyToDb(ExperimentDTO sourceDto, @MappingTarget Experiment experiment);

    void copyToVo(Experiment experiment, @MappingTarget ExperimentVO result);
}
