package org.biosino.upload.dto;

import lombok.Data;
import org.biosino.common.mongo.dto.BaseQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/4
 */
@Data
public class DataQuery extends BaseQuery {
    // submission no
    private String subNo;

    // 查询的预归档的数据的类型
    private Boolean rawData;

    // 导出数据勾选的ID
    private List<String> ids;

    private String name;
    // 是否已经归档
    private String archived;
    private String creator;
    private List<String> securities;
    private Boolean existAnalysisNo;
    private Boolean existRunNo;
}
