package org.biosino.upload.vo;

import lombok.Data;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.upload.dto.AnalysisImportDTO;
import org.biosino.upload.dto.ArchiveAnalysisImportDTO;
import org.biosino.upload.vo.exp.SubExpSampleVO;

import java.util.List;
import java.util.Map;

/**
 * 提交详情页
 *
 * <AUTHOR>
 * @date 2024/2/24
 */
@Data
public class SubDetailVO {

    private Submission submission;

    // Raw Data
    private ProjectVO project;

    private List<SubExpSampleVO> experiment;
    private List<PublishVO> expPublish;

    private List<SubExpSampleVO> sample;
    private List<PublishVO> sapPublish;

    private List<Map<String, Object>> rawDataArchiveData;

    // Analysis Data
    private List<AnalysisImportDTO> analysis;
    private List<PublishVO> analysisPublish;

    private List<ArchiveAnalysisImportDTO> analysisArchiveData;

    // 审核的文献信息
    private PublishVO publish;

    // 数据入库中
    private boolean processing;
}
