package org.biosino.upload.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.constant.ConfigConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.StringUtils;
import org.biosino.common.core.utils.bean.BeanUtils;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.upload.dto.PublishDTO;
import org.biosino.upload.dto.mapper.PublishDTOMapper;
import org.biosino.upload.repository.*;
import org.biosino.upload.vo.PublishVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PublishService {
    private final PublishRepository publishRepository;
    private final SampleRepository sampleRepository;
    private final ProjectRepository projectRepository;
    private final AnalysisRepository analysisRepository;
    private final ExperimentRepository experimentRepository;
    private final RemoteMemberService remoteMemberService;

    // 新增未审核的项目，文献：在temp_data中，状态：init，删除：否
    // 审核通过，文献：temp_data：null ，状态：audit，删除：否
    // 二次新增文献，文献：temp_data：在temp_data中 ，状态：init，删除：否
    // 删除个别文献，文献：temp_data：在temp_data中 ，状态：init，删除：是，审核通过后删除
    @Transactional(rollbackFor = Exception.class)
    public void save(List<PublishDTO> publishDTO, String type, String typeId, boolean isMetadataEdit) {

        if (isEmptyPublish(publishDTO)) {
            return;
        }

        final String memberId = SecurityUtils.getMemberId();

        List<Publish> publishList;
        // 如果是metadata编辑进来的
        if (isMetadataEdit) {
            publishList = publishRepository.findByTypeId(type, typeId);
        } else {
            publishList = publishRepository.findExistTempByTypeId(type, typeId);
        }

        Map<String, Publish> idMap = new HashMap<>();

        if (CollUtil.isNotEmpty(publishList)) {
            idMap = publishList.stream().collect(Collectors.toMap(Publish::getId, Function.identity(), (existingValue, newValue) -> existingValue));
        }

        Set<String> updateIds = new HashSet<>();

        for (PublishDTO dto : publishDTO) {
            if (BeanUtils.allFieldIsNull(dto)) {
                continue;
            }
            if (StrUtil.isBlank(dto.getPublication())) {
                throw new ServiceException("Save Publish failed, Journal cannot be empty");
            }

            if (StrUtil.isBlank(dto.getArticleName())) {
                throw new ServiceException("Save Publish failed, Title cannot be empty");
            }

            if (StrUtil.isBlank(dto.getDoi())) {
                throw new ServiceException("Save Publish failed, Doi cannot be empty");
            }

            if (!StringUtils.isDOIFormat(dto.getDoi())) {
                throw new ServiceException("Save Publish failed, DOI format is incorrect");
            }

            if (StrUtil.isNotBlank(dto.getPmid()) && !StringUtils.isPMIDFormat(dto.getPmid())) {
                throw new ServiceException("Save Publish failed, PMID format is incorrect");
            }

            if (dto.isEmpty()) {
                dto.setId(null);
                continue;
            }

            String id = dto.getId();
            Publish publish;
            if (idMap.containsKey(id)) {
                publish = idMap.get(id);

                Publish tempData = PublishDTOMapper.INSTANCE.copy(publish);
                // 编辑
                PublishDTOMapper.INSTANCE.copyToDb(dto, tempData);
                tempData.setType(type);
                tempData.setTypeId(typeId);
                tempData.setUpdater(memberId);
                tempData.setUpdateDate(new Date());
                tempData.setAudited(AuditEnum.unaudited.name());

                publish.setTempData(tempData);
                publishRepository.save(publish);

            } else {
                // 新增
                publish = new Publish();
                PublishDTOMapper.INSTANCE.copyToDb(dto, publish);
                publish.setId(IdUtil.objectId());
                publish.setType(type);
                publish.setTypeId(typeId);
                publish.setCreator(memberId);
                publish.setCreateDate(new Date());
                publish.setUpdater(memberId);
                publish.setUpdateDate(new Date());
                publish.setAudited(AuditEnum.unaudited.name());
                publish.setSort(1);
                publish.setStatus(ConfigConstants.enable);

                publish.setTempData(PublishDTOMapper.INSTANCE.copy(publish));
            }
            publish = publishRepository.save(publish);
            idMap.put(publish.getId(), publish);

            updateIds.add(publish.getId());
        }

        // 找出未被操作的文献，然后删除
        idMap.forEach((publishId, publish) -> {
            if (!updateIds.contains(publishId)) {
                Publish tempData = PublishDTOMapper.INSTANCE.copy(publish);
                tempData.setUpdater(memberId);
                tempData.setUpdateDate(new Date());
                tempData.setDeleted(true);

                publish.setTempData(tempData);
                publishRepository.save(publish);
            }
        });

    }

    public static boolean isEmptyPublish(List<PublishDTO> publishDTOList) {
        if (CollUtil.isEmpty(publishDTOList)) {
            return true;
        }
        boolean result = true;
        for (PublishDTO publishDTO : publishDTOList) {
            if (StrUtil.isNotBlank(publishDTO.getPublication()) || StrUtil.isNotBlank(publishDTO.getDoi()) || StrUtil.isNotBlank(publishDTO.getPmid())
                    || StrUtil.isNotBlank(publishDTO.getArticleName()) || StrUtil.isNotBlank(publishDTO.getReference())) {
                return false;
            }
        }
        return result;
    }

    public void updatePublish(String type, String oldId, String newId) {
        List<Publish> publishList = publishRepository.findByTypeId(type, oldId);

        for (Publish publish : publishList) {
            if (publish != null) {
                publish.setTypeId(newId);
                Publish tempData = publish.getTempData();
                if (tempData != null) {
                    tempData.setTypeId(newId);
                }
                publishRepository.save(publish);
            }
        }
    }

    public List<PublishVO> getTempPublishVO(String type, String typeId) {

        List<Publish> publishList = publishRepository.findExistTempByTypeId(type, typeId);

        List<PublishVO> result = new ArrayList<>();

        for (Publish publish : publishList) {
            PublishVO vo = new PublishVO();
            PublishDTOMapper.INSTANCE.copyToVo(publish.getTempData(), vo);
            result.add(vo);
        }
        return result;
    }

    public List<PublishVO> getPublishVo(String type, String typeId) {
        List<Publish> publishList = publishRepository.findByTypeId(type, typeId);

        List<PublishVO> result = new ArrayList<>();
        for (Publish publish : publishList) {
            PublishVO vo = new PublishVO();
            if (publish.getTempData() != null) {
                PublishDTOMapper.INSTANCE.copyToVo(publish.getTempData(), vo);
            } else {
                PublishDTOMapper.INSTANCE.copyToVo(publish, vo);
            }
            result.add(vo);
        }
        return result;
    }

    public void submitPublish(String type, String typeId) {
        if (type == null || typeId == null) {
            return;
        }

        List<Publish> publishList = publishRepository.findTempByTypeId(type, typeId);
        if (CollUtil.isEmpty(publishList)) {
            return;
        }
        for (Publish publish : publishList) {
            publish = PublishDTOMapper.INSTANCE.copy(publish.getTempData());
            // 编辑
            publish.setUpdateDate(new Date());
            publish.setAudited(AuditEnum.audited.name());

            publish.setTempData(null);
            publishRepository.save(publish);
        }
    }

    public void submitPublishBySubNo(String subNo) {
        if (StrUtil.isBlank(subNo)) {
            return;
        }
        List<Publish> publishes = publishRepository.findTempBySubNo(subNo);

        if (CollUtil.isEmpty(publishes)) {
            return;
        }

        for (Publish publish : publishes) {
            publish = PublishDTOMapper.INSTANCE.copy(publish.getTempData());
            publish.setUpdateDate(new Date());
            publish.setAudited(AuditEnum.audited.name());
            publish.setTempData(null);
            publishRepository.save(publish);
        }
    }

    public PublishVO getAuditPublishInfo(String sub) {
        List<Publish> publishList = publishRepository.findTempBySubNo(sub);
        if (CollUtil.isEmpty(publishList)) {
            return null;
        }

        Publish masterPublish = publishList.get(0);
        Publish tempData = masterPublish.getTempData();

        PublishVO publishVO = new PublishVO();
        BeanUtils.copyProperties(tempData, publishVO);

        List<PublishVO.TypeInfo> typeInfos = new ArrayList<>();

        for (Publish publish : publishList) {
            String type = publish.getType();
            String typeId = publish.getTypeId();

            PublishVO.TypeInfo info = new PublishVO.TypeInfo();
            info.setType(type);
            info.setTypeNo(typeId);
            info.setArticleName(publish.getArticleName());
            info.setCreateDate(publish.getCreateDate());
            info.setCreator(publish.getCreator());

            R<MemberDTO> user = remoteMemberService.getOneMemberByMemberId(publish.getCreator(), "FtpUser", SecurityConstants.INNER);
            if (R.isSuccess(user)) {
                info.setEmail(user.getData().getEmail());
                info.setUsername(user.getData().getFirstName() + " " + user.getData().getLastName());
            }

            if (AuthorizeType.project.name().equals(type)) {
                Project project = projectRepository.findTopByProjectNo(typeId).orElseThrow(() -> new ServiceException("no found the publish project"));
                info.setTypeName(project.getName());
            }

            if (AuthorizeType.experiment.name().equals(type)) {
                Experiment experiment = experimentRepository.findTopByExpNo(typeId).orElseThrow(() -> new ServiceException("no found the publish experiment"));
                info.setTypeName(experiment.getName());
            }

            if (AuthorizeType.sample.name().equals(type)) {
                Sample sample = sampleRepository.findTopBySapNo(typeId).orElseThrow(() -> new ServiceException("no found the publish sample"));
                info.setTypeName(sample.getName());
            }

            if (AuthorizeType.analysis.name().equals(type)) {
                Analysis analysis = analysisRepository.findFirstByAnalysisNo(typeId).orElseThrow(() -> new ServiceException("no found the publish analysis"));
                info.setTypeName(analysis.getName());
            }
            typeInfos.add(info);
        }

        publishVO.setTypeInfoList(typeInfos);
        return publishVO;
    }

}
