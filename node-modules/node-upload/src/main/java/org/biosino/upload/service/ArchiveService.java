package org.biosino.upload.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.dto.*;
import org.biosino.upload.dto.abs.AbsBatchDTO;
import org.biosino.upload.dto.mapper.DataDTOMapper;
import org.biosino.upload.dto.mapper.RunDTOMapper;
import org.biosino.upload.repository.*;
import org.biosino.upload.vo.ErrorMsgVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/1/15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ArchiveService extends BaseService {

    private final DataRepository dataRepository;

    private final RunRepository runRepository;

    private final ExperimentRepository experimentRepository;

    private final SampleRepository sampleRepository;

    private final AnalysisRepository analysisRepository;

    private final SubmissionRepository submissionRepository;

    @Transactional(rollbackFor = Exception.class)
    public void saveArchivingAnalysisData(AnalysisDataArchiveDTO archiveDTO) {
        if (StrUtil.isBlank(archiveDTO.getSubNo())) {
            throw new ServiceException("Params error,No subNo found");
        }
        if (StrUtil.isBlank(archiveDTO.getAnalysisNo())) {
            throw new ServiceException("Please select Analysis");
        }
        if (CollUtil.isEmpty(archiveDTO.getDatNos())) {
            throw new ServiceException("Please select Data");
        }

        String subNo = archiveDTO.getSubNo();
        Submission submission = getEditSubmissionByNo(subNo);

        List<String> archivedAnalysisDataNos = CollUtil.isNotEmpty(submission.getAnalysisDataNos()) ?
                submission.getAnalysisDataNos() : CollUtil.newArrayList();


        String analysisNo = archiveDTO.getAnalysisNo();

        if (CollUtil.isNotEmpty(archiveDTO.getDatNos())) {
            for (String dataNo : archiveDTO.getDatNos()) {
                Data data = dataRepository.getNoOpenDataByDatNo(dataNo);
                if (data == null) {
                    throw new ServiceException("Data not found");
                }

                Data tempData = data.getTempData();
                // 从已归档已审核状态转换待审核归档
                if (tempData == null) {
                    tempData = DataDTOMapper.INSTANCE.copy(data);
                }
                tempData.setArchived(ArchiveEnum.yes.name());

                // 解除run的关联
                tempData.setRunNo(null);

                // 添加analysis的关联
                tempData.setAnalNo(analysisNo);
                tempData.setSubNo(subNo);
                tempData.setUpdateDate(new Date());

                data.setTempData(tempData);
                dataRepository.save(data);
                archivedAnalysisDataNos.add(dataNo);
            }
        }

        // 更新字段
        submission.setAnalysisDataNos(archivedAnalysisDataNos.stream().distinct().collect(Collectors.toList()));

        // 保存数据
        saveEditSubmission(submission);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveArchivingRawData(RawDataArchiveDTO archiveDTO) {
        if (archiveDTO.isCreateNewRun() && StrUtil.isBlank(archiveDTO.getRunName())) {
            throw new ServiceException("Please fill in the Run name");
        }
        String subNo = archiveDTO.getSubNo();
        Submission submission = getEditSubmissionByNo(subNo);

        List<String> archivedRawDataNos = CollUtil.isNotEmpty(submission.getRawDataNos()) ? submission.getRawDataNos() : CollUtil.newArrayList();

        Date currentDate = new Date();
        Run run;
        // 新建Run
        if (archiveDTO.isCreateNewRun()) {
            run = new Run();
            String id = IdUtil.fastSimpleUUID();
            run.setId(IdUtil.objectId());
            run.setRunNo(id);
            run.setSubNo(subNo);
            run.setName(archiveDTO.getRunName());
            run.setDescription(archiveDTO.getRunDescription());
            run.setExpNo(archiveDTO.getExpNo());
            run.setSapNo(archiveDTO.getSapNo());
            run.setAudited(AuditEnum.init.name());
            run.setVisibleStatus(VisibleStatusEnum.Unaccessible.name());
            run.setHitNum(0L);
            run.setCreateDate(currentDate);
            run.setUpdateDate(currentDate);
            run.setCreator(SecurityUtils.getMemberId());
            run.setOwnership(OwnershipEnum.self_support.getDesc());

            Run tempRun = RunDTOMapper.INSTANCE.copy(run);
            run.setTempData(tempRun);

            run = runRepository.save(run);
        } else {
            // 找出已存在的RUN，追加数据
            run = runRepository.findFirstByRunNo(archiveDTO.getRunNo()).orElseThrow(() -> new ServiceException("The Run: " + archiveDTO.getRunNo() + " was not found"));
        }
        // 处理归档的数据
        if (CollUtil.isNotEmpty(archiveDTO.getDatNos())) {
            for (String dataNo : archiveDTO.getDatNos()) {
                Data data = dataRepository.getNoOpenDataByDatNo(dataNo);
                if (data == null) {
                    throw new ServiceException("Data not found");
                }
                Data tempData = data.getTempData();

                // 从已归档已审核状态转换待审核归档
                if (tempData == null) {
                    tempData = DataDTOMapper.INSTANCE.copy(data);
                }

                // 清除analysisNo的关联
                tempData.setAnalNo(null);
                tempData.setSubNo(subNo);
                tempData.setArchived(ArchiveEnum.yes.name());
                tempData.setRunNo(run.getRunNo());
                tempData.setUpdateDate(currentDate);

                data.setTempData(tempData);
                dataRepository.save(data);
                archivedRawDataNos.add(dataNo);
            }
        }

        // 更新submission记录
        submission.setRawDataNos(archivedRawDataNos.stream().distinct().collect(Collectors.toList()));

        saveEditSubmission(submission);
    }

    public List<ErrorMsgVO> batchSaveArchivingRawData(ArchivingBatchDTO dto) {
        String memberId = SecurityUtils.getMemberId();

        // 记录错误信息
        List<ErrorMsgVO> errors = new ArrayList<>();
        // 数据转为列 map
        Map<String, List<Object>> excelMap = getExcelMap(dto.getTitles(), dto.getDatas());

        // 校验必填项
        mustFilledCheck(excelMap, "data_id", errors);

        // 将数据转为对象
        List<ArchiveImportDTO> archiveImportDTOS = new ArrayList<>();
        for (int i = 0; i < dto.getDatas().size(); i++) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("rowIndex", i);
            for (int j = 0; j < dto.getTitles().size(); j++) {
                Object o = dto.getDatas().get(i)[j];
                if (o == null) {
                    jsonObject.put(dto.getTitles().get(j), null);
                } else {
                    jsonObject.put(dto.getTitles().get(j), StrUtil.trimToNull(o.toString()));
                }
            }
            archiveImportDTOS.add(jsonObject.toJavaObject(ArchiveImportDTO.class));
        }

        // 将所以会涉及到id的地方变为新的id
        parseRawDataArchiveImportMainNo(archiveImportDTOS);

        // 记录校验过的no或name
        List<String> expNos = archiveImportDTOS.stream().map(ArchiveImportDTO::getExpNo)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> expNames = archiveImportDTOS.stream().map(ArchiveImportDTO::getExpName)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        Map<String, Boolean> expNoRecordMap = experimentRepository.existsUserExperimentByExpNos(expNos, memberId);
        Map<String, Boolean> expNameRecordMap = experimentRepository.existAuditInitExperimentByExpNames(expNames, memberId);

        List<String> sapNos = archiveImportDTOS.stream().map(ArchiveImportDTO::getSapNo)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> sapNames = archiveImportDTOS.stream().map(ArchiveImportDTO::getSapName)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        Map<String, Boolean> sapNoRecordMap = sampleRepository.existsUserSampleBySapNos(sapNos, memberId);
        Map<String, Boolean> sapNameRecordMap = sampleRepository.existAuditInitSampleBySapNames(sapNames, memberId);

        List<Experiment> expList = experimentRepository.findAllAuditInitExpByNames(expNames, memberId);
        Map<String, Experiment> expNameToExpMap = expList.stream().collect(Collectors.toMap(Experiment::getName, Function.identity(), (existingValue, newValue) -> existingValue));
        List<Sample> sapList = sampleRepository.findAllAuditInitSampleByNames(sapNames, memberId);
        Map<String, Sample> sapNameToSapMap = sapList.stream().collect(Collectors.toMap(Sample::getName, Function.identity(), (existingValue, newValue) -> existingValue));

        // 新增未分配正式ID的RUN
        Map<String, Run> isnertRunRecordMap = createIsnertRunMap(archiveImportDTOS);
        // 已存在，有正式ID的RUN
        Map<String, Run> existRunRecordMap = createExistRunMap(archiveImportDTOS);

        List<Run> allRunExistRunList = new ArrayList<>();
        if (CollUtil.isNotEmpty(isnertRunRecordMap)) {
            allRunExistRunList.addAll(isnertRunRecordMap.values());
        }
        if (CollUtil.isNotEmpty(existRunRecordMap)) {
            allRunExistRunList.addAll(existRunRecordMap.values());
        }
        // 查询当前未分配正式id的run的submission
        List<String> subNos = allRunExistRunList.stream().map(x -> {
            if (x.getTempData() != null) {
                return x.getTempData().getSubNo();
            } else {
                return x.getSubNo();
            }
        }).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, Submission> subNoToSubmissionMap = submissionRepository.findAllBySubNoIn(subNos).stream().collect(Collectors.toMap(Submission::getSubNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 当前批次提交的data数据
        Map<String, Data> dataRecordMap = createDataMap(archiveImportDTOS);

        // 记录已经准备入库的data
        Map<String, Integer> duplicateData = new HashMap<>();

        // 记录没填写run_no 但填写了run_name 的前面填写的experiment 和 sample是否出现不一样的
        Map<String, List<String>> runNameInfoMap = new HashMap<>();

        // 如果用户提交的所有的data no在非公开的数据中都无法找到，则不执行任何校验，返回错误
        if (CollUtil.isEmpty(dataRecordMap)) {
            errors.add(errMsg(null, "data_id", "All data", "Unable to find all data in non-public data"));
            return errors;
        }

        List<Run> newRun = new ArrayList<>();
        Set<Run> updateRunSet = new HashSet<>();
        Map<String, ArchiveImportDTO> runNoToLineMap = new LinkedHashMap<>();
        // newRunNameToRunNoMap
        Map<String, String> newRunNameToRunNoMap = new HashMap<>();
        List<Data> dataResult = new ArrayList<>();

        for (ArchiveImportDTO line : archiveImportDTOS) {
            String runNo = line.getRunNo();

            String dataNo = StrUtil.trim(line.getDataNos());

            // 校验data_id
            if (StrUtil.isBlank(line.getDataNos())) {
                errors.add(errMsg(line.getRowIndex(), "data_id", null, "can't be empty"));
            } else {
                if (!dataNo.startsWith(SequenceType.DATA.getPrefix())) {
                    // 校验Data NO格式
                    errors.add(errMsg(line.getRowIndex(), "data_id", dataNo, "ID format error"));
                } else {
                    // 校验Data是否存在
                    if (!dataRecordMap.containsKey(dataNo)) {
                        errors.add(errMsg(line.getRowIndex(), "data_id", dataNo, "not found in your private data"));
                    } else {
                        Data data = dataRecordMap.get(dataNo);
                        Data tempData = data.getTempData();
                        if (tempData != null && tempData.getSubNo() != null && !dto.getSubNo().equals(tempData.getSubNo())) {
                            errors.add(errMsg(line.getRowIndex(), "data_id", dataNo, "The data ID has already been used in submission: " + tempData.getSubNo()));
                        }
                        String fileName = line.getFileName();
                        if (StrUtil.isNotBlank(fileName)) {
                            if (!MyFileUtils.isValidFilename(fileName)) {
                                errors.add(errMsg(line.getRowIndex(), "file_name", fileName, "The file name contains illegal characters is not allowed. Such as spaces, ampersands (&), percent signs (%), asterisks (*), or Greek letters: " + fileName));
                            }
                            String oldDataNameSuffix = MyFileUtils.getFileNameSuffix(data.getName());
                            String newDataNameSuffix = MyFileUtils.getFileNameSuffix(fileName);
                            if (!StrUtil.equals(oldDataNameSuffix, newDataNameSuffix)) {
                                errors.add(errMsg(line.getRowIndex(), "file_name", fileName, "The file name suffix is not consistent with the original data name: " + fileName));
                            }
                        }
                    }
                    if (duplicateData.containsKey(dataNo)) {
                        errors.add(errMsg(line.getRowIndex(), "data_id", dataNo, "The data ID already exists in line " + duplicateData.get(dataNo)));
                    }
                    duplicateData.put(dataNo, line.getRowIndex() + 1);
                }
            }

            // 校验Run
            if (StrUtil.isBlank(line.getRunNo()) && StrUtil.isBlank(line.getRunName())) {
                errors.add(errMsg(line.getRowIndex(), "run_id, run_name", null, "must be filled in one"));
                continue;
            }

            // 存在Run NO
            if (StrUtil.isNotBlank(line.getRunNo())) {
                // 校验Run No格式
                if (!line.getRunNo().startsWith(SequenceType.RUN.getPrefix())) {
                    errors.add(errMsg(line.getRowIndex(), "run_id", line.getRunNo(), "format is not correct"));
                    continue;
                }
                // 校验Run No是否存在
                if (!existRunRecordMap.containsKey(runNo)) {
                    errors.add(errMsg(line.getRowIndex(), "run_id", line.getRunNo(), "not exist"));
                    continue;
                }
                // 检查 前四列 ，都不需要填写
                if (StrUtil.isNotBlank(line.getExpNo())
                        || StrUtil.isNotBlank(line.getExpName())
                        || StrUtil.isNotBlank(line.getSapNo())
                        || StrUtil.isNotBlank(line.getSapName())) {
                    errors.add(errMsg(line.getRowIndex(), "run_id", line.getRunNo(), "If you fill in run_id, the experiment_id, experiment_name, sample_id, sample_name do not need to be filled in"));
                    continue;
                }

                if (runNoToLineMap.get(line.getRunNo()) != null) {
                    ArchiveImportDTO beforeLine = runNoToLineMap.get(line.getRunNo());
                    if (!StrUtil.equals(beforeLine.getRunName(), line.getRunName())
                            || !StrUtil.equals(beforeLine.getRunDesc(), line.getRunDesc())) {
                        errors.add(errMsg(line.getRowIndex(), "run_name, run_description", null, "You filled in a different run_name or run_description at row " + (beforeLine.getRowIndex() + 1)));
                        continue;
                    }
                } else {
                    runNoToLineMap.put(line.getRunNo(), line);
                }

                // 更新RUN的信息
                Run run = existRunRecordMap.get(runNo);
                if (run.getTempData() != null
                        && subNoToSubmissionMap.get(run.getTempData().getSubNo()) != null
                        && !StrUtil.equals(run.getTempData().getSubNo(), dto.getSubNo())
                        && StrUtil.equalsAny(subNoToSubmissionMap.get(run.getTempData().getSubNo()).getStatus(), SubmissionStatusEnum.waiting.name(), SubmissionStatusEnum.reviewing.name())
                ) {
                    errors.add(errMsg(line.getRowIndex(), "run_id", line.getRunName(),
                            StrUtil.format("The run_id is being used in submission {}, which is currently under review. Please wait until the review is complete, or you can withdraw {}.",
                                    run.getTempData().getSubNo(), run.getTempData().getSubNo())));
                    continue;
                }

                updateRunInfo(line, run, existRunRecordMap, expNameToExpMap, sapNameToSapMap, dto.getSubNo(), updateRunSet);

                // 存在Run
                // 更新temp_data
                updateTempData(dto.getSubNo(), dataResult, runNo, dataRecordMap.get(dataNo), line.getFileName(), line.getDataRemark());
                continue; // 中断后续代码，解析下一行
            }

            String runName = line.getRunName();

            // 提前校验 experiment_id、experiment_name、sample_id、sample_name
            boolean colCheckError = false;
            if (StrUtil.isNotBlank(line.getExpNo()) && StrUtil.isNotBlank(line.getExpName())) {
                errors.add(errMsg(line.getRowIndex(), "experiment_id, experiment_name", null, "If you fill in the run_name, the experiment_id and experiment_name columns, only choose one column to fill in."));
                colCheckError = true;
            }

            if (StrUtil.isNotBlank(line.getSapNo()) && StrUtil.isNotBlank(line.getSapName())) {
                errors.add(errMsg(line.getRowIndex(), "sample_id, sample_name", null, "If you fill in the run_name, the sample_id and sample_name columns, only choose one column to fill in."));
                colCheckError = true;
            }
            if (colCheckError) {
                continue;
            }

            // 校验只填写了runName，但是前面experiment 和 sample 的信息不一样
            if (StrUtil.isBlank(line.getRunNo()) && StrUtil.isNotBlank(runName)) {
                String exp = StrUtil.isNotBlank(line.getExpNo()) ? line.getExpNo() : line.getExpName();
                String sap = StrUtil.isNotBlank(line.getSapNo()) ? line.getSapNo() : line.getSapName();
                String value = exp + "*#*#" + sap;
                if (runNameInfoMap.containsKey(runName)) {
                    List<String> sameLine = runNameInfoMap.get(runName);
                    if (!StrUtil.equals(sameLine.get(0), value)) {
                        errors.add(errMsg(line.getRowIndex(), "run_name", runName, "Please ensure this run_name is under the same experiment and sample, and this run_name already exists at row " + sameLine.get(1)));
                        continue;
                    }
                } else {
                    runNameInfoMap.put(runName, CollUtil.newArrayList(value, String.valueOf(line.getRowIndex() + 1)));
                }
            }

            boolean hasError = false;
            // 完全新建Run
            // 校验exp_id 和 exp_name
            if (StrUtil.isBlank(line.getExpNo()) && StrUtil.isBlank(line.getExpName())) {
                hasError = true;
                errors.add(errMsg(line.getRowIndex(), "experiment_id, experiment_name", null, "must be filled in one"));
            } else if (StrUtil.isNotBlank(line.getExpNo())) {
                // 填写的id格式不正确
                if (!line.getExpNo().startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                    errors.add(errMsg(line.getRowIndex(), "experiment_id", line.getExpNo(), "ID format error"));
                } else {
                    // 校验id是否存在
                    Boolean existExpNo = expNoRecordMap.get(line.getExpNo());
                    if (!existExpNo) {
                        errors.add(errMsg(line.getRowIndex(), "experiment_id", line.getExpNo(), "not exist"));
                        hasError = true;
                    }
                }
            } else {
                // 校验name是否存在
                Boolean existExpName = expNameRecordMap.get(line.getExpName());
                if (!existExpName) {
                    errors.add(errMsg(line.getRowIndex(), "experiment_name", line.getExpName(), "not exist"));
                    hasError = true;
                }
            }

            // 校验sap_id 和 sap_name
            if (StrUtil.isBlank(line.getSapNo()) && StrUtil.isBlank(line.getSapName())) {
                hasError = true;
                errors.add(errMsg(line.getRowIndex(), "sample_id, sample_name", null, "must be filled in one"));
            } else if (StrUtil.isNotBlank(line.getSapNo())) {
                // 填写的id格式不正确
                if (!line.getSapNo().startsWith(SequenceType.SAMPLE.getPrefix())) {
                    errors.add(errMsg(line.getRowIndex(), "sample_id", line.getSapNo(), "ID format error"));
                } else {
                    // 校验id是否存在
                    Boolean existSapNo = sapNoRecordMap.get(line.getSapNo());
                    if (!existSapNo) {
                        errors.add(errMsg(line.getRowIndex(), "sample_id", line.getSapNo(), "not exist"));
                        hasError = true;
                    }
                }
            } else {
                // 校验name是否存在
                Boolean existSapName = sapNameRecordMap.get(line.getSapName());
                if (!existSapName) {
                    errors.add(errMsg(line.getRowIndex(), "sample_name", line.getSapName(), "not exist"));
                    hasError = true;
                }
            }


            // 归档到历史创建但是未分配正式ID的Run
            if (isnertRunRecordMap.containsKey(runName)) {
                Run run = isnertRunRecordMap.get(runName);

                if (run.getTempData() != null
                        && subNoToSubmissionMap.get(run.getTempData().getSubNo()) != null
                        && !StrUtil.equals(run.getTempData().getSubNo(), dto.getSubNo())
                        && StrUtil.equalsAny(subNoToSubmissionMap.get(run.getTempData().getSubNo()).getStatus(), SubmissionStatusEnum.waiting.name(), SubmissionStatusEnum.reviewing.name())
                ) {
                    errors.add(errMsg(line.getRowIndex(), "run_name", line.getRunName(),
                            StrUtil.format("The run_name is being used in submission {}, which is currently under review. Please wait until the review is complete, or you can withdraw {}.",
                                    run.getTempData().getSubNo(), run.getTempData().getSubNo())));
                    continue;
                }

                // 更新RUN的信息
                updateRunInfo(line, run, isnertRunRecordMap, expNameToExpMap, sapNameToSapMap, dto.getSubNo(), updateRunSet);

                // 更新temp_data
                updateTempData(dto.getSubNo(), dataResult, run.getRunNo(), dataRecordMap.get(dataNo), line.getFileName(), line.getDataRemark());
                continue;
            }


            if (hasError) {
                continue;
            }

            String expNo;
            // 查询experimentNo
            if (StrUtil.isNotBlank(line.getExpNo())) {
                expNo = line.getExpNo();
            } else {
                expNo = expNameToExpMap.get(line.getExpName()).getExpNo();
            }

            String sapNo;
            // 获取sapNo
            if (StrUtil.isNotBlank(line.getSapNo())) {
                sapNo = line.getSapNo();
            } else {
                sapNo = sapNameToSapMap.get(line.getSapName()).getSapNo();
            }

            if (newRunNameToRunNoMap.containsKey(runName)) {
                // 更新temp_data
                updateTempData(dto.getSubNo(), dataResult, newRunNameToRunNoMap.get(runName), dataRecordMap.get(dataNo), line.getFileName(), line.getDataRemark());
                continue;
            }

            // 新建RUN
            Run run = new Run();
            String id = IdUtil.fastSimpleUUID();
            run.setId(IdUtil.objectId());
            run.setRunNo(id);
            run.setSubNo(dto.getSubNo());
            run.setName(line.getRunName());
            run.setDescription(line.getRunDesc());
            run.setExpNo(expNo);
            run.setSapNo(sapNo);
            run.setAudited(AuditEnum.init.name());
            run.setVisibleStatus(VisibleStatusEnum.Unaccessible.name());
            run.setHitNum(0L);
            run.setCreateDate(new Date());
            run.setUpdateDate(new Date());
            run.setCreator(memberId);
            run.setOwnership(OwnershipEnum.self_support.getDesc());

            Run tempRun = RunDTOMapper.INSTANCE.copy(run);
            run.setTempData(tempRun);

            // 更新temp_data
            updateTempData(dto.getSubNo(), dataResult, run.getRunNo(), dataRecordMap.get(dataNo), line.getFileName(), line.getDataRemark());
            newRunNameToRunNoMap.put(run.getName(), run.getRunNo());
            newRun.add(run);
        }

        if (CollUtil.isNotEmpty(errors)) {
            // 返回错误数据
            return errors;
        }

        // 找到submission
        Submission submission = getEditSubmissionByNo(dto.getSubNo());

        // 获取rawDataMultiNos
        List<String> oldDataNos = submission.getRawDataMultipleNos();
        // single归档的数据如果被回滚了也得被删除
        List<String> rawDataNos = submission.getRawDataNos();
        List<String> newDataNos = dataResult.stream().map(Data::getDatNo).collect(Collectors.toList());

        // 撤回在历史中存在，在新的中不存在的数据
        if (CollUtil.isNotEmpty(oldDataNos)) {
            // 计算集合的单差集进行回滚数据，即只返回【oldDataNos】中有，但是【newDataNos】中没有的元素
            List<String> disjunction = CollUtil.subtractToList(oldDataNos, newDataNos);
            Set<String> disjunctionData = CollUtil.newHashSet(disjunction);
            List<Data> disjunctionDataList = dataRepository.findAllPrivateByDataNoIn(disjunctionData, SecurityUtils.getMemberId());
            for (Data data : disjunctionDataList) {
                if (data.getArchived().equals(ArchiveEnum.yes.name())) {
                    data.setTempData(null);
                } else {
                    // 数据回滚
                    Data tempData = DataDTOMapper.INSTANCE.copy(data);
                    tempData.setRunNo(null);
                    tempData.setAnalNo(null);
                    tempData.setSubNo(null);
                    tempData.setArchived(ArchiveEnum.no.name());
                    data.setTempData(tempData);
                }
                // 需要将submission单独归档中如果存在这个数据，则将这个记录删除
                if (CollUtil.isNotEmpty(rawDataNos)) {
                    rawDataNos.remove(data.getDatNo());
                }
            }
            dataRepository.saveAll(disjunctionDataList);
        }
        // 更新已存在的run
        if (CollUtil.isNotEmpty(updateRunSet)) {
            runRepository.saveAll(updateRunSet);
        }

        // 批量保存数据
        runRepository.saveAll(newRun);
        dataRepository.saveAll(dataResult);

        // 更新Submission
        submission.setRawDataMultipleNos(CollUtil.isEmpty(newDataNos) ? null : newDataNos);
        submission.setRawDataNos(CollUtil.isEmpty(rawDataNos) ? null : rawDataNos);
        saveEditSubmission(submission);
        return null;
    }

    /**
     * 用户修改已经有正式ID的Run名称和描述信息
     */
    private void updateRunInfo(ArchiveImportDTO line, Run run,
                               Map<String, Run> runMap,
                               Map<String, Experiment> expNameToExpMap,
                               Map<String, Sample> sapNameToSapMap,
                               String subNo,
                               Set<Run> updateRunList) {
        String linenName = line.getRunName();
        String linenDesc = line.getRunDesc();
        String lineRunNo = line.getRunNo();
        String lineExpNo = line.getExpNo();
        String lineExpName = line.getExpName();
        String lineSapNo = line.getSapNo();
        String lineSapName = line.getSapName();

        boolean update = false;

        Run tempData = run.getTempData();
        if (tempData == null) {
            tempData = RunDTOMapper.INSTANCE.copy(run);
        }

        String runName = tempData.getName();
        String runDesc = tempData.getDescription();
        String expNo = tempData.getExpNo();
        String sapNo = tempData.getSapNo();
        if (StrUtil.isNotBlank(linenName) && !linenName.equals(runName)) {
            tempData.setName(linenName);
            update = true;
        }
        if (!StrUtil.equals(linenDesc, runDesc)) {
            tempData.setDescription(linenDesc);
            update = true;
        }
        // 如果run更新了所属的exp或者sample，需要更新tempData
        if (StrUtil.isNotBlank(lineExpName)) {
            Experiment experiment = expNameToExpMap.get(lineExpName);
            if (experiment == null) {
                throw new ServiceException("There is an exception when submitting archived data, please contact the administrator!");
            }
            if (!StrUtil.equals(experiment.getExpNo(), expNo)) {
                tempData.setExpNo(experiment.getExpNo());
                update = true;
                if (StrUtil.equals(run.getAudited(), AuditEnum.init.name())) {
                    run.setExpNo(experiment.getExpNo());
                }
            }
        }
        if (StrUtil.isNotBlank(lineSapName)) {
            Sample sample = sapNameToSapMap.get(lineSapName);
            if (sample == null) {
                throw new ServiceException("There is an exception when submitting archived data, please contact the administrator!");
            }
            if (!StrUtil.equals(sample.getSapNo(), sapNo)) {
                tempData.setSapNo(sample.getSapNo());
                update = true;
                // 如果这个run的外层是init
                if (StrUtil.equals(run.getAudited(), AuditEnum.init.name())) {
                    run.setSapNo(sample.getSapNo());
                }
            }
        }
        // 如果是新创建的run更新exp或者sample，需要更新tempData
        if (StrUtil.isBlank(lineRunNo) && StrUtil.isNotBlank(lineExpNo) && !StrUtil.equals(lineExpNo, expNo)) {
            tempData.setExpNo(lineExpNo);
            update = true;
            if (StrUtil.equals(run.getAudited(), AuditEnum.init.name())) {
                run.setExpNo(lineExpNo);
            }
        }
        if (StrUtil.isBlank(lineRunNo) && StrUtil.isNotBlank(lineSapNo) && !StrUtil.equals(lineSapNo, sapNo)) {
            tempData.setSapNo(lineSapNo);
            update = true;
            if (StrUtil.equals(run.getAudited(), AuditEnum.init.name())) {
                run.setSapNo(lineSapNo);
            }
        }

        if (update) {
            tempData.setSubNo(subNo);
            tempData.setAudited(AuditEnum.unaudited.name());
            tempData.setUpdateDate(new Date());
            run.setTempData(tempData);
            // runRepository.save(run);
            updateRunList.add(run);
            runMap.put(run.getRunNo(), run);
        }
    }

    /**
     * 将输入的no全部填充为8位数
     * OEX000001 ==> OEX00000001
     */
    private void parseRawDataArchiveImportMainNo(List<ArchiveImportDTO> archiveImportDTOS) {
        for (ArchiveImportDTO dto : archiveImportDTOS) {
            if (StrUtil.isNotBlank(dto.getExpNo())) {
                // "OEX000001"==>"OEX00000001"
                dto.setExpNo(NodeUtils.get8NumberNo(dto.getExpNo()));
            }
            if (StrUtil.isNotBlank(dto.getSapNo())) {
                dto.setSapNo(NodeUtils.get8NumberNo(dto.getSapNo()));
            }
            if (StrUtil.isNotBlank(dto.getRunNo())) {
                dto.setRunNo(NodeUtils.get8NumberNo(dto.getRunNo()));
            }
            if (StrUtil.isNotBlank(dto.getDataNos())) {
                dto.setDataNos(NodeUtils.get8NumberNo(dto.getDataNos()));
            }
        }
    }

    public void delete(String subNo, String type, Boolean single) {
        if (StrUtil.isBlank(subNo) || single == null || StrUtil.isBlank(type)) {
            throw new ServiceException("The request parameter is illegal");
        }

        Submission submission = getEditSubmissionByNo(subNo);

        List<String> nos = null;
        if (single) {
            if (type.equals(SubmissionDataTypeEnum.rawData.name())) {
                nos = submission.getRawDataNos();
                submission.setRawDataNos(null);
            }
            if (type.equals(SubmissionDataTypeEnum.analysisData.name())) {
                nos = submission.getAnalysisDataNos();
                submission.setAnalSingleNo(null);
            }
        } else {
            if (type.equals(SubmissionDataTypeEnum.rawData.name())) {
                nos = submission.getRawDataMultipleNos();
                submission.setRawDataMultipleNos(null);
            }
            if (type.equals(SubmissionDataTypeEnum.analysisData.name())) {
                nos = submission.getAnalDataMultipleNos();
                submission.setAnalDataMultipleNos(null);
            }
        }

        if (CollUtil.isEmpty(nos)) {
            throw new ServiceException("Delete failed, there is no data to delete");
        }

        List<Data> datas = dataRepository.findAllPrivateByDataNoIn(nos, SecurityUtils.getMemberId());

        if (CollUtil.isEmpty(datas)) {
            throw new ServiceException("Delete failed, no data found for deletion");
        }

        Set<String> runNos = new HashSet<>();

        for (Data data : datas) {
            // 如果是rawData，需要获取run_no，然后再更新data的temp_data
            if (StrUtil.equals(type, SubmissionDataTypeEnum.rawData.name())
                    && StrUtil.isNotBlank(data.getTempData().getRunNo())
                    && !StrUtil.startWith(data.getTempData().getRunNo(), SequenceType.RUN.getPrefix())) {
                runNos.add(data.getTempData().getRunNo());
            }
            if (data.getArchived().equals(ArchiveEnum.yes.name())) {
                data.setTempData(null);
            } else {
                Data tempData = DataDTOMapper.INSTANCE.copy(data);
                data.setTempData(tempData);
            }
            dataRepository.save(data);
        }

        // 需要删除没有被 data 关联的 run（audited:init）
        if (CollUtil.isNotEmpty(runNos)) {
            // 找出那些run下还有data
            List<Data> relatedData = dataRepository.findByTempDataRunNoIn(runNos);
            List<String> hasAttachRunNos = relatedData.stream().map(x -> x.getTempData().getRunNo()).distinct().collect(Collectors.toList());

            // runNos和hasAttachRunNos取差集
            List<String> subtractRunNos = CollUtil.subtractToList(runNos, hasAttachRunNos);

            // 删除没有关联的数据
            runRepository.deleteByRunNoInAndAuditedInit(subtractRunNos);
        }

        saveEditSubmission(submission);
    }

    private static void updateTempData(String subNo, List<Data> resultData, String runNo, Data data, String newFileName, String remark) {
        if (data == null) {
            return;
        }
        Data tempData = data.getTempData();

        // 从已归档已审核状态转换待审核归档
        if (tempData == null) {
            tempData = DataDTOMapper.INSTANCE.copy(data);
        }

        // 清除analysisNo的关联
        tempData.setAnalNo(null);
        tempData.setSubNo(subNo);
        tempData.setArchived(ArchiveEnum.yes.name());
        tempData.setRunNo(runNo);
        tempData.setUpdateDate(new Date());
        tempData.setRemark(remark);
        // 更新Data Name
        if (StrUtil.isNotBlank(newFileName)) {
            tempData.setName(newFileName);
            tempData.setFileName(newFileName);
        }

        data.setTempData(tempData);
        resultData.add(data);
    }

    private Map<String, Run> createIsnertRunMap(List<ArchiveImportDTO> archiveImportDTOS) {
        Map<String, Run> resultMap = new HashMap<>();

        if (CollUtil.isEmpty(archiveImportDTOS)) {
            return resultMap;
        }

        Set<String> runNames = new HashSet<>();

        for (ArchiveImportDTO line : archiveImportDTOS) {
            String runNo = line.getRunNo();
            String runName = line.getRunName();
            if (StrUtil.isNotBlank(runName) && StrUtil.isBlank(runNo)) {
                runNames.add(runName.trim());
            }
        }

        List<Run> runList = runRepository.findAllInitByNames(runNames, SecurityUtils.getMemberId());

        if (CollUtil.isEmpty(runList)) {
            return resultMap;
        }

        return runList.stream().collect(Collectors.toMap(Run::getName, Function.identity(), (existingValue, newValue) -> existingValue));
    }

    private Map<String, Run> createExistRunMap(List<ArchiveImportDTO> archiveImportDTOS) {
        Map<String, Run> resultMap = new HashMap<>();

        if (CollUtil.isEmpty(archiveImportDTOS)) {
            return resultMap;
        }

        Set<String> runNos = new HashSet<>();

        for (ArchiveImportDTO line : archiveImportDTOS) {
            String runNo = line.getRunNo();
            if (StrUtil.isNotBlank(runNo) && runNo.trim().startsWith(SequenceType.RUN.getPrefix())) {
                runNos.add(runNo);
            }
        }

        List<Run> runList = runRepository.findAllByCreator(runNos, SecurityUtils.getMemberId());

        if (CollUtil.isEmpty(runList)) {
            return resultMap;
        }

        return runList.stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));
    }

    private Map<String, Data> createDataMap(List<ArchiveImportDTO> archiveImportDTOS) {

        if (CollUtil.isEmpty(archiveImportDTOS)) {
            return null;
        }

        Set<String> dataNos = new HashSet<>();

        for (ArchiveImportDTO line : archiveImportDTOS) {

            String dataNosLine = line.getDataNos();

            if (StrUtil.isBlank(dataNosLine)) {
                continue;
            }

            dataNos.add(dataNosLine);
        }

        List<Data> data = dataRepository.findAllPrivateByDataNoIn(dataNos, SecurityUtils.getMemberId());

        if (CollUtil.isEmpty(data)) {
            return null;
        }

        return data.stream().collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));
    }

    private Map<String, Data> createAnalDataMap(List<ArchiveAnalysisImportDTO> archiveImportDTOS) {

        if (CollUtil.isEmpty(archiveImportDTOS)) {
            return null;
        }

        Set<String> dataNos = new HashSet<>();

        for (ArchiveAnalysisImportDTO line : archiveImportDTOS) {

            String dataNosLine = line.getDataNo();

            if (StrUtil.isBlank(dataNosLine)) {
                continue;
            }

            dataNos.add(dataNosLine.trim());
        }

        List<Data> data = dataRepository.findAllPrivateByDataNoIn(dataNos, SecurityUtils.getMemberId());

        if (CollUtil.isEmpty(data)) {
            return null;
        }

        return data.stream().collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));
    }

    /**
     * 获取表格数据
     *
     * @param subNo    Submission No
     * @param editPage 编辑页 or 详情页
     * @param fromPassMethod 是审核方法调用的
     */
    public List<Map<String, Object>> getMultiRawDataArchiveBySubNo(String subNo, boolean editPage, boolean fromPassMethod) {
        if (StrUtil.isBlank(subNo)) {
            return null;
        }
        // 查询回显信息
        Submission submission = getSubmissionByNo(subNo);
        if (editPage) {
            submission = getEditSubmissionByNo(subNo);
        }
        List<String> rawDataMultipleNos = submission.getRawDataMultipleNos();

        if (!editPage) {
            if (CollUtil.isEmpty(rawDataMultipleNos)) {
                rawDataMultipleNos = new ArrayList<>();
            }
            List<String> rawDataNos = submission.getRawDataNos();
            if (CollUtil.isNotEmpty(rawDataNos)) {
                rawDataMultipleNos.addAll(rawDataNos);
            }
        }
        if (CollUtil.isEmpty(rawDataMultipleNos)) {
            return null;
        }
        // 一定要去重，避免出现重复的行
        rawDataMultipleNos = rawDataMultipleNos.stream().distinct().collect(Collectors.toList());

        List<Map<String, Object>> rows = new ArrayList<>();

        Set<String> dataNos = new HashSet<>(rawDataMultipleNos);

        List<Data> dataList = dataRepository.getNoOpenDataByDatNos(dataNos);
        Map<String, Data> dataNoToDataMap = dataList.stream().collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> runNos = dataList.stream().filter(x -> x.getTempData() != null)
                .map(x -> x.getTempData().getRunNo()).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        List<Run> runList = runRepository.findAllByRunNoIn(runNos);

        List<String> expNos = runList.stream().map(Run::getExpNo)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        List<String> sapNos = runList.stream().map(Run::getSapNo)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        List<Experiment> expList = experimentRepository.findAllByExpNoIn(expNos);

        List<Sample> sapList = sampleRepository.findAllBySapNoIn(sapNos);

        Map<String, Run> runNoToRunMap = runList.stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Experiment> expNoToExpMap = expList.stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));
        Map<String, Sample> sapNoToSapMap = sapList.stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (String rowDataNo : rawDataMultipleNos) {

            Data data = dataNoToDataMap.get(rowDataNo);
            Data tempData = data.getTempData();
            if (tempData == null) {
                // 应该不能为空的，但是防止特殊情况空指针
                continue;
            }
            Map<String, Object> row = new HashMap<>();
            Run run = runNoToRunMap.get(tempData.getRunNo());
            if (run.getTempData() != null) {
                run = run.getTempData();
            }

            boolean formalRun = run.getRunNo().startsWith(SequenceType.RUN.getPrefix());
            if (formalRun) {
                row.put("run_id", run.getRunNo());
            }

            Experiment experiment = expNoToExpMap.get(run.getExpNo());
            if (experiment.getExpNo().startsWith(SequenceType.EXPERIMENT.getPrefix())) {
                if (editPage) {
                    row.put("experiment_id", formalRun ? "" : experiment.getExpNo());
                } else {
                    row.put("experiment_id", experiment.getExpNo());
                }
                row.put("experiment_name", editPage ? "" : experiment.getName());
            } else {
                if (fromPassMethod) {
                    row.put("experiment_id", experiment.getExpNo());
                } else {
                    row.put("experiment_id", "");
                }
                row.put("experiment_name", experiment.getName());
            }
            Sample sample = sapNoToSapMap.get(run.getSapNo());
            if (sample.getSapNo().startsWith(SequenceType.SAMPLE.getPrefix())) {
                if (editPage) {
                    row.put("sample_id", formalRun ? "" : sample.getSapNo());
                } else {
                    row.put("sample_id", sample.getSapNo());
                }
                row.put("sample_name", editPage ? "" : sample.getName());
            } else {
                if (fromPassMethod) {
                    row.put("sample_id", sample.getSapNo());
                } else {
                    row.put("sample_id", "");
                }
                row.put("sample_name", sample.getName());
            }
            if (fromPassMethod) {
                row.put("run_id", run.getRunNo());
            }

            row.put("run_name", run.getName());
            row.put("run_description", run.getDescription());

            row.put("data_id", rowDataNo);
            row.put("file_name", data.getTempData() != null ? data.getTempData().getName() : data.getName());
            row.put("data_remark", data.getTempData() != null ? data.getTempData().getRemark() : data.getRemark());
            row.put("file_type", data.getDataType());
            row.put("file_size", FileUtil.readableFileSize(data.getFileSize()));
            // 需要转成字符串,这样SubDataLog快照里面记录的是字符串
            row.put("upload_date", DateUtil.format(data.getCreateDate(), DatePattern.NORM_DATETIME_FORMAT));
            row.put("file_path", data.getFilePath());

            rows.add(row);
        }

        return rows;
    }

    public List<ErrorMsgVO> batchSaveAnalDataArchive(AbsBatchDTO dto) {
        String memberId = SecurityUtils.getMemberId();
        // 找到submission
        Submission submission = getEditSubmissionByNo(dto.getSubNo());

        // 校验填写的内容
        // 记录错误信息
        List<ErrorMsgVO> errors = new ArrayList<>();

        // 将数据转为对象
        List<ArchiveAnalysisImportDTO> archiveImportDTOS = new ArrayList<>();
        for (int i = 0; i < dto.getDatas().size(); i++) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("rowIndex", i);
            for (int j = 0; j < dto.getTitles().size(); j++) {
                Object o = dto.getDatas().get(i)[j];
                if (o == null) {
                    jsonObject.put(dto.getTitles().get(j), null);
                } else {
                    jsonObject.put(dto.getTitles().get(j), StrUtil.trimToNull(o.toString()));
                }
            }
            archiveImportDTOS.add(jsonObject.toJavaObject(ArchiveAnalysisImportDTO.class));
        }

        parseAnalDataArchiveImportMainNo(archiveImportDTOS);

        List<String> analNos = archiveImportDTOS.stream().map(ArchiveAnalysisImportDTO::getAnalNo)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> analNames = archiveImportDTOS.stream().map(ArchiveAnalysisImportDTO::getAnalName)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 缓存analysisId和analysisName
        Map<String, Boolean> analNoRecordMap = analysisRepository.existsByCreatorAndNos(analNos, memberId);
        Map<String, Boolean> analNameRecordMap = analysisRepository.existAuditInitAnalByAnalNames(analNames, memberId);

        // 记录已经准备入库的data
        Map<String, Integer> duplicateData = new HashMap<>();

        // Data ID所属的原有分组
        Map<String, Data> dataRecordMap = createAnalDataMap(archiveImportDTOS);

        // 如果用户提交的所有的data no在非公开的数据中都无法找到，则不执行任何校验，返回错误
        if (CollUtil.isEmpty(dataRecordMap)) {
            errors.add(errMsg(null, "data_id", "All data", "Unable to find all data in non-public data"));
            return errors;
        }

        // 校验analysisName
        for (ArchiveAnalysisImportDTO line : archiveImportDTOS) {

            // 校验dataNo
            if (StrUtil.isBlank(line.getDataNo())) {
                errors.add(errMsg(line.getRowIndex(), "data_id", null, "can't be empty"));
            } else {
                String dataNo = line.getDataNo();
                // 填写的id的格式不正确
                if (!dataNo.startsWith(SequenceType.DATA.getPrefix())) {
                    errors.add(errMsg(line.getRowIndex(), "data_id", dataNo, "ID format error"));
                } else {
                    // 校验dataNo是否填写重复
                    if (duplicateData.containsKey(dataNo)) {
                        errors.add(errMsg(line.getRowIndex(), "data_id", dataNo,
                                "The data ID already exists in line " + duplicateData.get(dataNo)));
                    } else {
                        // 校验Data是否存在
                        if (!dataRecordMap.containsKey(dataNo)) {
                            errors.add(errMsg(line.getRowIndex(), "data_id", dataNo, "not found in your private data"));
                        } else {
                            Data data = dataRecordMap.get(dataNo);
                            Data tempData = data.getTempData();
                            if (tempData != null && tempData.getSubNo() != null && !dto.getSubNo().equals(tempData.getSubNo())) {
                                errors.add(errMsg(line.getRowIndex(), "data_id", dataNo, "The data ID has already been used in submission: " + tempData.getSubNo()));
                            }
                            String fileName = line.getFileName();
                            if (StrUtil.isNotBlank(fileName)) {
                                if (!MyFileUtils.isValidFilename(fileName)) {
                                    errors.add(errMsg(line.getRowIndex(), "file_name", fileName, "The file name contains illegal characters is not allowed. Such as spaces, ampersands (&), percent signs (%), asterisks (*), or Greek letters: " + fileName));
                                }
                                String oldDataNameSuffix = MyFileUtils.getFileNameSuffix(data.getName());
                                String newDataNameSuffix = MyFileUtils.getFileNameSuffix(fileName);
                                if (!StrUtil.equals(oldDataNameSuffix, newDataNameSuffix)) {
                                    errors.add(errMsg(line.getRowIndex(), "file_name", fileName, "The file name suffix is not consistent with the original data name: " + fileName));
                                }
                            }
                        }
                        if (duplicateData.containsKey(dataNo)) {
                            errors.add(errMsg(line.getRowIndex(), "data_id", dataNo, "The data ID already exists in line " + duplicateData.get(dataNo)));
                        }
                        duplicateData.put(dataNo, line.getRowIndex() + 1);
                    }
                }
            }

            // 校验analNo以及analName
            if (StrUtil.isBlank(line.getAnalNo()) && StrUtil.isBlank(line.getAnalName())) {
                errors.add(errMsg(line.getRowIndex(), "analysis_id, analysis_name", null, "must be filled in one"));
            } else if (StrUtil.isNotBlank(line.getAnalNo()) && StrUtil.isNotBlank(line.getAnalName())) {
                errors.add(errMsg(line.getRowIndex(), "analysis_id, analysis_name", null, "the analysis_id and analysis_name columns, only choose one column to fill in"));
            } else if (StrUtil.isNotBlank(line.getAnalNo())) {
                // 填写的id格式不正确
                if (!line.getAnalNo().startsWith(SequenceType.ANALYSIS.getPrefix())) {
                    errors.add(errMsg(line.getRowIndex(), "analysis_id", line.getAnalNo(), "ID format error"));
                } else {
                    // 校验id是否存在
                    Boolean b = analNoRecordMap.get(line.getAnalNo());
                    if (!b) {
                        errors.add(errMsg(line.getRowIndex(), "analysis_id", line.getAnalNo(), "not exist"));
                    }
                }
            } else {
                // 校验analName
                Boolean b = analNameRecordMap.get(line.getAnalName());
                if (!b) {
                    errors.add(errMsg(line.getRowIndex(), "analysis_name", line.getAnalName(), "not exist"));
                }
            }
        }

        // 如果有错就返回错误信息
        if (CollUtil.isNotEmpty(errors)) {
            return errors;
        }

        // 否者直接返回
        List<Data> saveList = new ArrayList<>();
        List<Data> updateList = new ArrayList<>();

        List<Analysis> analList = analysisRepository.findAllAuditInitAnalByAnalNames(analNames, memberId);
        Map<String, Analysis> analNameToAnalMap = analList.stream().collect(Collectors.toMap(Analysis::getName, Function.identity(), (existingValue, newValue) -> existingValue));
        List<String> allDataNos = new ArrayList<>();

        for (ArchiveAnalysisImportDTO line : archiveImportDTOS) {
            String dataNos = line.getDataNo();
            allDataNos.add(dataNos);
        }

        List<Data> dataList = dataRepository.findAllByDataNoIn(allDataNos, memberId);

        Map<String, Data> dataNoToDataMap = dataList.stream().collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 查询data
        for (ArchiveAnalysisImportDTO line : archiveImportDTOS) {
            String dataNo = line.getDataNo();
            Data data = dataNoToDataMap.get(dataNo);

            Data tempData;
            // 判断是新增还是修改
            // 重新修改关联
            if (data.getTempData() == null) {
                tempData = DataDTOMapper.INSTANCE.copy(data);
            } else {
                // 新增关联
                tempData = data.getTempData();
            }
            // 清空关联
            tempData.setRunNo(null);
            // 找出关联数据
            if (StrUtil.isNotBlank(line.getAnalNo())) {
                tempData.setAnalNo(line.getAnalNo());
            } else {
                tempData.setAnalNo(analNameToAnalMap.get(line.getAnalName()).getAnalysisNo());
            }
            // 更新Data name
            String fileName = line.getFileName();
            if (StrUtil.isNotBlank(fileName)) {
                tempData.setName(fileName);
                tempData.setFileName(fileName);
            }
            tempData.setRemark(line.getDataRemark());

            tempData.setArchived(ArchiveEnum.yes.name());
            tempData.setSubNo(submission.getSubNo());
            tempData.setUpdateDate(new Date());

            data.setTempData(tempData);
            saveList.add(data);
        }
        // 过滤出需要保存的Data
        List<String> saveDataNos = saveList.stream().map(Data::getDatNo).collect(Collectors.toList());

        List<String> flatAnalDataMultipleNos = submission.getAnalDataMultipleNos();

        // 取差集需要回滚的Data的DataNo
        List<String> rollBackDataNos = CollUtil.subtractToList(flatAnalDataMultipleNos, saveDataNos);
        List<Data> rollBackDatas = dataRepository.findAllByDataNoIn(rollBackDataNos, memberId);
        Map<String, Data> dataNoToRollbackDataMap = rollBackDatas.stream()
                .collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 如果回滚的数据中包含了在single中的数据，得删除
        List<String> analysisDataNos = submission.getAnalysisDataNos();
        for (String dataNo : rollBackDataNos) {
            // 找出这些Data
            Data data = dataNoToRollbackDataMap.get(dataNo);
            if (data.getArchived().equals(ArchiveEnum.yes.name())) {
                data.setTempData(null);
            } else {
                // 数据回滚
                Data tempData = DataDTOMapper.INSTANCE.copy(data);
                tempData.setRunNo(null);
                tempData.setAnalNo(null);
                tempData.setSubNo(null);
                tempData.setArchived(ArchiveEnum.no.name());
                data.setTempData(tempData);
            }
            if (CollUtil.isNotEmpty(analysisDataNos)) {
                analysisDataNos.remove(data.getDatNo());
            }
            updateList.add(data);
        }
        dataRepository.saveAll(saveList);
        dataRepository.saveAll(updateList);

        // 获取需要保存的dataNo
        List<String> saveDataNos2 = archiveImportDTOS.stream().map(ArchiveAnalysisImportDTO::getDataNo).collect(Collectors.toList());
        // 直接保存数据
        submission.setAnalDataMultipleNos(CollUtil.isEmpty(saveDataNos2) ? null : saveDataNos2);
        submission.setAnalysisDataNos(CollUtil.isEmpty(analysisDataNos) ? null : analysisDataNos);
        // 保存数据
        saveEditSubmission(submission);
        return null;
    }

    private void parseAnalDataArchiveImportMainNo(List<ArchiveAnalysisImportDTO> archiveImportDTOS) {
        for (ArchiveAnalysisImportDTO dto : archiveImportDTOS) {
            if (StrUtil.isNotBlank(dto.getAnalNo())) {
                dto.setAnalNo(NodeUtils.get8NumberNo(dto.getAnalNo()));
            }
            if (StrUtil.isNotBlank(dto.getDataNo())) {
                dto.setDataNo(NodeUtils.get8NumberNo(dto.getDataNo()));
            }
        }
    }

    /**
     * 获取表格数据
     *
     * @param subNo    Submission No
     * @param editPage 编辑页 or 详情页
     * @param fromPassMethod 是否是pass方法调用
     */
    public List<ArchiveAnalysisImportDTO> getMultiAnalArchiveBySubNo(String subNo, boolean editPage, boolean fromPassMethod) {

        // 查询回显信息
        Submission submission = getSubmissionByNo(subNo);
        if (editPage) {
            submission = getEditSubmissionByNo(subNo);
        }
        List<String> analDataMultipleNos = submission.getAnalDataMultipleNos();

        if (!editPage) {
            if (CollUtil.isEmpty(analDataMultipleNos)) {
                analDataMultipleNos = new ArrayList<>();
            }
            List<String> rawDataNos = submission.getAnalysisDataNos();
            if (CollUtil.isNotEmpty(rawDataNos)) {
                analDataMultipleNos.addAll(rawDataNos);
            }
        }
        if (CollUtil.isEmpty(analDataMultipleNos)) {
            return null;
        }
        // 一定要去重，避免出现重复的行
        analDataMultipleNos = analDataMultipleNos.stream().distinct().collect(Collectors.toList());

        List<ArchiveAnalysisImportDTO> result = new ArrayList<>();

        Set<String> dataNos = new HashSet<>(analDataMultipleNos);

        List<Data> dataList = dataRepository.getNoOpenDataByDatNos(dataNos);
        Map<String, Data> dataNoToDataMap = dataList.stream().collect(Collectors.toMap(Data::getDatNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> analNos = dataList.stream().filter(x -> x.getTempData() != null)
                .map(x -> x.getTempData().getAnalNo()).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        List<Analysis> analList = analysisRepository.findAllByAnalysisNoIn(analNos);

        Map<String, Analysis> analNoToAnalMap = analList.stream().collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (String rowDataNo : analDataMultipleNos) {
            ArchiveAnalysisImportDTO dto = new ArchiveAnalysisImportDTO();
            Data data = dataNoToDataMap.get(rowDataNo);
            Data tempData = data.getTempData();
            dto.setDataNo(rowDataNo);
            String analNo = tempData.getAnalNo();
            Analysis anal = analNoToAnalMap.get(analNo);
            if (anal.getTempData() != null) {
                anal = anal.getTempData();
            }
            // 如果是编辑页面
            if (analNo.startsWith(SequenceType.ANALYSIS.getPrefix())) {
                dto.setAnalNo(analNo);
                dto.setAnalName(editPage ? "" : anal.getName());
            } else {
                if (fromPassMethod) {
                    dto.setAnalNo(analNo);
                } else {
                    dto.setAnalNo("");
                }
                dto.setAnalName(anal.getName());
            }
            dto.setFileName(data.getTempData() != null ? data.getTempData().getName() : data.getName());
            dto.setDataRemark(data.getTempData() != null ? data.getTempData().getRemark() : data.getRemark());
            dto.setFileType(data.getDataType());
            dto.setFileSize(FileUtil.readableFileSize(data.getFileSize()));
            dto.setUploadDate(data.getCreateDate());
            dto.setFilePath(data.getFilePath());
            result.add(dto);
        }
        return result;
    }

    public void cancel(ArchivingCancelDTO dto) {
        List<String> dataNos = dto.getDataNos();
        Submission submission = getEditSubmissionByNo(dto.getSubNo());
        List<Data> dataList = dataRepository.findAllByDataNoIn(dataNos, SecurityUtils.getMemberId());

        Set<String> runNos = new HashSet<>();

        for (Data data : dataList) {
            if (StrUtil.equals(dto.getType(), SubmissionDataTypeEnum.rawData.name())
                    && StrUtil.isNotBlank(data.getTempData().getRunNo())
                    && !StrUtil.startWith(data.getTempData().getRunNo(), SequenceType.RUN.getPrefix())) {
                runNos.add(data.getTempData().getRunNo());
            }
            // 如果data外层是已归档，就把tempData set为null 来恢复数据
            if (StrUtil.equals(data.getArchived(), ArchiveEnum.yes.name())) {
                data.setTempData(null);
            } else {
                // 数据回滚
                Data tempData = DataDTOMapper.INSTANCE.copy(data);
                tempData.setRunNo(null);
                tempData.setAnalNo(null);
                tempData.setSubNo(null);
                tempData.setArchived(ArchiveEnum.no.name());
                data.setTempData(tempData);
            }
        }
        if (StrUtil.equals(dto.getType(), SubmissionDataTypeEnum.rawData.name())) {
            List<String> rawDataNos = submission.getRawDataNos();
            List<String> dataMultipleNos = submission.getRawDataMultipleNos();

            if (CollUtil.isNotEmpty(dataMultipleNos)) {
                dataMultipleNos.removeAll(dataNos);
                submission.setRawDataMultipleNos(dataMultipleNos);
            }

            if (CollUtil.isNotEmpty(rawDataNos)) {
                rawDataNos.removeAll(dataNos);
                submission.setRawDataNos(rawDataNos);
            }
        } else if (StrUtil.equals(dto.getType(), SubmissionDataTypeEnum.analysisData.name())) {
            List<String> analysisDataNos = submission.getAnalysisDataNos();
            List<String> analDataMultipleNos = submission.getAnalDataMultipleNos();

            if (CollUtil.isNotEmpty(analDataMultipleNos)) {
                analDataMultipleNos.removeAll(dataNos);
                submission.setAnalDataMultipleNos(analDataMultipleNos);
            }

            if (CollUtil.isNotEmpty(analysisDataNos)) {
                analysisDataNos.removeAll(dataNos);
                submission.setAnalysisDataNos(analysisDataNos);
            }
        }
        // 保存dataList
        dataRepository.saveAll(dataList);

        // 需要删除没有被 data 关联的 run（audited:init）
        if (CollUtil.isNotEmpty(runNos)) {
            // 找出那些run下还有data
            List<Data> relatedData = dataRepository.findByTempDataRunNoIn(runNos);
            List<String> hasAttachRunNos = relatedData.stream().map(x -> x.getTempData().getRunNo()).distinct().collect(Collectors.toList());

            // runNos和hasAttachRunNos取差集
            List<String> subtractRunNos = CollUtil.subtractToList(runNos, hasAttachRunNos);

            // 删除没有关联的数据
            runRepository.deleteByRunNoInAndAuditedInit(subtractRunNos);
        }

        // 保存submission
        saveEditSubmission(submission);
    }
}
