package org.biosino.upload.dto.mapper;

import org.biosino.common.mongo.entity.Project;
import org.biosino.upload.dto.ProjectDTO;
import org.biosino.upload.vo.ProjectVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 实验对象拷贝
 *
 * <AUTHOR>
 */
@Mapper
public interface ProjectDTOMapper {
    ProjectDTOMapper INSTANCE = Mappers.getMapper(ProjectDTOMapper.class);

    @Mapping(target = "tempData", ignore = true)
    Project copy(Project project);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tempData", ignore = true)
    void tempToDb(Project source, @MappingTarget Project target);

    void copyToDb(ProjectDTO sourceDto, @MappingTarget Project project);

    void copyToVo(Project project, @MappingTarget ProjectVO result);
}
