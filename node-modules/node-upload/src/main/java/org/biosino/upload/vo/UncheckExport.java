package org.biosino.upload.vo;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/6
 */
@Data
public class UncheckExport {
    @Excel(name = "File Name")
    private String name;

    @Excel(name = "File Path")
    private String relativePath;

    @Excel(name = "Upload Date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date uploadDate;

    @Excel(name = "Upload Type")
    private String uploadType;

    @Excel(name = "File Size")
    private String readableSize;

    @Excel(name = "MD5 File")
    private String md5FileStatus;

    @Excel(name = "File MD5")
    private String md5;

    @Excel(name = "Provided MD5")
    private String md5FileContent;

}
