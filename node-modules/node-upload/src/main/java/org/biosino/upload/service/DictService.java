package org.biosino.upload.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.domain.Select;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.security.utils.DictUtils;
import org.biosino.common.security.utils.ExpSampleTokenUtils;
import org.biosino.system.api.domain.SysDictData;
import org.biosino.upload.repository.ExpSampleTypeRepository;
import org.biosino.upload.repository.SubmissionRepository;
import org.biosino.upload.vo.SampleTypeVO;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DictService {
    private final ExpSampleTypeRepository expSampleTypeRepository;
    private final SubmissionRepository submissionRepository;

    public List<String> getDictValue(String type) {

        List<SysDictData> dictDataList = DictUtils.getDictCache(type);

        if (CollUtil.isEmpty(dictDataList)) {
            return null;
        }

        return dictDataList.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
    }

    public List<Select> getTypeData(String type) {

        List<SysDictData> dictDataList = DictUtils.getDictCache(type);

        if (CollUtil.isEmpty(dictDataList)) {
            return null;
        }

        List<Select> selectList = new ArrayList<>();

        for (SysDictData sysDictData : dictDataList) {
            selectList.add(new Select(sysDictData.getDictLabel(), sysDictData.getDictValue()));
        }

        return selectList;
    }

    public ExpSampleType getExpSapDictData(String type) {
        if (StrUtil.isBlank(type)) {
            return null;
        }
        ExpSampleType expSampleType = expSampleTypeRepository.findByName(type);

        if (expSampleType == null) {
            throw new ServiceException("Experiment dictionary data not found exp data");
        }
//        checkExpSapPermission(type, expSampleType);
        List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
        if (CollUtil.isEmpty(attributes)) {
            throw new ServiceException("The " + type + " type has no configured related properties");
        }
        attributes = attributes.stream().filter(x -> DataStatusEnum.enable.name().equals(x.getStatus())).sorted(new ExpSampleType.MyAttributesComparator()).collect(Collectors.toList());
        if (CollUtil.isEmpty(attributes)) {
            throw new ServiceException("The " + type + " type has no enable attributes");
        }
        expSampleType.setAttributes(attributes);
        return expSampleType;
    }

    public Submission getSubmissionByNo(String subNo) {
        return submissionRepository.findTopBySubNo(subNo).orElseThrow(() -> new ServiceException("Submission data not found"));
    }

    public SampleTypeVO getSampleType(final String subNo) {
        List<String> savedMultTypes = null;
        if (StrUtil.isNotBlank(subNo)) {
            Submission submission = getSubmissionByNo(subNo);
            final List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();
            if (CollUtil.isNotEmpty(sapMultipleData)) {
                savedMultTypes = new ArrayList<>();
                for (Submission.SampleGroup sampleGroup : sapMultipleData) {
                    savedMultTypes.add(sampleGroup.getType());
                }
            }
        }
        final SampleTypeVO vo = new SampleTypeVO();

        // 如果启用了License功能只返回有权限的模板，否则返回全部
        if (ExpSampleTokenUtils.getLicenseStatus()) {
            final List<String> sampleName = ExpSampleTokenUtils.getAllSampleName();
            vo.setTypes(expSampleTypeRepository.findAllByNameIn(sampleName));
        } else {
            vo.setTypes(expSampleTypeRepository.findAllByType(ExpSampleTypeEnum.sample));
        }
        vo.setSavedMultTypes(savedMultTypes);
        return vo;
    }

    public List<ExpSampleType> getExperimentType() {
        // 如果启用了License功能只返回有权限的模板，否则返回全部
        if (ExpSampleTokenUtils.getLicenseStatus()) {
            final List<String> allExperiment = ExpSampleTokenUtils.getAllExperimentName();
            return expSampleTypeRepository.findAllByNameIn(allExperiment);
        } else {
            return expSampleTypeRepository.findAllByType(ExpSampleTypeEnum.experiment);
        }
    }
}
