package org.biosino.upload.dto;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

@Data
public class SubmitterDTO {

    private String subNo;

    @NotBlank
    private String dataType;

    @NotBlank
    private String firstName;

    private String middleName;

    @NotBlank
    private String lastName;

    @NotBlank
    private String orgName;

    private String deptName;

    private String piName;

    @Email
    @NotBlank
    private String email;

    private String phone;

    private String fax;

    private String street;

    private String city;

    private String stateProvince;

    private String postalCode;

    private String countryRegion;

}
