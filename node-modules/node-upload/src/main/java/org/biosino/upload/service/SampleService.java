package org.biosino.upload.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.Select;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.enums.dict.BaseAttrType;
import org.biosino.common.core.enums.dict.ExpSampleDataType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.core.utils.node.TaxonomyEsDTO;
import org.biosino.common.core.utils.node.TaxonomyUtil;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.RemoteDictService;
import org.biosino.es.api.external.DmsApi;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.biosino.upload.api.vo.DeleteErrorMsgVO;
import org.biosino.upload.dto.*;
import org.biosino.upload.dto.mapper.SampleDTOMapper;
import org.biosino.upload.repository.*;
import org.biosino.upload.vo.ErrorMsgVO;
import org.biosino.upload.vo.PublishVO;
import org.biosino.upload.vo.SampleVO;
import org.biosino.upload.vo.exp.ExpTypeVO;
import org.biosino.upload.vo.exp.SubExpSampleVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.biosino.common.core.constant.CacheConstants.*;
import static org.biosino.common.mongo.entity.ExpSampleType.*;
import static org.biosino.upload.service.ExperimentService.initAttr;
import static org.biosino.upload.tool.excelparseutil.imp.ExcelParser.RELATED_LINKS_SEPARATOR;

@Service
@RequiredArgsConstructor
@Slf4j
public class SampleService extends BaseService {
    // private static final Pattern TAX_ID_PATTERN = Pattern.compile("\\s\\[(\\d+)\\]");
    private static final Pattern TAX_ID_PATTERN = Pattern.compile("\\[(\\d+)\\]");
    private static final String TAXONOMY_DUPLICATED = "Taxonomy name is duplicated";

    private final RunRepository runRepository;
    private final DataRepository dataRepository;
    private final SampleRepository sampleRepository;
    private final PublishRepository publishRepository;
    private final RemoteDictService remoteDictService;
    //    private final RemoteUpdateIndexService remoteUpdateIndexService;
    private final ExpSampleTypeRepository expSampleTypeRepository;
    private final DictService dictService;
    private final DmsApi dmsApi;

    @Value("${node.api.dms-api:https://idc.biosino.org/dms}")
    public String dmsApiUrl;

    public SampleVO getSapInfoByNo(String sapNo) {
        if (sapNo == null) {
            throw new ServiceException("Sample ID cannot be empty");
        }
        Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("Not found data"));
        if (!StrUtil.equals(sample.getCreator(), SecurityUtils.getMemberId())) {
            throw new ServiceException("No Permission!");
        }

        SampleVO result = new SampleVO();
        if (sample.getAudited().equals(AuditEnum.audited.name()) && sample.getTempData() == null) {
            SampleDTOMapper.INSTANCE.copyToVo(sample, result);
            result.setOrganism(initFullOrganism(sample));

            result.setCustomAttributes(getSampleCustomAttributes(sample));
            result.setAttributes(sample.getAttributes());
            List<PublishVO> publishVO = getPublishVO(AuthorizeType.sample, sapNo);

            result.setPublish(publishVO);
        } else {
            SampleDTOMapper.INSTANCE.copyToVo(sample.getTempData(), result);
            result.setOrganism(initFullOrganism(sample.getTempData()));

            result.setCustomAttributes(getSampleCustomAttributes(sample.getTempData()));
            result.setAttributes(sample.getTempData().getAttributes());
            List<PublishVO> publishVo = getTempPublishVO(AuthorizeType.sample, sapNo);
            result.setPublish(publishVo);
        }

        return result;
    }

    private String initFullOrganism(final Sample sample) {
        String organism = sample.getOrganism();
        String taxId = sample.getTaxId();
        if (organism != null && taxId != null) {
            return StrUtil.format("{} [{} {}]", organism, TaxonomyUtil.TAX_ID_PRE, taxId);
        } else {
            return null;
        }
    }

    public List<Map<String, String>> getSampleCustomAttributes(Sample sample) {
        List<Map<String, String>> result = new ArrayList<>();

        Map<String, String> customAttr = sample.getCustomAttr();
        Map<String, String> customAttrDesc = sample.getCustomAttrDesc();

        if (customAttr != null && !customAttr.isEmpty()) {
            for (Map.Entry<String, String> entry : customAttr.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                Map<String, String> map = new HashMap<>();
                map.put("attr", key);
                map.put("value", value);
                if (customAttrDesc != null && customAttrDesc.containsKey(key)) {
                    map.put("attrDesc", customAttrDesc.get(key));
                }
                result.add(map);
            }
        }
        sample.setCustomAttr(customAttr);
        return result;
    }

    private void checkNameExist(String memberId, String sapNo, String name) {
        // 校验名称是否存在
        Sample existData = sampleRepository.validateSampleName(memberId, sapNo, name);
        if (existData != null && existData.getTempData() != null) {
            throw new ServiceException("The sample name already exists in submission: " + existData.getTempData().getSubNo());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public SampleVO save(SampleDTO dto) {
        String subNo = dto.getSubNo();

        Submission submission = getEditSubmissionByNo(subNo);
        final String sapNo = submission.getSapSingleNo();

        // 校验名称是否存在
        checkNameExist(SecurityUtils.getMemberId(), sapNo, dto.getName());

        // 根据Organism填充 taxId
        if (StrUtil.isNotBlank(dto.getOrganism())) {
            final TaxonomyEsDTO taxonomyNode = findTaxId(dto.getOrganism());
            if (taxonomyNode == null || taxonomyNode.getTaxId() == null) {
                throw new ServiceException(StrUtil.upperFirst(ORGANISM) + " is not a valid organization value");
            }
            dto.setTaxId(taxonomyNode.getTaxId());
            dto.setOrganism(taxonomyNode.getScientificName());
        }

        String subjectType = dto.getSubjectType();
        final Map<String, String> attributesMap = dto.getAttributes();

        ExpSampleType expSampleType = expSampleTypeRepository.findByName(subjectType);
        List<Attributes> attributesList = expSampleType.getAttributes();
        // 校验属性中的值
        for (Attributes attributes : attributesList) {
            String attField = attributes.getAttributesField();
            if (!attributesMap.containsKey(attField)) {
                continue;
            }
            String dataSource = attributes.getDataSource();
            String attrVal = attributesMap.get(attField);
            // 校验Biome
            if (HOST_BIOME.equals(dataSource) || NON_HOST_BIOME.equals(dataSource) || ENV_BIOME.equals(dataSource) || ENV_BIOME_WATER.equals(dataSource)) {
                Set<String> valSet;
                if (attributes.isAllowCreate() && attrVal.contains(DELIMITER)) {
                    String[] split = attrVal.split(DELIMITER);
                    valSet = CollUtil.newHashSet(split);
                } else {
                    valSet = CollUtil.newHashSet(attrVal);
                }

                for (String val : valSet) {
                    boolean existBiome = remoteDictService.existBiome(dataSource, StrUtil.trim(val), SecurityConstants.INNER).getData();
                    if (!existBiome) {
                        throw new ServiceException(attributes.getAttributesName() + " isn't a valid value!");
                    }
                }
            }

            // 校验disease
            if (DISEASE.equals(dataSource) && StrUtil.isNotBlank(attributesMap.get(DISEASE))) {
                if (Boolean.FALSE.equals(remoteDictService.existDisease(attributesMap.get(DISEASE), SecurityConstants.INNER).getData())) {
                    throw new ServiceException(attributes.getAttributesName() + " isn't a valid disease value!");
                }
            }
        }

        final Sample sample = dtoToDb(dto, sapNo, submission, SecurityUtils.getMemberId());

        // 处理自定义属性和自定义属性描述
        setCustomAttr(sample, dto.getCustomAttributes());

        // 保存文献
        savePublish(dto.getPublish(), AuthorizeType.sample, sample.getSapNo());

        if (sapNo == null) {
            // 新增样本
            submission.setSapSingleNo(sample.getSapNo());
            saveEditSubmission(submission);
        }

        sampleRepository.save(sample);
        // 将生成的id更新到tempData字段
        sampleRepository.updateTempId(CollUtil.toList(sample.getSapNo()));

        return getSapInfoByNo(sample.getSapNo());
    }

    private TaxonomyEsDTO findTaxId(String organism) {
        organism = StrUtil.trimToNull(organism);
        if (organism == null) {
            return null;
        }
        TaxonomyEsDTO taxonomyNode = TaxonomyUtil.parseTaxInfo(organism);
        if (taxonomyNode != null) {
            final String taxId = taxonomyNode.getTaxId();
            final List<String> taxIds = dmsApi.findTaxId(taxonomyNode.getScientificName(), remoteDictService);
            if (taxId != null) {
                if (taxIds.contains(taxId)) {
                    taxonomyNode.setTaxId(taxId);
                } else {
                    // throw new ServiceException("Taxonomy not exist");
                    taxonomyNode = null;
                }
            } else {
                final int idCount = CollUtil.size(taxIds);
                if (idCount > 1) {
                    throw new ServiceException(TAXONOMY_DUPLICATED);
                } else if (idCount == 1) {
                    taxonomyNode.setTaxId(taxIds.get(0));
                } else {
                    taxonomyNode = null;
                }
            }
        }
        return taxonomyNode;
    }

    private void setCustomAttr(final Sample sample, final List<Map<String, String>> customAttributes) {
        if (customAttributes != null) {
            final Map<String, String> customAttrMap = new LinkedHashMap<>();
            final Map<String, String> customAttrDescMap = new LinkedHashMap<>();
            for (Map<String, String> item : customAttributes) {
                String attr = item.get("attr");
                String attrDesc = item.get("attrDesc");
                String value = item.get("value");
                // 排除空属性
                if (StrUtil.isNotBlank(attr) && StrUtil.isNotBlank(value)) {
                    customAttrMap.put(attr, value);
                    if (StrUtil.isNotBlank(attrDesc)) {
                        customAttrDescMap.put(attr, attrDesc);
                    }
                }
            }
            if (!customAttrMap.isEmpty()) {
                sample.getTempData().setCustomAttr(customAttrMap);
            } else {
                sample.getTempData().setCustomAttr(null);
            }
            if (!customAttrDescMap.isEmpty()) {
                sample.getTempData().setCustomAttrDesc(customAttrDescMap);
            } else {
                sample.getTempData().setCustomAttrDesc(null);
            }
        }
    }

    private Sample dtoToDb(SampleDTO dto, String sapNo, Submission submission, String memberId) {
        return dtoToDb(dto, sapNo, submission, memberId, null);
    }

    private Sample dtoToDb(SampleDTO dto, String sapNo, Submission submission, String memberId, Sample savedSap) {
        final Date now = new Date();
        Sample dbSap;
        // 新增实验
        if (sapNo == null) {
            dbSap = initExp(dto, now, memberId, submission.getSubmitter());
            // 保存暂存数据
            dbSap.setTempData(SampleDTOMapper.INSTANCE.copy(dbSap));
        } else {
            // 编辑实验
            if (savedSap == null) {
                dbSap = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("Not found data"));
            } else {
                dbSap = savedSap;
            }
            dto.setSapNo(dbSap.getSapNo());
            // 保存暂存数据
            Sample tempData = dbSap.getTempData();
            if (tempData == null) {
                tempData = SampleDTOMapper.INSTANCE.copy(dbSap);
            }
            SampleDTOMapper.INSTANCE.copyToDb(dto, tempData);
            if (AuditEnum.audited.name().equals(tempData.getAudited())) {
                tempData.setAudited(AuditEnum.unaudited.name());
            }

            tempData.setUpdateDate(now);
            tempData.setRelatedLinks(NodeUtils.cleanRelatedLinks(tempData.getRelatedLinks()));

            // single保存：更新外层的Experiment数据
            if (AuditEnum.init.name().equals(tempData.getAudited())) {
                dbSap = SampleDTOMapper.INSTANCE.copy(tempData);
            }
            dbSap.setTempData(tempData);
        }
        deleteEmptyAttr(dbSap);
        deleteEmptyAttr(dbSap.getTempData());
        return dbSap;
    }

    /**
     * 删除空属性
     *
     * @param item
     */
    private void deleteEmptyAttr(final Sample item) {
        if (item != null) {
            item.setAttributes(deleteEmptyMap(item.getAttributes()));
            item.setCustomAttr(deleteEmptyMap(item.getCustomAttr()));
//            item.setBackup(deleteEmptyMap(item.getBackup()));
        }
    }

    private Sample initExp(SampleDTO dto, Date now, String memberId, final Submitter submitter) {
        final Sample sample = new Sample();
        SampleDTOMapper.INSTANCE.copyToDb(dto, sample);
        // 手动指定id效率太低
//        exp.setId(IdUtil.objectId());
        // 临时编号
        sample.setSapNo(IdUtil.fastSimpleUUID());
        sample.setCreateDate(now);
        sample.setUpdateDate(now);
        sample.setCreator(memberId);
        sample.setOwnership(OwnershipEnum.self_support.getDesc());
        sample.setSubmitter(submitter);
        sample.setHitNum(0L);
        sample.setAudited(AuditEnum.init.name());
        sample.setVisibleStatus(VisibleStatusEnum.Unaccessible.name());
        sample.setRelatedLinks(NodeUtils.cleanRelatedLinks(sample.getRelatedLinks()));

        return sample;
    }

    private List<String> sapMultipleNos(final Submission submission, final String type) {
        List<Submission.SampleGroup> data = submission.getSapMultipleData();
        List<String> list = null;
        if (data != null) {
            for (Submission.SampleGroup item : data) {
                if (item != null && type.equals(item.getType())) {
                    list = item.getNos();
                    break;
                }
            }
        }
        return CollUtil.isEmpty(list) ? new ArrayList<>() : list;
    }

    public String validateSampleName(String sapNo, String name) {
        if (StringUtil.isBlank(name)) {
            return "The sample name cannot be blank";
        }
        // 校验名称是否存在
        checkNameExist(SecurityUtils.getMemberId(), sapNo, name);
        return null;
    }

    public List<SelectOption> getSampleOptions() {
        List<Sample> list = sampleRepository.findAllByCreator(SecurityUtils.getMemberId());
        return list.stream().map(x -> {
            SelectOption option = new SelectOption();
            option.setValue(x.getSapNo());
            if (x.getSapNo().startsWith(SequenceType.SAMPLE.getPrefix())) {
                option.setLabel(x.getSapNo() + " (" + x.getName() + ")");
            } else {
                option.setLabel(x.getName());
            }
            return option;
        }).collect(Collectors.toList());
    }

    public Page<SelectOption> getSampleOptionsByPage(ArchivedSelectQueryDTO queryDTO) {
        queryDTO.setCreator(SecurityUtils.getMemberId());
        Page<Sample> page = sampleRepository.findAllByPage(queryDTO);

        return page.map(x -> {
            SelectOption option = new SelectOption();
            option.setValue(x.getSapNo());
            if (x.getSapNo().startsWith(SequenceType.SAMPLE.getPrefix())) {
                option.setLabel(x.getSapNo() + " (" + x.getName() + ")");
            } else {
                option.setLabel(x.getName());
            }
            return option;
        });
    }

    public ExpTypeVO getSampleTypeData(final String sampleType, final String subNo) {
        // 属性数据
        final List<ExpSampleType.Attributes> attributes = new ArrayList<>();
        ExpSampleType expTypeData = dictService.getExpSapDictData(sampleType);

        attributes.add(initAttr(BaseAttrType.optional, SAMPLE_ID, SAMPLE_ID, ExpSampleDataType.Input, "For newly created samples, the sample ID can be left blank. When modifying a sample that has already been assigned a sample ID, the sample ID must be filled in. Once the sample ID is assigned, it cannot be changed."));
        attributes.add(initAttr(BaseAttrType.required, SAMPLE_NAME, SAMPLE_NAME, ExpSampleDataType.Input, "Fill in the unique name of the sample you plan to create or the name of a sample that has been successfully created and assigned a sample ID. If the sample ID and sample name do not match, it is considered an update to the sample name."));
        if (!BaseAttrType.none.name().equals(expTypeData.getOrganism())) {
            attributes.add(initAttr(BaseAttrType.valueOf(expTypeData.getOrganism()), ORGANISM, ORGANISM, ExpSampleDataType.Input, "The most descriptive scientific name for the sample (to the species, if possible). You can refer to the NCBI taxonomy database (https://www.ncbi.nlm.nih.gov/). In the case of a new species, <NAME_EMAIL>. The corresponding organism of human is Homo sapiens."));
        }
        if (!BaseAttrType.none.name().equals(expTypeData.getTissue())) {
            attributes.add(initAttr(BaseAttrType.valueOf(expTypeData.getTissue()), TISSUE, TISSUE, ExpSampleDataType.Input, "Type of tissue the sample was taken from (http://bioportal.bioontology.org/ontologies/1005). Common tissue types see sheet \"tissue_type\" in the upload template file."));
        }
        if (!BaseAttrType.none.name().equals(expTypeData.getDesc())) {
            attributes.add(initAttr(BaseAttrType.valueOf(expTypeData.getDesc()), DESCRIPTION, DESCRIPTION, ExpSampleDataType.Input, "Description of the sample."));
        }
        if (!BaseAttrType.none.name().equals(expTypeData.getProtocol())) {
            attributes.add(initAttr(BaseAttrType.valueOf(expTypeData.getProtocol()), PROTOCOL, PROTOCOL, ExpSampleDataType.Input, "Description of sample processing protocol."));
        }
        attributes.add(initAttr(BaseAttrType.optional, RELATED_LINKS, RELATED_LINKS, ExpSampleDataType.Input));

        attributes.addAll(expTypeData.getAttributes());

        for (ExpSampleType.Attributes attribute : attributes) {
            if (attribute.getDescription() == null && attribute.getDataType().startsWith("Number")) {
                attribute.setDescription("Number");
            }
        }

        List<Map<String, Object>> rows = null;
        List<String> savedTypes = null;
        final Map<String, String> customAttrMap = new LinkedHashMap<>();
        // 获取表格数据
        if (StrUtil.isNotBlank(subNo)) {
            // 查询回显信息
            final Submission submission = getEditSubmissionByNo(subNo);
            final List<Submission.SampleGroup> data = submission.getSapMultipleData();
            if (CollUtil.isNotEmpty(data)) {
                final String memberId = SecurityUtils.getMemberId();
                savedTypes = new ArrayList<>();
                for (Submission.SampleGroup item : data) {
                    final String type = item.getType();
                    // 已入库类型，用于高亮显示
                    savedTypes.add(type);
                    if (sampleType.equals(type)) {
                        // 表格回显数据
                        final List<String> nos = item.getNos();
                        if (CollUtil.isNotEmpty(nos)) {
                            rows = initSampleTableData(sampleRepository.findAllTempByNosAndCreatorAndType(nos, memberId, sampleType), false, customAttrMap);
                        }
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(customAttrMap)) {
            // 添加自定义属性回显
            for (Map.Entry<String, String> entry : customAttrMap.entrySet()) {
                final String custAttr = entry.getKey();
                attributes.add(initAttr(BaseAttrType.optional, custAttr, custAttr, ExpSampleDataType.Input, StrUtil.trimToNull(entry.getValue()), true));
            }
        }

        ExpTypeVO vo = new ExpTypeVO();
        vo.setAttributes(attributes);
        vo.setRows(rows);
        vo.setSavedTypes(savedTypes);
        return vo;
    }


    public List<SubExpSampleVO> getSampleDataList(List<String> nos, String creator) {
        if (CollUtil.isEmpty(nos)) {
            return null;
        }

        List<Sample> sampleList = sampleRepository.findAllTempByNosAndCreatorAndType(nos, creator, null);

        if (CollUtil.isEmpty(sampleList)) {
            return null;
        }

        Map<String, List<Sample>> sapmleMap = sampleList.stream()
                .sorted(Comparator.comparing(Sample::getCreateDate).reversed())
                .collect(Collectors.groupingBy(Sample::getSubjectType));

        List<SubExpSampleVO> result = new ArrayList<>();

        sapmleMap.forEach((sapType, value) -> {
            SubExpSampleVO vo = new SubExpSampleVO();
            vo.setType(sapType);

            Set<String> titles = new LinkedHashSet<>();

            ExpSampleType expTypeData = dictService.getExpSapDictData(sapType);

            // 基础公共字段标题
            titles.add(SAMPLE_ID);
            titles.add(SAMPLE_NAME);
            if (!BaseAttrType.none.name().equals(expTypeData.getOrganism())) {
                titles.add(ORGANISM);
            }
            if (!BaseAttrType.none.name().equals(expTypeData.getTissue())) {
                titles.add(TISSUE);
            }
            titles.add(DESCRIPTION);
            titles.add(RELATED_LINKS);
            titles.add(PROTOCOL);

            List<String> attrFieldList = expTypeData.getAttributes().stream()
                    .sorted(Comparator.comparing(ExpSampleType.Attributes::getGroup).thenComparing(Attributes::getSort))
                    .map(ExpSampleType.Attributes::getAttributesField).collect(Collectors.toList());

            titles.addAll(attrFieldList);

            List<Map<String, Object>> rows = new ArrayList<>();

            // 表格回显数据
            if (CollUtil.isNotEmpty(nos)) {
                rows = initSampleTableData(sampleRepository.findAllTempByNosAndCreatorAndType(nos, creator, sapType), true);
            }

            // 将自定义属性的key抽取出来
            if (CollUtil.isNotEmpty(rows)) {
                for (Map<String, Object> rowMap : rows) {
                    titles.addAll(rowMap.keySet());
                }
            }

            vo.setTitle(new ArrayList<>(titles));

            vo.setRows(rows);

            result.add(vo);
        });


        return result;
    }

    public List<String> getExistSampleOrganism(String sampleType) {
        return sampleRepository.findSampleOrganisms(sampleType, SecurityUtils.getMemberId());
    }

    public List<Select> getExistSampleAttr(String sampleType) {
        List<Select> selects = new ArrayList<>();

        // 构造用户的自定义属性
        Set<String> customAttr = new HashSet<>();
        List<Sample> sampleCustomAttr = sampleRepository.findSampleCustomAttr(sampleType, SecurityUtils.getMemberId());
        if (CollUtil.isNotEmpty(sampleCustomAttr)) {
            for (Sample sample : sampleCustomAttr) {
                Map<String, String> customAttrMap = sample.getCustomAttr();
                if (CollUtil.isEmpty(customAttrMap)) {
                    continue;
                }
                Set<String> keySet = customAttrMap.keySet();
                if (CollUtil.isNotEmpty(keySet)) {
                    customAttr.addAll(keySet);
                }
            }
        }
        if (CollUtil.isNotEmpty(customAttr)) {
            for (String attr : customAttr) {
                Select select = Select.builder().label(attr).value("custom_" + attr).build();
                selects.add(select);
            }
        }

        // 构造该模板中系统自带的属性
        ExpSampleType expSampleType = expSampleTypeRepository.findByName(sampleType);

        if (expSampleType == null) {
            throw new ServiceException("Experiment dictionary data not found exp data");
        }

        List<ExpSampleType.Attributes> attributes = expSampleType.getAttributes();
        for (ExpSampleType.Attributes attribute : attributes) {
            Select select = Select.builder().label(attribute.getAttributesName()).value("system_" + attribute.getAttributesField()).build();
            selects.add(select);
        }
        return selects;
    }

    public Page<SampleVO> getExistSampleData(ExistSampleQueryDTO dto) {
        dto.setCreator(SecurityUtils.getMemberId());
        PageImpl<Sample> page = sampleRepository.findSample(dto);
        return page.map(sample -> {
            sample.setAttributes(null);
            sample.setCustomAttr(null);
            sample.setRelatedLinks(null);
            sample.setProtocol(null);
            SampleVO sampleVO = new SampleVO();
            SampleDTOMapper.INSTANCE.copyToVo(sample, sampleVO);
            return sampleVO;
        });
    }

    /**
     * 批量保存样本
     */
    public List<ErrorMsgVO> batchSave(final SampleBatchDTO dto) {
        if (dto == null) {
            throw new ServiceException("Parameter error.");
        }
        final String memberId = SecurityUtils.getMemberId();
        // 提交信息
        final String subNo = dto.getSubNo();
        final Submission submission = getEditSubmissionByNo(subNo);
        final AuthorizeType stageEnum = AuthorizeType.findByName(dto.getStage()).orElseThrow(() -> new ServiceException("The stage type is wrong. Please Check."));
        if (!stageEnum.equals(AuthorizeType.sample)) {
            throw new ServiceException("The stage type is wrong. Please Check.");
        }
        // 删除空行
        dto.delBlank();
        // 标题信息，表格数据
        final List<String> titles = dto.getTitles();
        final List<Object[]> datas = dto.getDatas();
        final Map<String, String> allAttrDesMap = dto.initDescriptionMap();
        final int totalCol = titles.size();
        final int totalRow = datas.size();
        if (totalCol == 0 || totalRow == 0) {
            throw new ServiceException("The submitted data cannot be empty");
        }

        // 名称和id
        final String nameColKey = SAMPLE_NAME;
        final String idColKey = SAMPLE_ID;
        // 名称列索引
        final int nameColIndex = requiredCol(titles, nameColKey);
        // 新增的名称集合
        final Set<String> names = new HashSet<>();
        // 编号列索引
        final int idColIndex = requiredCol(titles, idColKey);
        final Set<String> ids = new HashSet<>();

        // 获取属性字典表配置
        final String sampleType = dto.getSampleType();
        ExpSampleType expSampleType = expSampleTypeRepository.findByName(sampleType);

        if (expSampleType == null) {
            throw new ServiceException("Experiment dictionary data not found exp data");
        }
        // 其它必需列校验
        Integer organismColIndex = null;
        if (!BaseAttrType.none.name().equals(expSampleType.getOrganism())) {
            organismColIndex = requiredCol(titles, ORGANISM);
        }
        Integer tissueColIndex = null;
        if (!BaseAttrType.none.name().equals(expSampleType.getTissue())) {
            tissueColIndex = requiredCol(titles, TISSUE);
        }
        Integer descColIndex = null;
        if (!BaseAttrType.none.name().equals(expSampleType.getDesc())) {
            descColIndex = requiredCol(titles, DESCRIPTION);
        }
        Integer protocolColIndex = null;
        if (!BaseAttrType.none.name().equals(expSampleType.getProtocol())) {
            protocolColIndex = requiredCol(titles, PROTOCOL);
        }

        final int reLinkColIndex = requiredCol(titles, RELATED_LINKS);

        final Map<String, ExpSampleType.Attributes> rangeMap = ExpSampleType.handelExpAttrMap(expSampleType);
        final HashSet<String> attrNotExist = new HashSet<>(rangeMap.keySet());
        titles.forEach(attrNotExist::remove);
        if (CollUtil.isNotEmpty(attrNotExist)) {
            throw new ServiceException(StrUtil.format("Attributes not found: {}", CollUtil.join(attrNotExist, ", ")));
        }

        // 字典中配置的推荐属性必填个数
        Integer recommendNum = expSampleType.getRecommendNum();
        if (recommendNum == null) {
            recommendNum = 0;
        }

        Set<String> insertNameSet = new LinkedHashSet<>();

        // 校验表格数据
        final List<ErrorMsgVO> errors = new LinkedList<>();
        final Map<String, TaxonomyEsDTO> organismTaxIdMap = new HashMap<>();
        // tax名称是否重复
        final Map<String, Boolean> taxIdDuplicatedMap = new HashMap<>();
        for (int i = 0; i < totalRow; i++) {
            final Object[] row = datas.get(i);
            final int colCount = row.length;
            if (colCount != totalCol) {
                throw new ServiceException("There are values without attributes!");
            }

            // 实验(样本)名称校验
            final String name = strVal(row[nameColIndex]);
            if (name == null) {
                // 必填
                emptyErr(i, errors, nameColKey);
            }

            // id校验
            final String id = strVal(row[idColIndex]);
            if (id != null) {
                // id唯一
                duplicateErr(i, errors, ids, idColKey, id);
            } else {
                // 新的数据 要求name唯一
                if (name != null) {
                    duplicateErr(i, errors, names, nameColKey, name);
                }
            }

            if (StrUtil.isBlank(id) && StrUtil.isNotBlank(name)) {
                insertNameSet.add(name);
            }

            // 属性校验
            int recommendCount = 0;

            // organism校验
            if (organismColIndex != null) {
                final String organism = strVal(row[organismColIndex]);
                if (StrUtil.isBlank(organism)) {
                    if (BaseAttrType.required.name().equals(expSampleType.getOrganism())) {
                        // 必填
                        emptyErr(i, errors, ORGANISM);
                    }
                } else {
                    if (BaseAttrType.recommend.name().equals(expSampleType.getOrganism())) {
                        recommendCount++;
                    }

                    TaxonomyEsDTO taxonomyNode = null;
                    if (organismTaxIdMap.containsKey(organism)) {
                        taxonomyNode = organismTaxIdMap.get(organism);
                    } else {
                        try {
                            taxonomyNode = findTaxId(organism);
                            taxIdDuplicatedMap.put(organism, false);
                        } catch (ServiceException e) {
                            taxIdDuplicatedMap.put(organism, TAXONOMY_DUPLICATED.equals(e.getMessage()));
                        }
                    }

                    if (taxonomyNode == null) {
                        organismTaxIdMap.put(organism, null);

                        if (Boolean.TRUE.equals(taxIdDuplicatedMap.get(organism))) {
                            errors.add(errMsg(i, ORGANISM, organism,
                                    StrUtil.format("{} corresponds to multiple Taxonomy IDs. Please refer to the format \"{} value [Taxonomy ID: xxx]\" to supplement the Taxonomy ID information, such as: root [Taxonomy ID: 1]", ORGANISM, ORGANISM)
                            ));
                        } else {
                            errors.add(errMsg(i, ORGANISM, organism, StrUtil.format("This is not a valid {} value! Please see the sample attributes page.", ORGANISM)));
                        }
                    } else {
                        organismTaxIdMap.put(organism, taxonomyNode);
                    }
                }
            }

            // tissue校验
            if (tissueColIndex != null) {
                final String tissue = strVal(row[tissueColIndex]);
                if (tissue == null) {
                    if (BaseAttrType.required.name().equals(expSampleType.getTissue())) {
                        // 必填
                        emptyErr(i, errors, TISSUE);
                    }
                } else if (BaseAttrType.recommend.name().equals(expSampleType.getTissue())) {
                    recommendCount++;
                }
            }

            // description字段校验
            if (descColIndex != null) {
                final String desc = strVal(row[descColIndex]);
                if (desc == null) {
                    if (BaseAttrType.required.name().equals(expSampleType.getDesc())) {
                        // 必填
                        emptyErr(i, errors, DESCRIPTION);
                    }
                } else if (BaseAttrType.recommend.name().equals(expSampleType.getDesc())) {
                    recommendCount++;
                }
            }

            // protocol字段校验
            if (protocolColIndex != null) {
                final String protocol = strVal(row[protocolColIndex]);
                if (protocol == null) {
                    if (BaseAttrType.required.name().equals(expSampleType.getProtocol())) {
                        // 必填
                        emptyErr(i, errors, PROTOCOL);
                    }
                } else if (BaseAttrType.recommend.name().equals(expSampleType.getProtocol())) {
                    recommendCount++;
                }
            }

            for (Map.Entry<String, ExpSampleType.Attributes> entry : rangeMap.entrySet()) {
                final String key = entry.getKey();
                final int attrIndex = titles.indexOf(key);
                final String attrVal = strVal(row[attrIndex]);
                ExpSampleType.Attributes attributes = rangeMap.get(key);
                if (attributes != null) {
                    final String required = attributes.getRequired();
                    final boolean isRequired = BaseAttrType.required.name().equals(required);
                    if (attrVal == null) {
                        if (isRequired) {
                            // 必填项
                            emptyErr(i, errors, key);
                        }
                    } else if (BaseAttrType.recommend.name().equals(required)) {
                        // 统计值不为空的推荐项数量
                        recommendCount++;
                    }

                    if (attrVal != null) {
                        // 校验Biome类型的数据
                        final String dataSource = attributes.getDataSource();
                        if (HOST_BIOME.equals(dataSource) || NON_HOST_BIOME.equals(dataSource) || ENV_BIOME.equals(dataSource) || ENV_BIOME_WATER.equals(dataSource)) {
                            Set<String> valSet;
                            if (attributes.isAllowCreate() && attrVal.contains(DELIMITER)) {
                                String[] split = attrVal.split(DELIMITER);
                                valSet = CollUtil.newHashSet(split);
                            } else {
                                valSet = CollUtil.newHashSet(attrVal);
                            }

                            for (String val : valSet) {
                                boolean existBiome = remoteDictService.existBiome(dataSource, StrUtil.trim(val), SecurityConstants.INNER).getData();
                                if (!existBiome) {
                                    errors.add(errMsg(i, key, val, "isn't a valid value! Please see the sample attributes page."));
                                }
                            }
                        } else if (DISEASE.equals(dataSource)) {
                            // Human类型的disease字段校验
                            if (Boolean.FALSE.equals(remoteDictService.existDisease(attrVal, SecurityConstants.INNER).getData())) {
                                errors.add(errMsg(i, key, attrVal, "isn't a valid disease value! Please see the sample attributes page."));
                            }
                        } else if (TAXONOMY.equals(dataSource)) {
                            // Taxonomy类型数据校验
                            TaxonomyEsDTO taxonomyNode = null;
                            if (organismTaxIdMap.containsKey(attrVal)) {
                                taxonomyNode = organismTaxIdMap.get(attrVal);
                            } else {
                                try {
                                    taxonomyNode = findTaxId(attrVal);
                                    taxIdDuplicatedMap.put(attrVal, false);
                                } catch (ServiceException e) {
                                    taxIdDuplicatedMap.put(attrVal, TAXONOMY_DUPLICATED.equals(e.getMessage()));
                                }
                            }

                            if (taxonomyNode == null) {
                                organismTaxIdMap.put(attrVal, null);

                                if (Boolean.TRUE.equals(taxIdDuplicatedMap.get(attrVal))) {
                                    errors.add(errMsg(i, key, attrVal,
                                            StrUtil.format("{} corresponds to multiple Taxonomy IDs. Please refer to the format \"{} value [Taxonomy ID: xxx]\" to supplement the Taxonomy ID information, such as: root [Taxonomy ID: 1]", key, key)
                                    ));
                                } else {
                                    errors.add(errMsg(i, key, attrVal, StrUtil.format("This is not a valid {} value! Please see the sample attributes page.", ORGANISM)));
                                }

                            } else {
                                organismTaxIdMap.put(attrVal, taxonomyNode);
                            }

                        } else {
                            // 下拉框校验
                            final List<String> rangeStrList = attributes.getRangeStrList();
                            if (!attributes.isAllowCreate() && CollUtil.isNotEmpty(rangeStrList)) {
                                selectionErr(i, errors, rangeStrList, key, attrVal);
                            }
                            // 日期校验
                            if (ExpSampleDataType.Date.name().equals(attributes.getDataType())) {
                                final String date = dateErr(i, errors, key, attrVal);
                                if (date != null) {
                                    row[attrIndex] = date;
                                }
                            }
                            // 正则校验
                            if (StrUtil.isNotBlank(attributes.getValueRegex())) {
                                if (!ReUtil.isMatch(attributes.getValueRegex(), attrVal)) {
                                    errors.add(errMsg(i, key, attrVal, "Data format error, please refer to: " + attributes.getValueFormat()));
                                }
                            }
                        }
                    }
                }
            }
            if (recommendCount < recommendNum) {
                // 推荐字段没有满足最小必填个数
                errors.add(errMsg(i, "Too few recommended fields", String.valueOf(recommendCount),
                        StrUtil.format("The number of recommended fields filled in is less than {}", recommendNum)));
            }
        }
        if (CollUtil.isNotEmpty(errors)) {
            // 表格校验错误
            return errors;
        } else {
            // 查库校验
            final List<String> sapMultipleNos = sapMultipleNos(submission, sampleType);
            // 所有已入库的名称
            final Map<String, String> dbNames = sampleRepository.findTempNameByNamesInAndNoNotIn(names, memberId, sapMultipleNos);
            final List<Sample> savedSap = sampleRepository.findAllByNosAndCreator(ids, memberId);
            // 所有已入库的数据
            final Map<String, Sample> dbSapMap = new HashMap<>();
            for (Sample item : savedSap) {
                dbSapMap.put(item.getSapNo(), item);
                final List<String> usedIds = item.getUsedIds();
                if (CollUtil.isNotEmpty(usedIds)) {
                    for (String usedId : usedIds) {
                        if (usedId != null) {
                            dbSapMap.put(usedId, item);
                        }
                    }
                }
            }
            // 用于判断编辑的样本是否已被其他submission使用
            final Set<String> allSapNosOfSubmission = sampleRepository.findAllTempNoByNos(sapMultipleNos, memberId);
            for (int i = 0; i < totalRow; i++) {
                final Object[] row = datas.get(i);
                final String id = strVal(row[idColIndex]);
                final String name = strVal(row[nameColIndex]);
                if (id != null) {
                    // 编辑
                    if (!dbSapMap.containsKey(id)) {
                        errors.add(errMsg(i, idColKey, id, "id does not exist or not allow edit"));
                    } else {
                        // 要求只能编辑审核通过的实验
                        final Sample sample = dbSapMap.get(id);
                        if (!AuditEnum.audited.name().equals(sample.getAudited())
                                && !allSapNosOfSubmission.contains(id)) {
                            // 存在正式编号的样本已被编辑，且待审核
                            errors.add(errMsg(i, idColKey, id, "The data ID has already been used in submission: " + sample.getSubNo()));
                        }
                    }
                } else if (dbNames.containsKey(name)) {
                    // 新增, 名称重复
                    errors.add(errMsg(i, nameColKey, name, "name exist in submission: " + dbNames.get(name)));
                }
            }

            // 校验将要被删除的样本是否被引用
            List<String> multipleNos = null;

            // 找出哪些新增状态的组学将要被删除
            List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();

            if (CollUtil.isNotEmpty(sapMultipleData)) {

                for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {
                    if (sapMultipleDatum.getType().equals(sampleType)) {
                        multipleNos = sapMultipleDatum.getNos();
                    }
                }
            }

            if (CollUtil.isNotEmpty(multipleNos)) {
                List<String> insertNameList = new ArrayList<>();
                for (String multipleNo : multipleNos) {
                    // 如果是非正式ID
                    if (!multipleNo.startsWith(SequenceType.SAMPLE.getPrefix())) {
                        insertNameList.add(multipleNo);
                    }
                }
                if (CollUtil.isNotEmpty(insertNameList)) {
                    Map<String, String> nameMap = sampleRepository.findNameByIds(insertNameList);

                    if (CollUtil.isNotEmpty(nameMap)) {

                        Set<String> keySet = nameMap.keySet();

                        for (String name : keySet) {
                            // 删除新增的数据情况
                            if (!insertNameSet.contains(name)) {

                                String sapNo = nameMap.get(name);

                                // 校验当前组学在哪些Run中已被使用
                                List<Run> runs = runRepository.findAllNameByFieldAndNo("sap_no", sapNo);

                                if (CollUtil.isNotEmpty(runs)) {
                                    for (Run run : runs) {
                                        Run tempData = run.getTempData();
                                        String subNo2 = tempData.getSubNo().equals(dto.getSubNo()) ? tempData.getSubNo() + " (Current Submission)" : tempData.getSubNo();
                                        String runNo = AuditEnum.init.name().equals(tempData.getAudited()) ? "Unassigned formal ID" : tempData.getRunNo();

                                        String msg = "Delete failed, The current data is already in use in Submission NO: " + subNo2 + ", Run No: " + runNo + ", Run Name: " + tempData.getName();
                                        errors.add(errMsg(null, "sample_name", name, msg));
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (CollUtil.isNotEmpty(errors)) {
                // 返回校验失败信息
                return errors;
            }

            // 全部校验通过
            dbNames.clear();
            allSapNosOfSubmission.clear();

            // 所有已入库临时数据的名称和对象map
            final Map<String, Sample> nameItemOfTempData = sampleRepository.findTempNameByNamesInAndNoIn(names, memberId, sapMultipleNos);

            final List<Sample> toSaveData = new ArrayList<>();
            final List<Sample> toSaveDataWithId = new ArrayList<>();
            final Set<Integer> notCustomColSet = new HashSet<>();
            for (int i = 0; i < totalRow; i++) {
                final Object[] row = datas.get(i);

                final String name = strVal(row[nameColIndex]);
                addNotCustom(i, notCustomColSet, nameColIndex);

                final SampleDTO sourceDto = new SampleDTO();
                sourceDto.setSubNo(subNo);
                sourceDto.setName(name);

                if (descColIndex != null) {
                    sourceDto.setDescription(strVal(row[descColIndex]));
                    addNotCustom(i, notCustomColSet, descColIndex);
                }

                if (protocolColIndex != null) {
                    sourceDto.setProtocol(strVal(row[protocolColIndex]));
                    addNotCustom(i, notCustomColSet, protocolColIndex);
                }

                if (organismColIndex != null) {
                    final String organism = strVal(row[organismColIndex]);
                    addNotCustom(i, notCustomColSet, organismColIndex);

                    final TaxonomyEsDTO taxonomyNode = organismTaxIdMap.get(organism);
                    sourceDto.setTaxId(taxonomyNode.getTaxId());
                    sourceDto.setOrganism(taxonomyNode.getScientificName());
                }

                if (tissueColIndex != null) {
                    sourceDto.setTissue(strVal(row[tissueColIndex]));
                    addNotCustom(i, notCustomColSet, tissueColIndex);
                }


                final String reLink = strVal(row[reLinkColIndex]);
                addNotCustom(i, notCustomColSet, reLinkColIndex);

                if (reLink != null) {
                    sourceDto.setRelatedLinks(CollUtil.toList(reLink.split(RELATED_LINKS_SEPARATOR)));
                }
                final Map<String, String> attributes = new LinkedHashMap<>();
                // 读取属性数据
                for (Map.Entry<String, ExpSampleType.Attributes> entry : rangeMap.entrySet()) {
                    final String key = entry.getKey();
                    final int keyIndex = titles.indexOf(key);
                    final String attrVal = strVal(row[keyIndex]);
                    addNotCustom(i, notCustomColSet, keyIndex);

                    attributes.put(key, attrVal);
                }
                sourceDto.setAttributes(CollUtil.isEmpty(attributes) ? null : attributes);
                sourceDto.setSubjectType(sampleType);

                Sample sample = null;
                // 编号
                String id = strVal(row[idColIndex]);
                addNotCustom(i, notCustomColSet, idColIndex);

                if (id == null) {
                    // 新增的临时数据，根据名称获取已入库的id
                    sample = nameItemOfTempData.get(name);
                    if (sample != null) {
                        id = StrUtil.trimToNull(sample.getSapNo());
                    }
                }
                if (id == null) {
                    sample = dtoToDb(sourceDto, null, submission, memberId);
                    addCusAttr(sample, notCustomColSet, titles, row, allAttrDesMap);
                    toSaveData.add(sample);
                } else {
                    sample = dtoToDb(sourceDto, id, submission, memberId, dbSapMap.getOrDefault(id, sample));
                    addCusAttr(sample, notCustomColSet, titles, row, allAttrDesMap);
                    toSaveDataWithId.add(sample);
                }
            }

            dbSapMap.clear();
            // 编辑和新增分开保存，提高保存效率
            sampleRepository.saveAll(toSaveDataWithId);
            // 保存实验信息
            sampleRepository.saveAll(toSaveData);
            toSaveData.addAll(toSaveDataWithId);
            // 保存后生成的样本编号集合
            final List<String> dbNos = new ArrayList<>();
            for (Sample item : toSaveData) {
                dbNos.add(item.getSapNo());
            }
            // 更新submission
            // 删除临时实验数据
            sampleRepository.deleteTempByNosAndCreator(sapMultipleNos, memberId, dbNos);
            // 将生成的id更新到tempData字段
            sampleRepository.updateTempId(dbNos);
            // 保存Submission关联字段
            List<Submission.SampleGroup> sampleGroupList = submission.getSapMultipleData();
            if (CollUtil.isEmpty(sampleGroupList)) {
                sampleGroupList = new ArrayList<>();
                sampleGroupList.add(new Submission.SampleGroup(sampleType, dbNos));
            } else {
                boolean hasCurrType = false;
                for (Submission.SampleGroup item : sampleGroupList) {
                    if (item != null && sampleType.equals(item.getType())) {
                        hasCurrType = true;
                        item.setNos(dbNos);
                        break;
                    }
                }
                if (!hasCurrType) {
                    sampleGroupList.add(new Submission.SampleGroup(sampleType, dbNos));
                }
            }
            submission.setSapMultipleData(sampleGroupList);
            saveEditSubmission(submission);

            return null;
        }
    }

    /**
     * 批量导入时，处理自定义属性
     */
    private void addCusAttr(Sample sample, Set<Integer> notCustomColSet, List<String> titles, Object[] row, final Map<String, String> allAttrDesMap) {
        final int allColSize = CollUtil.size(titles);
        // 自定义属性、值
        final Map<String, String> customAttrMap = new LinkedHashMap<>();
        // 自定义属性、描述批注
        final Map<String, String> customAttrDescMap = new LinkedHashMap<>();

        if (allColSize > CollUtil.size(notCustomColSet)) {
            for (int i = 0; i < allColSize; i++) {
                // 取出自定义属性
                if (!notCustomColSet.contains(i)) {
                    final String key = titles.get(i);
                    final String attrVal = strVal(row[i]);
                    // 排除空属性
                    if (StrUtil.isNotBlank(key) && StrUtil.isNotBlank(attrVal)) {
                        customAttrMap.put(key, attrVal);
                        final String desc = allAttrDesMap.get(key);
                        if (desc != null) {
                            customAttrDescMap.put(key, desc);
                        }
                    }
                }
            }

        }

        if (!customAttrMap.isEmpty()) {
            sample.getTempData().setCustomAttr(customAttrMap);
        } else {
            sample.getTempData().setCustomAttr(null);
        }
        if (!customAttrDescMap.isEmpty()) {
            sample.getTempData().setCustomAttrDesc(customAttrDescMap);
        } else {
            sample.getTempData().setCustomAttrDesc(null);
        }
    }

    private void addNotCustom(final int i, final Set<Integer> notCustomColIndex, final int colIndex) {
        if (i == 0) {
            notCustomColIndex.add(colIndex);
        }
    }

    /**
     * 样本筛选后，根据勾选编号查询并返回表格数据
     */
    public List<Map<String, Object>> findSampleByNosAndType(final ExistSampleQueryDTO dto) {
        final List<String> nos = dto.getSampleNos();
        final String sampleType = dto.getSampleType();
        if (CollUtil.isEmpty(nos) || StrUtil.isBlank(sampleType)) {
            return new ArrayList<>();
        }
        dto.setCreator(SecurityUtils.getMemberId());
        dto.setPageNum(1);
        dto.setPageSize(5000);
        final PageImpl<Sample> samples = sampleRepository.findSample(dto);
        return initSampleTableData(samples.getContent(), false);
    }

    /**
     * 将Sample数据转换为Handsontable数据
     */
    private List<Map<String, Object>> initSampleTableData(final List<Sample> samples, boolean hasTempId) {
        return initSampleTableData(samples, hasTempId, null);
    }

    private List<Map<String, Object>> initSampleTableData(final List<Sample> samples, boolean hasTempId, final Map<String, String> customAttrMap) {
        if (CollUtil.isEmpty(samples)) {
            return null;
        }
        List<Map<String, Object>> rows = null;
        // 表格回显数据
        if (CollUtil.isNotEmpty(samples)) {
            final String initAudit = AuditEnum.init.name();
            rows = new ArrayList<>();
            for (Sample sample : samples) {
                final Map<String, Object> row = new HashMap<>();
                if (hasTempId || !initAudit.equals(sample.getAudited())) {
                    // 不回显临时id
                    row.put(SAMPLE_ID, sample.getSapNo());
                }
                row.put(SAMPLE_NAME, sample.getName());
                row.put(ORGANISM, initFullOrganism(sample));
                row.put(DESCRIPTION, sample.getDescription());
                row.put(RELATED_LINKS, relatedLinksStr(sample.getRelatedLinks()));
                row.put(TISSUE, sample.getTissue());
                row.put(PROTOCOL, sample.getProtocol());
                Map<String, String> attr = sample.getAttributes();
                if (CollUtil.isNotEmpty(attr)) {
                    row.putAll(attr);
                }
                final Map<String, String> customAttr = sample.getCustomAttr();
                Map<String, String> customAttrDesc = sample.getCustomAttrDesc();
                if (customAttrDesc == null) {
                    customAttrDesc = new LinkedHashMap<>();
                }
                if (CollUtil.isNotEmpty(customAttr)) {
                    if (customAttrMap != null) {
                        for (String key : customAttr.keySet()) {
                            customAttrMap.put(key, customAttrDesc.get(key));
                        }
                    }
                    row.putAll(customAttr);
                }
                rows.add(row);
            }
        }
        return rows;
    }

    public List<DeleteErrorMsgVO> delete(String subNo, String sampleType, Boolean single) {
        if (StrUtil.isBlank(subNo) || single == null || StrUtil.isBlank(sampleType)) {
            throw new ServiceException("The request parameter is illegal");
        }
        Submission submission = getEditSubmissionByNo(subNo);

        List<String> sapNos = null;
        if (single) {
            sapNos = CollUtil.newArrayList(submission.getSapSingleNo());
            submission.setSapSingleNo(null);
        } else {
            List<Submission.SampleGroup> sapMultipleData = submission.getSapMultipleData();

            List<Submission.SampleGroup> newMultipleData = new ArrayList<>();

            if (CollUtil.isNotEmpty(sapMultipleData)) {

                for (Submission.SampleGroup sapMultipleDatum : sapMultipleData) {

                    if (sapMultipleDatum.getType().equals(sampleType)) {
                        sapNos = sapMultipleDatum.getNos();
                    } else {
                        newMultipleData.add(sapMultipleDatum);
                    }
                }
            }
            submission.setSapMultipleData(newMultipleData);
        }

        if (CollUtil.isEmpty(sapNos)) {
            throw new ServiceException("There is no data to delete");
        }

        List<DeleteErrorMsgVO> vos = new ArrayList<>();

        for (String sapNo : sapNos) {
            // 校验当前组学在哪些Run中已被使用
            List<Run> runs = runRepository.findAllNameByFieldAndNo("sap_no", sapNo);

            if (CollUtil.isNotEmpty(runs)) {
                for (Run run : runs) {
                    Run tempData = run.getTempData();

                    DeleteErrorMsgVO vo = new DeleteErrorMsgVO();

                    Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("not found sample: " + sapNo));
                    vo.setTarget(sample.getName());

                    vo.setName(tempData.getName());
                    vo.setType(AuthorizeType.run.name());

                    String tempSubNo = tempData.getSubNo();
                    vo.setSubNo(tempSubNo.equals(subNo) ? tempSubNo + " (Current Submission)" : tempSubNo);

                    vo.setNo(AuditEnum.init.name().equals(tempData.getAudited()) ? "Unassigned formal ID" : tempData.getRunNo());

                    vos.add(vo);
                }
            }
        }

        if (CollUtil.isNotEmpty(vos)) {
            return vos;
        }

        // 删除数据
        sampleRepository.deleteTempByNosAndCreator(sapNos, SecurityUtils.getMemberId(), null);

        saveEditSubmission(submission);
        return null;
    }

    public SampleVO saveEdit(SampleDTO dto) {
        String subNo = dto.getSubNo();
        Submission submission = getEditSubmissionByNo(subNo);

        String sapNo = dto.getSapNo();

        // 校验名称是否存在
        checkNameExist(SecurityUtils.getMemberId(), sapNo, dto.getName());

        // 根据Organism填充 taxId
        if (StrUtil.isNotBlank(dto.getOrganism())) {
            final TaxonomyEsDTO taxonomyNode = findTaxId(dto.getOrganism());
            if (taxonomyNode == null || taxonomyNode.getTaxId() == null) {
                throw new ServiceException(StrUtil.upperFirst(ORGANISM) + " is not a valid organization value");
            }
            dto.setTaxId(taxonomyNode.getTaxId());
            dto.setOrganism(taxonomyNode.getScientificName());
        }

        String subjectType = dto.getSubjectType();
        final Map<String, String> attributesMap = dto.getAttributes();

        ExpSampleType expSampleType = expSampleTypeRepository.findByName(subjectType);
        List<Attributes> attributesList = expSampleType.getAttributes();
        // 校验属性中的值
        for (Attributes attributes : attributesList) {
            String attField = attributes.getAttributesField();
            if (!attributesMap.containsKey(attField)) {
                continue;
            }
            String dataSource = attributes.getDataSource();
            String attrVal = attributesMap.get(attField);
            // 校验Biome
            if (HOST_BIOME.equals(dataSource) || NON_HOST_BIOME.equals(dataSource) || ENV_BIOME.equals(dataSource) || ENV_BIOME_WATER.equals(dataSource)) {
                Set<String> valSet;
                if (attributes.isAllowCreate() && attrVal.contains(DELIMITER)) {
                    String[] split = attrVal.split(DELIMITER);
                    valSet = CollUtil.newHashSet(split);
                } else {
                    valSet = CollUtil.newHashSet(attrVal);
                }

                for (String val : valSet) {
                    boolean existBiome = remoteDictService.existBiome(dataSource, StrUtil.trim(val), SecurityConstants.INNER).getData();
                    if (!existBiome) {
                        throw new ServiceException(attributes.getAttributesName() + " isn't a valid value!");
                    }
                }
            }

            // 校验disease
            if (DISEASE.equals(dataSource) && StrUtil.isNotBlank(attributesMap.get(DISEASE))) {
                if (Boolean.FALSE.equals(remoteDictService.existDisease(attributesMap.get(DISEASE), SecurityConstants.INNER).getData())) {
                    throw new ServiceException(attributes.getAttributesName() + " isn't a valid disease value!");
                }
            }
        }

        Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("not found sample: " + sapNo));

        if (!StrUtil.equals(sample.getVisibleStatus(), VisibleStatusEnum.Unaccessible.name())) {
            throw new ServiceException("No editing allowed!");
        }
        Sample tempData;
        if (sample.getTempData() == null) {
            tempData = SampleDTOMapper.INSTANCE.copy(sample);
        } else {
            tempData = sample.getTempData();
        }
        SampleDTOMapper.INSTANCE.copyToDb(dto, tempData);

        tempData.setSubmitter(submission.getSubmitter());
        tempData.setRelatedLinks(NodeUtils.cleanRelatedLinks(tempData.getRelatedLinks()));
        tempData.setUpdateDate(new Date());
        tempData.setAudited(AuditEnum.unaudited.name());

        sample.setTempData(tempData);

        // 处理自定义属性
        setCustomAttr(sample, dto.getCustomAttributes());

        // 保存文献
        saveEditPublish(dto.getPublish(), AuthorizeType.sample, sample.getSapNo());

        // 保存样本
        sampleRepository.save(sample);

        // 更新submission
        submission.setSapSingleNo(sample.getSapNo());
        saveEditSubmission(submission);

        return getSapInfoByNo(sample.getSapNo());
    }

    public DeleteCheckResultVO deleteCheck(String sapNo, String memberId, boolean validateShare) {
        Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("not found sample: " + sapNo));
        // 当前用户是否是owner
        if (!StrUtil.equals(sample.getCreator(), memberId)) {
            throw new ServiceException("No Permission!");
        }
        // 找到这个sample下的Run
        List<Run> runList = runRepository.findAllBySapNoIn(CollUtil.newArrayList(sapNo));

        // 找到dataList
        List<String> runNos = runList.stream().map(Run::getRunNo).distinct().collect(Collectors.toList());
        List<Data> dataList = dataRepository.findAllByRunNoIn(runNos);
        List<String> dataNos = dataList.stream().map(Data::getDatNo).distinct().collect(Collectors.toList());

        Set<DeleteErrorMsgVO> errors = new HashSet<>();

        if (sample.getTempData() != null) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(sapNo);
            vo.setNo(sample.getTempData().getSapNo());
            vo.setType(AuthorizeType.sample.name());
            vo.setName(sample.getTempData().getName());
            vo.setSubNo(sample.getTempData().getSubNo());
            errors.add(vo);
        }

        for (Run run : runList) {
            if (run.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(sapNo);
                vo.setNo(run.getTempData().getRunNo());
                vo.setType(AuthorizeType.run.name());
                vo.setName(run.getTempData().getName());
                vo.setSubNo(run.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        List<Run> tempRunList = runRepository.findTempBySapNoIn(CollUtil.newArrayList(sapNo));
        for (Run run : tempRunList) {
            if (run.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(sapNo);
                vo.setNo(run.getTempData().getRunNo());
                vo.setType(AuthorizeType.run.name());
                vo.setName(run.getTempData().getName());
                vo.setSubNo(run.getTempData().getSubNo());
                errors.add(vo);
            }
        }
        for (Data data : dataList) {
            if (data.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(sapNo);
                vo.setNo(data.getTempData().getDatNo());
                vo.setType(AuthorizeType.data.name());
                vo.setName(data.getTempData().getName());
                vo.setSubNo(data.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        List<Data> tempDataList = dataRepository.findTempByRunNoIn(runNos);

        for (Data data : tempDataList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(sapNo);
            vo.setNo(data.getTempData().getDatNo());
            vo.setType(AuthorizeType.data.name());
            vo.setName(data.getTempData().getName());
            vo.setSubNo(data.getTempData().getSubNo());
            errors.add(vo);
        }

        DeleteCheckResultVO resultVO = new DeleteCheckResultVO();
        resultVO.setSapNos(CollUtil.newArrayList(sapNo));
        resultVO.setRunNos(runNos);
        resultVO.setDataNos(dataNos);
        resultVO.setErrors(new ArrayList<>(errors));

        // 是否需要校验被删除的数据在share review request里面使用
        if (validateShare) {
            validateShareAndReviewAndRequest(resultVO, memberId);
        }
        return resultVO;
    }

    public void deleteSampleAll(String sapNo, String memberId) {
        Sample sample = sampleRepository.findTopBySapNo(sapNo).orElseThrow(() -> new ServiceException("not found sample: " + sapNo));
        // 当前用户是否是owner
        if (!StrUtil.equals(sample.getCreator(), memberId)) {
            throw new ServiceException("No Permission!");
        }
        DeleteCheckResultVO checkResultVO = deleteCheck(sapNo, memberId, false);
        // 删除数据
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("The Sample cannot be deleted because it is associated with other data");
        }
        // 添加删除的日志
        addUserCenterDeleteLog(sapNo, AuthorizeType.sample.name(), checkResultVO);
        // 将visible_status更新为delete
        sampleRepository.updateToDeleteAllBySapNoIn(checkResultVO.getSapNos());
        runRepository.updateToDeleteAllByRunNoIn(checkResultVO.getRunNos());
        dataRepository.updateToDeleteAllByDatNoIn(checkResultVO.getDataNos());
        // 删除相关联的publish
        publishRepository.updateToDeleteByTypeAndTypeId(AuthorizeType.sample.name(), checkResultVO.getSapNos());

        // 通知删除es索引
        updateEsData(AuthorizeType.sample.name(), sapNo);
    }
}
