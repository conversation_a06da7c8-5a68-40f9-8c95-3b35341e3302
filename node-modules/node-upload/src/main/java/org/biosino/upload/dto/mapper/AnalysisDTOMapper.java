package org.biosino.upload.dto.mapper;

import org.biosino.common.mongo.entity.Analysis;
import org.biosino.upload.dto.AnalysisDTO;
import org.biosino.upload.vo.AnalysisVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2024/2/4
 */
@Mapper
public interface AnalysisDTOMapper {
    AnalysisDTOMapper INSTANCE = Mappers.getMapper(AnalysisDTOMapper.class);

    @Mapping(target = "tempData", ignore = true)
    Analysis copy(Analysis source);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tempData", ignore = true)
    void tempToDb(Analysis source, @MappingTarget Analysis target);

    @Mapping(target = "tempData", ignore = true)
    void copy(Analysis source, @MappingTarget Analysis target);

    void copyToDb(AnalysisDTO source, @MappingTarget Analysis target);

    void copyToVo(Analysis analysis, @MappingTarget AnalysisVO target);
}
