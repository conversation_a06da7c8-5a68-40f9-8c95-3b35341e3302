package org.biosino.upload.vo;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class ExperimentVO {

    private String expNo;
    private String projectNo;
    private String expType;
    private String name;
    private String description;
    private String protocol;

    private Map<String, Object> attributes = new LinkedHashMap<>();

    private List<String> relatedLinks;

    private List<PublishVO> publish;
}
