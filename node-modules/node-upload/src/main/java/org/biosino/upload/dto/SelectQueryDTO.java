package org.biosino.upload.dto;

import lombok.Data;
import org.biosino.common.mongo.dto.BaseQuery;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/10
 */
@Data
public class SelectQueryDTO extends BaseQuery {
    private String type;
    private String idField;
    private String name;
    private String email;
    private String creator;
    private List<String> shareNos = new ArrayList<>();
}
