package org.biosino.upload.aspect;

import cn.hutool.core.thread.ThreadUtil;
import lombok.AllArgsConstructor;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.HttpUploadLog;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/15
 */
@Aspect
@Component
@AllArgsConstructor
public class HttpUploadLogAspect {

    private final MongoTemplate mongoTemplate;

    @Pointcut("execution(* org.biosino.upload.service.UploadService.uploadFile(..))")
    public void pointcut() {
    }

    @AfterReturning(value = "pointcut()", returning = "result")
    public void afterReturning(Object result) {
        Data data = (Data) result;
        HttpUploadLog httpUploadLog = HttpUploadLog.builder()
                .dataNo(data.getDatNo())
                .fileSize(data.getFileSize())
                .creator(data.getCreator())
                .createTime(new Date())
                .ip(IpUtils.getIpAddr())
                .build();
        // 异步保存
        ThreadUtil.execute(() -> mongoTemplate.save(httpUploadLog));
    }
}
