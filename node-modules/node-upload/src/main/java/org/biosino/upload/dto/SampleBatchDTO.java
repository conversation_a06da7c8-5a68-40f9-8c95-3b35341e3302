package org.biosino.upload.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.upload.dto.abs.AbsBatchDTO;

import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class SampleBatchDTO extends AbsBatchDTO {

    @NotBlank(message = "Sample Type cannot be empty")
    private String sampleType;

    /**
     * 所有自定义属性字段名以及描述信息（包含配置属性和自定义属性）
     */
    private List<ExpSampleType.Attributes> allAttrDes;

    public Map<String, String> initDescriptionMap() {
        final Map<String, String> map = new HashMap<>();
        if (allAttrDes != null && !allAttrDes.isEmpty()) {
            for (ExpSampleType.Attributes allAttrDe : allAttrDes) {
                final String attributesField = StrUtil.trimToNull(allAttrDe.getAttributesField());
                final String description = StrUtil.trimToNull(allAttrDe.getDescription());
                if (attributesField != null || description != null) {
                    map.put(attributesField, description);
                }
            }
        }
        return map;
    }
}
