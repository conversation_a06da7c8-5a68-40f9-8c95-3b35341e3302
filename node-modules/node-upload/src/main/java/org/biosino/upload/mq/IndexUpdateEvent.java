package org.biosino.upload.mq;

import lombok.Getter;
import org.biosino.common.core.enums.AuthorizeType;
import org.springframework.context.ApplicationEvent;
import org.springframework.util.CollectionUtils;

import java.util.Collection;

/**
 * <AUTHOR>
 */
@Getter
public class IndexUpdateEvent extends ApplicationEvent {

    private AuthorizeType type;
    private String[] typeIds;

    // 是否为删除node_related_es操作
    private Boolean deleteRelatedEs = false;

    public IndexUpdateEvent(Object source, AuthorizeType type, String... typeIds) {
        super(source);
        this.type = type;
        this.typeIds = typeIds;
    }

    public IndexUpdateEvent(Object source, AuthorizeType type, Collection<String> typeIds) {
        this(source, type, typeIds, false);
    }

    public IndexUpdateEvent(Object source, AuthorizeType type, Collection<String> typeIds, boolean deleteRelatedEs) {
        super(source);
        this.type = type;
        this.typeIds = CollectionUtils.isEmpty(typeIds) ? new String[]{} : typeIds.toArray(new String[]{});
        this.deleteRelatedEs = deleteRelatedEs;
    }


}
