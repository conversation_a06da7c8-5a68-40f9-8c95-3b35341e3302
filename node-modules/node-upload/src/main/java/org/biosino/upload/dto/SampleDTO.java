package org.biosino.upload.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class SampleDTO {
    @NotBlank
    private String subNo;

    private String sapNo;

    @NotBlank
    private String subjectType;

    @NotBlank
    private String name;
    private String organism;
    private String taxId;
    private String tissue;
    private String description;
    private String protocol;

    private Map<String, String> attributes = new LinkedHashMap<>();

    private List<Map<String, String>> customAttributes;

    private List<String> relatedLinks;

    private List<PublishDTO> publish;

    public void setName(String name) {
        this.name = StrUtil.trim(name);
    }
}
