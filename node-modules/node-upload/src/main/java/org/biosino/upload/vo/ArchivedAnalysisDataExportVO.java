package org.biosino.upload.vo;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/2/3
 */
@Data
public class ArchivedAnalysisDataExportVO {
    @Excel(name = "Data ID")
    private String datNo;

    @Excel(name = "File Name")
    private String name;

    @Excel(name = "Analysis ID")
    private String analNo;

    @Excel(name = "Analysis Name")
    private String analName;

    @Excel(name = "Upload Date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @Excel(name = "md5")
    private String md5;

    @Excel(name = "File Size")
    private String readableFileSize;
}
