package org.biosino.upload.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import org.biosino.common.core.enums.SubmissionStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.Submission;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.dto.SubmitterDTO;
import org.biosino.upload.repository.SubmissionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;


@Service
public class SubmitterService {

    @Autowired
    private SubmissionRepository submissionRepository;

    public Submission getSubmissionByNo(String subNo) {
        return submissionRepository.findTopBySubNo(subNo).orElseThrow(() -> new ServiceException("Not found data"));
    }

    public Submission save(SubmitterDTO dto) {
        String subNo = dto.getSubNo();

        Submitter submitter = new Submitter();
        submitter.setMemberId(SecurityUtils.getMemberId());
        BeanUtil.copyProperties(dto, submitter);

        // 新增submission
        if (StrUtil.isBlank(subNo)) {
            Submission submission = new Submission();
            submission.setSubmitter(submitter);
            submission.setDataType(dto.getDataType());
            submission.setCreator(SecurityUtils.getMemberId());
            submission.setCreateTime(new Date());
            submission.setUpdateTime(new Date());
            submission.setStatus(SubmissionStatusEnum.editing.name());
            return submissionRepository.save(submission);
        }

        // 更新submission
        Submission submission = getSubmissionByNo(subNo);

        if (!submission.getCreator().equals(SecurityUtils.getMemberId())) {
            throw new ServiceException("Operation failed, no permission");
        }

        submission.setSubmitter(submitter);
        submission.setUpdateTime(new Date());
        return submissionRepository.save(submission);
    }

    public Submission getSubmissionByTypeAndNo(String type, String no) {
        if (TypeInformation.typeInfoMap.get(type) == null) {
            throw new ServiceException("AuthorizeType does not exist");
        }

        Optional<String> optional = submissionRepository.findSubNoByTypeAndNo(TypeInformation.typeInfoMap.get(type), no);
        if (optional.isPresent()) {
            Optional<Submission> subOptional = submissionRepository.findTopBySubNo(optional.get());
            if (subOptional.isPresent()) {
                return subOptional.get();
            }
        }
        Submission submission = new Submission();
        Submitter submitter = submissionRepository.getSubmitterByTypeAndNo(TypeInformation.typeInfoMap.get(type), no);
        submission.setSubmitter(submitter);
        return submission;
    }
}
