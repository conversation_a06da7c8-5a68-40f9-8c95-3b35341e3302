package org.biosino.upload.dto.abs;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/22
 */
@Data
public abstract class AbsBatchDTO {
    public static final int MAX_ROW_COUNT = 10000;

    @NotBlank(message = "The submission stage cannot be empty")
    private String stage;

    @NotBlank(message = "Submission No cannot be empty")
    private String subNo;

    @Size(min = 1, max = MAX_ROW_COUNT, message = "The number of table rows must be between 1 and " + MAX_ROW_COUNT)
    private List<Object[]> datas;

    @Size(min = 2, max = 300, message = "The number of titles must be between 2 and 300")
    private List<String> titles;

    /**
     * 删除空行
     */
    public void delBlank() {
        this.dealBlankData();
    }

    public void dealBlankData() {
        final List<Object[]> datas = this.getDatas();
        final List<Object[]> list = new LinkedList<>();
        if (datas != null && !datas.isEmpty()) {
            for (final Object[] row : datas) {
                if (row != null) {
                    boolean hasData = false;
                    for (Object o : row) {
                        if (o != null) {
                            hasData = StrUtil.isNotBlank(o.toString());
                            if (hasData) {
                                break;
                            }
                        }
                    }
                    if (hasData) {
                        list.add(row);
                    }
                }
            }
            datas.clear();
        }
        this.setDatas(list);
    }
}
