package org.biosino.upload.tool.excelparseutil.imp.archiving;

import org.biosino.common.core.enums.dict.ExcelCellType;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.upload.tool.excelparseutil.imp.ExcelParser;

import java.io.File;
import java.util.LinkedHashMap;
import java.util.Map;

public class RawDataArchivingParse extends ExcelParser {

    public RawDataArchivingParse(File excelFile) {
        super(excelFile);
    }

    public RawDataArchivingParse(File excelFile, Map<String, Integer> param) throws ServiceException {
        super(excelFile, param, null);
    }

    @Override
    protected Map<String, Integer> getDefaultConf() {
        Map<String, Integer> param = new LinkedHashMap<>();
        param.put("archiving", 12);
        return param;
    }

    @Override
    public Map<String, Map<String, ExcelCellType>> definedTitle() {

        Map<String, Map<String, ExcelCellType>> titles = new LinkedHashMap<>();

        // 词条列表
        // archiving
        Map<String, ExcelCellType> archiving = new LinkedHashMap<>();
        archiving.put("experiment_id", ExcelCellType.string_);
        archiving.put("experiment_name", ExcelCellType.string_);
        archiving.put("sample_id", ExcelCellType.string_);
        archiving.put("sample_name", ExcelCellType.string_);
        archiving.put("run_name", ExcelCellType.string_);
        archiving.put("run_description", ExcelCellType.string_);
        archiving.put("data_id", ExcelCellType.string_);
        archiving.put("file_name", ExcelCellType.string_);
        archiving.put("data_remark", ExcelCellType.string_);

        // 所有sheet名
        titles.put("archiving", archiving);

        return titles;
    }
}
