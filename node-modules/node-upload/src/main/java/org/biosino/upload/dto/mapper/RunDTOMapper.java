package org.biosino.upload.dto.mapper;

import org.biosino.common.mongo.entity.Run;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * Data对象拷贝
 *
 * <AUTHOR>
 */
@Mapper
public interface RunDTOMapper {
    RunDTOMapper INSTANCE = Mappers.getMapper(RunDTOMapper.class);

    @Mapping(target = "tempData", ignore = true)
    Run copy(Run run);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tempData", ignore = true)
    void tempToDb(Run source, @MappingTarget Run target);

}
