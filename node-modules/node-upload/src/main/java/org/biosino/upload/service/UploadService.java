package org.biosino.upload.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.CompressUtil;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.file.FileTypeUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.common.mongo.entity.Express;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.common.security.utils.ConfigUtils;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.system.api.RemoteNotificationService;
import org.biosino.system.api.RemoteUserService;
import org.biosino.system.api.dto.ExpressInfoDTO;
import org.biosino.system.api.model.Member;
import org.biosino.upload.dto.abs.AbsBatchDTO;
import org.biosino.upload.dto.mapper.DataDTOMapper;
import org.biosino.upload.repository.DataRepository;
import org.biosino.upload.repository.ExpSampleTypeRepository;
import org.biosino.upload.repository.ExpressRepository;
import org.biosino.upload.tool.excelparseutil.CellData;
import org.biosino.upload.tool.excelparseutil.imp.ExcelParser;
import org.biosino.upload.tool.excelparseutil.imp.analysis.AnalysisParse;
import org.biosino.upload.tool.excelparseutil.imp.archiving.AnalysisArchivingParse;
import org.biosino.upload.tool.excelparseutil.imp.archiving.RawDataArchivingParse;
import org.biosino.upload.tool.excelparseutil.imp.expriment.ExperimentExcelParser;
import org.biosino.upload.tool.excelparseutil.imp.sample.SampleExcelParser;
import org.biosino.upload.vo.FileVo;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;


/**
 * <AUTHOR> Li
 * @date 2023/12/29
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UploadService {

    private final DataRepository dataRepository;

    private final ExpressRepository expressRepository;

    private final RemoteNotificationService remoteNotificationService;

    private final ExpSampleTypeRepository expSampleTypeRepository;

    private final RemoteUserService remoteUserService;

    public static final String SECURITY = "security";

    public Data uploadFile(MultipartFile mulFile) throws IOException {
        // 校验文件是不是已经上传过
        String memberId = SecurityUtils.getMemberId();
        String fileMd5 = SecureUtil.md5(mulFile.getInputStream());
        Optional<Data> optional = dataRepository.findFirstUnarchivedByMd5(fileMd5, memberId);
        if (optional.isPresent()) {
            // 已经有了相同md5的数据
            throw new ServiceException("Already have same md5 Unarchived Data, please don't upload same file again!");
        }

        // 保证目录日期和Data的createDate是同一天
        Date date = new Date();
        // 将文件保存在本地
        FileVo fileVo = transferMultipartFileToDisk(mulFile, date);
        // 保存上传记录
        Data data = saveUploadedFile(fileVo, date);
        return data;
    }


    @SneakyThrows
    private FileVo transferMultipartFileToDisk(MultipartFile mulFile, Date date) {
        String blackList = ConfigUtils.getConfig("node.file.suffixBlackList");
        blackList = blackList + "|.md5";
        if (StrUtil.isNotBlank(blackList)) {
            String[] split = blackList.split("\\|");
            if (StrUtil.endWithAnyIgnoreCase(mulFile.getOriginalFilename(), split)) {
                throw new ServiceException(StrUtil.format("File upload of {} type is not allowed.", blackList));
            }
        }

        if (!MyFileUtils.isValidFilepath(mulFile.getOriginalFilename())) {
            throw new ServiceException("File name contains illegal characters is not allowed. Such as spaces, ampersands (&), percent signs (%), asterisks (*), or Greek letters");
        }

        String extension = FilenameUtils.getExtension(mulFile.getOriginalFilename());

        extension = StrUtil.isNotBlank(extension) ? "." + extension : "";
        String relativePath = getTransToRelativePath(date, extension);
        File file = FileUtil.file(DirConstants.DATA_HOME, relativePath);
        file.getParentFile().mkdirs();
        mulFile.transferTo(file);
        return new FileVo(mulFile.getOriginalFilename(), file, relativePath);
    }

    // 如果文件已经存在，重新生成一个文件名，使用雪花id当作文件名
    private static String getTransToRelativePath(Date date, String extension) {
        String nextIdStr = IdUtil.getSnowflakeNextIdStr();
        String relativePath = DirectoryEnum.data.name() + File.separator
                + DateUtil.format(date, "yyyyMMdd") + File.separator
                + nextIdStr + extension;
        return FileUtil.exist(FileUtil.file(DirConstants.DATA_HOME, relativePath)) ? getTransToRelativePath(date, extension) : relativePath;
    }

    public Data saveUploadedFile(FileVo fileVo, Date date) {
        if (fileVo == null || fileVo.getFile() == null) {
            throw new ServiceException("file is empty!");
        }
        Data data = new Data();
        data.setId(IdUtil.objectId());
        data.setName(fileVo.getName());
        data.setMd5(SecureUtil.md5(fileVo.getFile()));
        data.setFileName(fileVo.getName());
        data.setFilePath(MyFileUtils.changeToLinuxSeparator(fileVo.getRelativePath()));
        data.setUploadType(UploadType.file.name());
        data.setSecurity(SecurityEnum._private.getDesc());
        data.setCreator(SecurityUtils.getMemberId());
        data.setCreateDate(date);
        data.setArchived(ArchiveEnum.no.name());
        data.setOwnership(OwnershipEnum.self_support.getDesc());
        data.setIp(IpUtils.getIpAddr());
        data.setFileSize(fileVo.getFile().length());
        data.setDataType(FileTypeUtils.getDataTypeByName(fileVo.getName()));

        dataRepository.save(data);
        // 保存一个tempData
        Data tempData = DataDTOMapper.INSTANCE.copy(data);
        data.setTempData(tempData);

        dataRepository.save(data);
        return data;
    }


    public void saveSubmitRawDataExpressSubmission(ExpressInfoDTO expressInfoDTO) {
        // 保存上传记录
        String submissionNo = saveExpressSubmission(expressInfoDTO);

        expressInfoDTO.setUserEmail(SecurityUtils.getMember().getEmail());
        // 发送邮件给管理员
        remoteNotificationService.sendExpressEmail(expressInfoDTO, SecurityConstants.INNER);
    }

    private String saveExpressSubmission(ExpressInfoDTO expressInfoDTO) {
        Member member = SecurityUtils.getMember();
        Express express = new Express();
        BeanUtil.copyProperties(expressInfoDTO, express);
        Submitter submitter = new Submitter();
        BeanUtil.copyProperties(member, submitter);
        express.setSubmitter(submitter);
        express.setCreator(member.getId());
        express.setCreateTime(new Date());
        expressRepository.save(express);
        return express.getId();
    }


    public File getDataCollectionTbl(final String fileName) throws IOException {
        /*final String dir = DirConstants.DATA_HOME + File.separator + DirectoryEnum.documents.name();
        File file = new File(dir, "download" + File.separator + fileName);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }*/
        final File file = MyFileUtils.expSampleTemplate(DirConstants.DATA_HOME, fileName);
        final File dir = file.getParentFile().getParentFile();
        if (!file.exists()) {
            final File fileTmp = new File(dir, "error.txt");
            fileTmp.createNewFile();
            List<String> list = new ArrayList<>();
            list.add("Request " + fileName + "  File does not exist!");
//            FileUtils.writeLines(fileTmp, "UTF-8", list, true);
            FileUtil.writeUtf8Lines(list, fileTmp);
            return fileTmp;
        }
        File file1 = new File(dir, fileName.substring(0, fileName.lastIndexOf('.')) + ".zip");
        CompressUtil.zip(file, file1);
        return file1;
    }


    public void downloadExperimentTemplate(HttpServletRequest request, HttpServletResponse response, String type, String name) {
        downloadExperimentTemplate(request, response, type, name, false);
    }


    public void downloadExperimentTemplate(HttpServletRequest request, HttpServletResponse response, final String type, final String name, final boolean isExample) {
        if (StrUtil.isNotBlank(type) && StrUtil.isNotBlank(name)) {
            final String sapPre = "Example_";
            final String prefix = isExample ? sapPre : StrUtil.EMPTY;
            try {
                switch (type) {
                    case "experiment":
                    case "sample":
                        ExpSampleType expSampleType = expSampleTypeRepository.findByName(name);

                        if (expSampleType == null) {
                            throw new ServiceException("Experiment dictionary data not found exp data");
                        }

                        final String templateName = expSampleType.getTemplateName();
                        if (StrUtil.isNotBlank(templateName)) {
                            final File file = getDataCollectionTbl(templateName.toLowerCase().startsWith(sapPre.toLowerCase()) ? templateName : (prefix + templateName));
                            DownloadUtils.download(request, response, file);
                        }
                        break;
                    case "analysis": {
                        File file = getDataCollectionTbl(prefix + "NODE_analysis_v1.0.xlsx");
                        DownloadUtils.download(request, response, file);
                        break;
                    }
                    case "archiving": {
                        File file = getDataCollectionTbl(prefix + "NODE_archiving_" + name + "_v2.0.xlsx");
                        DownloadUtils.download(request, response, file);
                        break;
                    }
                }
                /* else if ("=".equals(type)) {
                File file = uploadService.getDataCollectionTbl("NODE_archiving_v1.0.xlsx");
                DownloadUtils.download(request, response, file);
                } */
            } catch (IOException e) {
                log.warn("下载失败", e);
            }
        }
    }

    public Map<String, Object> parserExcelBySampleType(MultipartFile file, String stage, String type) {

        Map<String, List<Map<String, CellData>>> datas = null;
        final Map<String, String> titleAndDesc = new HashMap<>();
        File excel = null;
        try {
            if (StrUtil.isBlank(stage)) {
                throw new ServiceException("Parameter error.");
            }
            // 文件后缀检查(flag=false表示有错误)
            if (!checkFileName(file)) {
                throw new ServiceException("Please upload an excel file.");
            }
            final AuthorizeType stageTypeEnum = AuthorizeType.findByName(stage).orElseThrow(() -> new ServiceException("The stage type is wrong. Please Check."));

            excel = MyFileUtils.multipartFileToFile(file);

            // 根据不同阶段(stage)解析
            if (AuthorizeType.experiment.equals(stageTypeEnum) || AuthorizeType.sample.equals(stageTypeEnum)) {
                if (StrUtil.isBlank(type)) {
                    throw new ServiceException("Parameter error.");
                }
                ExpSampleType expSampleType = expSampleTypeRepository.findByName(type);

                if (expSampleType == null) {
                    throw new ServiceException("Experiment dictionary data not found exp data");
                }

                final Map<String, ExpSampleType.Attributes> rangeMap = ExpSampleType.handelExpAttrMap(expSampleType);
                // 判断表格title格式
                if (!checkTitle(excel, stageTypeEnum, rangeMap.keySet(), titleAndDesc)) {
                    throw new ServiceException("The format of file is wrong. Please Check");
                }
                datas = parserExpOrSapExcelByType(excel, type, rangeMap, stageTypeEnum, expSampleType);
            } else if (AuthorizeType.analysis.equals(stageTypeEnum)) {
                datas = parserAnalysisExcel(excel);
            } else if (AuthorizeType.archiving.equals(stageTypeEnum)) {
                datas = parserArchivingExcelByType(excel, type);
            } else {
                throw new ServiceException("The stage type is wrong. Please Check.");
            }
        } catch (IOException e) {
            log.warn("上传模板出错", e);
            throw new ServiceException("Upload error:" + e.getMessage());
        } finally {
            if (excel != null && excel.exists()) {
                FileUtil.del(excel);
            }
        }
        if (datas == null || datas.isEmpty()) {
            return null;
        }

        if (datas.size() > AbsBatchDTO.MAX_ROW_COUNT) {
            throw new ServiceException(StrUtil.format("Upload error: The data has exceeded {} rows", AbsBatchDTO.MAX_ROW_COUNT));
        }
        final LinkedHashMap<String, Object> result = new LinkedHashMap<>(datas);
        if (CollUtil.isNotEmpty(titleAndDesc)) {
            result.put("title_and_desc", titleAndDesc);
        }
        return result;
    }

    private Map<String, List<Map<String, CellData>>> parserExpOrSapExcelByType(File excel, String expType,
                                                                               final Map<String, ExpSampleType.Attributes> rangeMap,
                                                                               AuthorizeType stageTypeEnum, ExpSampleType expSampleType) {
        Map<String, List<Map<String, CellData>>> datas;
        if (StrUtil.isNotBlank(expType)) {
            final boolean delFile = true;
            if (AuthorizeType.experiment.equals(stageTypeEnum)) {
                // 实验数据上传
//                rangeMap.put(SECURITY, new ExpSampleType.Attributes(SecurityEnum.includeAllSecurity()));
                datas = new ExperimentExcelParser(excel, expSampleType).parse(rangeMap, delFile);
            } else {
                // 样本数据上传
                datas = new SampleExcelParser(excel, expSampleType).parse(rangeMap, delFile);
            }
        } else {
            throw new RuntimeException("The experiment type is wrong.");
        }
        return datas;
    }

    private boolean checkFileName(MultipartFile file) throws IOException {
//        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String suffix = FileUtil.extName(file.getOriginalFilename());
//        List<String> excelSuffix = CollUtil.toList("xls", "xlsx", "XLS", "XLSX");
        List<String> excelSuffix = CollUtil.toList("xlsx", "XLSX");
        if (!excelSuffix.contains(suffix)) {
            return false;
        } else {
            return true;
        }
    }

    private boolean checkTitle(File excel, final AuthorizeType stage, final Set<String> fieldSet, final Map<String, String> titleAndDesc) throws IOException {
        Workbook workbook = null;
        try {
            workbook = new XSSFWorkbook(FileUtils.openInputStream(excel));
            int sheetNum = workbook.getNumberOfSheets();
            if (sheetNum < 1) {
                return false;
            } else {
                Set<String> titles = null;
                Sheet sheet = workbook.getSheetAt(0);
                final Row row = sheet.getRow(12);
                String sheetRequired = "";
                if (AuthorizeType.experiment.equals(stage)) {
                    titles = fieldSet;
                } else if (AuthorizeType.sample.equals(stage)) {
                    titles = fieldSet;
                } else if (AuthorizeType.analysis.equals(stage)) {
                    sheetRequired = "analysis";
                    if (!sheetRequired.equals(sheet.getSheetName())) {
                        return false;
                    }
                } else if (AuthorizeType.archiving.equals(stage)) {
                    sheetRequired = "archiving";
                    if (!sheetRequired.equals(sheet.getSheetName())) {
                        return false;
                    }
                }

                if (titles != null && !titles.isEmpty()) {
                    final Set<String> titleSet = new HashSet<>();
                    for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
                        final Cell cell = row.getCell(j);
                        final String val = StrUtil.trimToNull(cell.getStringCellValue());
                        if (val != null) {
                            titleSet.add(val);
                            if (AuthorizeType.sample.equals(stage)) {
                                // 样本支持自定义属性
                                final Comment comment = cell.getCellComment();
                                String text = "";
                                if (comment != null) {
                                    // String author = comment.getAuthor();
                                    text = StrUtil.trimToEmpty(comment.getString().getString());
                                }
                                titleAndDesc.put(val, text);
                            }
                        }
                    }
                    if (!titleSet.containsAll(titles)) {
                        return false;
                    }
                }
            }
            return true;
        } finally {
            IoUtil.close(workbook);
        }
    }

    private Map<String, List<Map<String, CellData>>> parserAnalysisExcel(File excel) {
        Map<String, List<Map<String, CellData>>> datas = null;
        try {

            ExcelParser parser = new AnalysisParse(excel);
            datas = parser.parse(null);
        } catch (Exception e) {
            log.warn("解析分析数据出错", e);
            throw new ServiceException("Upload error:" + e.getMessage());
        }
        return datas;
    }

    private Map<String, List<Map<String, CellData>>> parserArchivingExcelByType(File excelFile, String type) {
        Map<String, List<Map<String, CellData>>> datas = null;
        try {
            if ("rawData".equals(type)) {
                RawDataArchivingParse parse = new RawDataArchivingParse(excelFile);
                datas = parse.parse(null);
            } else {
                AnalysisArchivingParse parse = new AnalysisArchivingParse(excelFile);
                datas = parse.parse(null);
            }
        } catch (Exception e) {
            log.warn("解析分析数据出错", e);
            throw new ServiceException("Upload error:" + e.getMessage());
        }
        return datas;
    }

    public static String encode(String str) {
        try {
            return URLEncoder.encode(str, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }


}
