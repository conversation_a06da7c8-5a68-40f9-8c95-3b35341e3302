package org.biosino.upload.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class ProjectDTO {

    @NotBlank
    private String subNo;

    private String projectNo;

    @NotBlank
    private String name;

    private String description;

    private List<String> relatedLinks;

    private List<PublishDTO> publish;

    public void setSubNo(String subNo) {
        this.subNo = StrUtil.trimToNull(subNo);
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = StrUtil.trimToNull(projectNo);
    }

    public void setName(String name) {
        this.name = StrUtil.trimToNull(name);
    }

    public void setDescription(String description) {
        this.description = StrUtil.trimToNull(description);
    }
}
