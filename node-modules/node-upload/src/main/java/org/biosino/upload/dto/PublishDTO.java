package org.biosino.upload.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

@Data
public class PublishDTO {
    private String type;
    private String typeId;

    private String id;

    // journal（需注意：一期页面的字段和数据库不对应）
    private String publication;

    private String doi;

    private String pmid;

    // title
    private String articleName;

    private String reference;

    public Boolean isEmpty() {
        return StrUtil.isBlank(publication) && StrUtil.isBlank(doi) && StrUtil.isBlank(pmid) && StrUtil.isBlank(articleName) && StrUtil.isBlank(reference);
    }
}
