package org.biosino.upload.dto.mapper;

import org.biosino.common.mongo.entity.Data;
import org.biosino.upload.vo.DataVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * Data对象拷贝
 *
 * <AUTHOR>
 */
@Mapper
public interface DataDTOMapper {
    DataDTOMapper INSTANCE = Mappers.getMapper(DataDTOMapper.class);

    @Mapping(target = "tempData", ignore = true)
    Data copy(Data data);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tempData", ignore = true)
    void tempToDb(Data source, @MappingTarget Data target);

    void copyToVo(Data data, @MappingTarget DataVO dataVo);
}
