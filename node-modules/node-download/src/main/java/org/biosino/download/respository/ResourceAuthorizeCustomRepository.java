package org.biosino.download.respository;

import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface ResourceAuthorizeCustomRepository {

    boolean existsAuthorizedDataTo(String memberId, String dataNo);

    List<ResourceAuthorize> searchAuthorizeListByData(String memberId, Collection<String> notPublicDataNos);
}
