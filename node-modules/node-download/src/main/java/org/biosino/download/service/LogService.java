package org.biosino.download.service;

import org.biosino.common.core.domain.IpAddr;
import org.biosino.common.core.enums.DownloadType;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.mongo.entity.DownloadLog;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.download.respository.DownloadLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class LogService {

    @Autowired
    private DownloadLogRepository downloadLogRepository;

    @Async
    public void addDownloadLog(String type, String no, String ownerId, String ip) {
        DownloadLog downloadLog = new DownloadLog();
        downloadLog.setType(type);
        downloadLog.setTypeNo(no);
        downloadLog.setCreateTime(new Date());
        downloadLog.setIp(ip);
        downloadLog.setDownloadType(DownloadType.http.name());
        IpAddr ipAddr = IpUtils.getIpCountry(downloadLog.getIp());
        if (ipAddr != null) {
            downloadLog.setCountry(ipAddr.getCountry());
        }
        downloadLog.setMemberId(SecurityUtils.getMemberId());
        downloadLog.setOwnerId(ownerId);
        downloadLogRepository.insert(downloadLog);
    }
}
