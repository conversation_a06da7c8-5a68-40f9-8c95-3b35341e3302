package org.biosino.download.respository.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.Review;
import org.biosino.download.respository.ReviewCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@RequiredArgsConstructor
public class ReviewCustomRepositoryImpl implements ReviewCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Review getReviewByNo(String reviewNo, String code) {
        Query query = new Query();
        Criteria criteria = Criteria.where("review_id").is(reviewNo);
        if (StrUtil.isNotBlank(code)) {
            criteria.and("code").is(code);
        }
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Review.class);
    }

    @Override
    public Review getReviewByUsedIds(String usedIds, String code) {
        Query query = new Query();
        Criteria criteria = Criteria.where("used_ids").is(usedIds).and("code").is(code);
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Review.class);
    }

    ;
}
