package org.biosino.download.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriteConfig;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.WebConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.ReviewStatusEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.biosino.common.mongo.entity.Review;
import org.biosino.common.mongo.entity.Share;
import org.biosino.common.mongo.entity.other.ReviewData;
import org.biosino.common.mongo.entity.other.ShareData;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.download.dto.BatchExportParamsDTO;
import org.biosino.download.dto.mapper.RelatedDataDTOMapper;
import org.biosino.download.respository.*;
import org.biosino.download.vo.*;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.DataSearchDTO;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.system.api.model.Member;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.rmi.ServerException;
import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.common.core.utils.file.MyFileUtils.getTempDir;

/**
 * <AUTHOR> Li
 * @date 2024/3/19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DataDownloadService {

    private final DataRepository dataRepository;
    private final ShareRepository shareRepository;
    private final ReviewRepository reviewRepository;
    private final ResourceAuthorizeRepository resourceAuthorizeRepository;
    private final DownloadLogRepository downloadLogRepository;
    private final RemoteDataService remoteDataService;
    private final LogService logService;

    public void downloadPublicData(String dataNo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (StrUtil.isBlank(dataNo)) {
            throw new ServiceException("Download Error! dataNo is null!");
        }
        Data data = dataRepository.findByDatNo(dataNo).orElseThrow(() -> new ServiceException("Download Error! data not exists! dataNo = " + dataNo));
        if (data.getFileSize() > 200 * 1024 * 1024) {
            throw new ServerException("Download Error! Files downloaded via http cannot be larger than 200 MB!");
        }
        if ((data.getSecurity().equals(SecurityEnum._public.getDesc()))
                || ((data.getSecurity().equals(SecurityEnum._restricted.getDesc())) &&
                (data.getPublicDate() != null)
                && (data.getPublicDate().before(new Date())))) {
            File file = FileUtil.file(DirConstants.DATA_HOME, data.getFilePath());
            DownloadUtils.download(request, response, file, data.getFileName());
            // 增加下载次数
            dataRepository.incDownloadNum(dataNo);
            // 记录日志
            logService.addDownloadLog(AuthorizeType.data.name(), dataNo, data.getCreator(), IpUtils.getIpAddr(request));
        } else {
            throw new ServerException("Download Error! invalid link: data security error! dataNo = " + dataNo);
        }
    }

    public void downloadDataByDataNo(String dataNo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (StrUtil.isBlank(dataNo)) {
            throw new ServiceException("DownLoad Error! dataNo is null!");
        }
        Data data = dataRepository.findByDatNo(dataNo).orElseThrow(() -> new ServiceException("Download Error! data not exists! dataNo = " + dataNo));
        if (data.getFileSize() > 200 * 1024 * 1024) {
            throw new ServerException("Download Error! Files downloaded via http cannot be larger than 200 MB!");
        }
        boolean isPublic = data.getSecurity().equals(SecurityEnum._public.getDesc());
        boolean isOwner = data.getCreator().equals(SecurityUtils.getMemberId());
        boolean isShared = shareRepository.isSharedDataTo(SecurityUtils.getMember().getEmail(), dataNo);
        boolean isAuthorized = resourceAuthorizeRepository.existsAuthorizedDataTo(SecurityUtils.getMemberId(), dataNo);

        if (isPublic || isOwner || isShared || isAuthorized) {
            File file = FileUtil.file(DirConstants.DATA_HOME, data.getFilePath());
            DownloadUtils.download(request, response, file, data.getFileName());
            // 记录日志
            logService.addDownloadLog(AuthorizeType.data.name(), dataNo, data.getCreator(), IpUtils.getIpAddr(request));
            // 增加下载次数
            dataRepository.incDownloadNum(dataNo);
        } else {
            throw new ServerException("Download Error! invalid link: data security error! dataNo = " + dataNo);
        }
    }

    public void exportDownloadLink(String type, String no, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (StrUtil.isBlank(type) || StrUtil.isBlank(no)) {
            throw new ServiceException("Export Error!type or no is null");
        }
        final Member member = SecurityUtils.getMember();
        if (member == null) {
            throw new ServiceException("Please Login.");
        }

        final AuthorizeType typeEnum = AuthorizeType.findByName(type).orElseThrow(() -> new ServiceException("invalid type"));
        final DataListSearchVO searchVO = new DataListSearchVO();
        searchVO.setType(type);
        searchVO.setTypeNo(no);
        searchVO.setCurrMemberId(member.getId());
        // 分页跳到最大
        searchVO.setFindAll(true);
        // 临时文件夹
        File tempDir = getTempDir();
        File analDataTxtFile = FileUtil.file(tempDir, type + "_" + no + "_data_download_link.txt");
        File rawDataTxtFile = FileUtil.file(tempDir, type + "_" + no + "_data_download_link.txt");
        if (typeEnum == AuthorizeType.share) {
            downloadShareLink(no, searchVO, rawDataTxtFile);
        } else if (typeEnum == AuthorizeType.review) {
            downloadReviewLink(no, searchVO, rawDataTxtFile);
        } else {
            // 构建查询
            searchVO.setCurrMemberEmail(member.getEmail());
            R<EsPageInfo<RelatedDataDTO>> rawDataR = remoteDataService.findPageData(searchVO, SecurityConstants.INNER);

            if (rawDataR == null || R.isError(rawDataR)) {
                throw new ServiceException("es service error");
            }

            if (CollUtil.isNotEmpty(rawDataR.getData().getList())) {
                List<RelatedDataDTO> list = filterUnAuthorizedData(rawDataR.getData().getList());
                if (type.equals(AuthorizeType.analysis.name())) {
                    // 写入analData
                    writeAnalDataFile(list, analDataTxtFile);
                } else {
                    // 写入rawData
                    writeRawDataFile(list, rawDataTxtFile);
                }
                if (CollUtil.isNotEmpty(list) && StrUtil.isNotBlank(list.get(0).getCreator())) {
                    String creator = list.get(0).getCreator();
                    logService.addDownloadLog(type, no, creator, IpUtils.getIpAddr(request));
                    dataRepository.incExportNum(type, no);
                }
            } else {
                throw new ServiceException("No data links export");
            }
            // 记录日志
        }
        File zipFile = ZipUtil.zip(tempDir);
        DownloadUtils.download(request, response, zipFile, type + "_" + no + "_data_download_link.zip");
    }

    private void downloadShareLink(final String no, DataListSearchVO searchVO, File rawDataTxtFile) {
        final Share share = shareRepository.findByShareId(no);
        if (share == null) {
            throw new ServiceException("Share data not exists");
        }
        if (!"sharing".equals(share.getStatus())) {
            throw new ServiceException("Data not sharing");
        }
        if (CollUtil.isNotEmpty(share.getDatas())) {
            final Set<String> dataNos = share.getDatas().stream().map(ShareData::getDatNo).filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(dataNos)) {

                searchVO.setDataNos(dataNos);

                final R<EsPageInfo<RelatedDataDTO>> rawDataR = remoteDataService.findPageData(searchVO, SecurityConstants.INNER);
                if (rawDataR == null || R.isError(rawDataR)) {
                    throw new ServiceException("es service error");
                }

                EsPageInfo<RelatedDataDTO> data = rawDataR.getData();

                if (data != null && CollUtil.isNotEmpty(data.getList())) {
                    final List<RelatedDataDTO> list = data.getList();

                    List result = new ArrayList<>();
                    // 是否为analysis
                    boolean isAnal = false;
                    for (RelatedDataDTO dto : list) {
                        if (!isAnal) {
                            isAnal = StrUtil.isNotBlank(dto.getAnalNo());
                        }
                        if (isAnal) {
                            ShareAnalDataDownloadLinkVO vo = RelatedDataDTOMapper.INSTANCE.copyToAnalysisShare(dto);
                            vo.setShareId(no);
                            vo.setUrl(initUrl(dto.getSecurity(), dto.getDatNo()));
                            result.add(vo);
                        } else {
                            ShareDataDownloadLinkVO vo = RelatedDataDTOMapper.INSTANCE.copyToShare(dto);
                            vo.setShareId(no);
                            vo.setUrl(initUrl(dto.getSecurity(), dto.getDatNo()));
                            result.add(vo);
                        }
                    }

                    CsvWriteConfig config = CsvWriteConfig.defaultConfig();
                    config.setFieldSeparator('\t');
                    if (isAnal) {
                        config.setHeaderAlias(new HashMap<String, String>() {
                            {
                                put("shareId", "share_id");
                                put("analNo", "anal_no");
                                put("datNo", "data_id");
                                put("security", "security");
                                put("name", "fileName");
                                put("url", "url");
                                put("md5", "MD5");
                            }
                        });
                    } else {
                        config.setHeaderAlias(new HashMap<String, String>() {
                            {
                                put("shareId", "share_id");
                                put("projNo", "project_id");
                                put("expNo", "experiment_id");
                                put("sapNo", "sample_id");
                                put("runNo", "run_id");
                                put("datNo", "data_id");
                                put("security", "security");
                                put("name", "fileName");
                                put("url", "url");
                                put("md5", "MD5");
                            }
                        });
                    }
                    writeCsv(rawDataTxtFile, config, result);
                }
            }
        }
    }

    private void downloadReviewLink(final String no, DataListSearchVO searchVO, File rawDataTxtFile) {
        Review review = reviewRepository.getReviewByNo(no, null);

        if (review == null) {
            throw new ServiceException("Review data not exists");
        }
        if (!"reviewing".equals(review.getStatus())) {
            throw new ServiceException("Data not reviewing");
        }
        if (CollUtil.isNotEmpty(review.getDatas())) {

            final Set<String> dataNos = review.getDatas().stream().map(ReviewData::getDataNo).filter(Objects::nonNull).collect(Collectors.toSet());

            if (CollUtil.isNotEmpty(dataNos)) {

                searchVO.setDataNos(dataNos);

                final R<EsPageInfo<RelatedDataDTO>> rawDataR = remoteDataService.findPageData(searchVO, SecurityConstants.INNER);
                if (rawDataR == null || R.isError(rawDataR)) {
                    throw new ServiceException("es service error");
                }
                EsPageInfo<RelatedDataDTO> data = rawDataR.getData();

                if (data != null && CollUtil.isNotEmpty(data.getList())) {
                    final List<RelatedDataDTO> list = data.getList();

                    // 是否为analysis
                    boolean isAnal = false;

                    for (RelatedDataDTO dto : list) {
                        if (StrUtil.isNotBlank(dto.getAnalNo())) {
                            isAnal = true;
                            break;
                        }
                    }

                    CsvWriteConfig config = CsvWriteConfig.defaultConfig();
                    config.setFieldSeparator('\t');
                    if (isAnal) {
                        List<ReviewAnalysisDownloadLinkVO> result = new ArrayList<>();

                        for (RelatedDataDTO dto : list) {
                            ReviewAnalysisDownloadLinkVO vo = RelatedDataDTOMapper.INSTANCE.copyToAnalysisReview(dto);
                            vo.setReviewId(no);
                            vo.setUrl(initUrl(dto.getSecurity(), dto.getDatNo()));
                            result.add(vo);
                        }

                        config.setHeaderAlias(new HashMap<String, String>() {
                            {
                                put("reviewId", "review_id");
                                put("analNo", "anal_no");
                                put("datNo", "data_id");
                                put("security", "security");
                                put("name", "fileName");
                                put("url", "url");
                                put("md5", "MD5");
                            }
                        });

                        writeCsv(rawDataTxtFile, config, result);
                    } else {
                        List<ReviewRawDownloadLinkVO> result = new ArrayList<>();

                        for (RelatedDataDTO dto : list) {
                            ReviewRawDownloadLinkVO vo = RelatedDataDTOMapper.INSTANCE.copyToRawReview(dto);
                            vo.setReviewId(no);
                            vo.setUrl(initUrl(dto.getSecurity(), dto.getDatNo()));
                            result.add(vo);
                        }

                        config.setHeaderAlias(new HashMap<String, String>() {
                            {
                                put("reviewId", "review_id");
                                put("projNo", "project_id");
                                put("expNo", "experiment_id");
                                put("sapNo", "sample_id");
                                put("runNo", "run_id");
                                put("datNo", "data_id");
                                put("security", "security");
                                put("name", "fileName");
                                put("url", "url");
                                put("md5", "MD5");
                            }
                        });

                        writeCsv(rawDataTxtFile, config, result);
                    }
                }
            }
        }
    }

    private static <T> void writeCsv(final File rawDataTxtFile, final CsvWriteConfig config, final List<T> data) {
        try (CsvWriter writer = CsvUtil.getWriter(rawDataTxtFile, StandardCharsets.UTF_8, false, config)) {
            writer.writeBeans(data);
        }
    }

    public void batchExportDownloadLink(List<BatchExportParamsDTO.ExportParam> dtos, HttpServletRequest request, HttpServletResponse response) throws IOException {
        List<RelatedDataDTO> rawDataList = new ArrayList<>();
        List<RelatedDataDTO> analDataList = new ArrayList<>();
        for (BatchExportParamsDTO.ExportParam dto : dtos) {
            String type = dto.getType();
            String typeNo = dto.getTypeNo();
            // 构建查询
            DataListSearchVO searchVO = new DataListSearchVO();
            searchVO.setType(type);
            searchVO.setTypeNo(typeNo);
            searchVO.setCurrMemberId(SecurityUtils.getMemberId());
            searchVO.setCurrMemberEmail(SecurityUtils.getMember().getEmail());
            // 分页调到最大
            searchVO.setFindAll(true);

            R<EsPageInfo<RelatedDataDTO>> dataR = remoteDataService.findPageData(searchVO, SecurityConstants.INNER);
            if (dataR == null || R.isError(dataR)) {
                throw new ServiceException("es service error");
            }

            EsPageInfo<RelatedDataDTO> dataPage = dataR.getData();
            List<RelatedDataDTO> list = dataPage.getList();

            if (type.equals(AuthorizeType.analysis.name())) {
                analDataList.addAll(list);
            } else {
                rawDataList.addAll(list);
            }

            if (CollUtil.isNotEmpty(list) && StrUtil.isNotBlank(list.get(0).getCreator())) {
                String creator = list.get(0).getCreator();
                logService.addDownloadLog(type, typeNo, creator, IpUtils.getIpAddr(request));
            }
        }

        // 临时文件夹
        File tempDir = getTempDir();
        File rawDataTxtFile = FileUtil.file(tempDir, "raw_data_download_link.txt");
        File analDataTxtFile = FileUtil.file(tempDir, "analysis_data_download_link.txt");

        if (CollUtil.isNotEmpty(rawDataList)) {
            List<RelatedDataDTO> list = filterUnAuthorizedData(rawDataList);
            // 写入rawData
            writeRawDataFile(list, rawDataTxtFile);
        }
        if (CollUtil.isNotEmpty(analDataList)) {
            List<RelatedDataDTO> list = filterUnAuthorizedData(analDataList);
            // 写入analData
            writeAnalDataFile(list, analDataTxtFile);
        }

        File zipFile = ZipUtil.zip(tempDir);
        DownloadUtils.download(request, response, zipFile, "batch_data_download_link.zip");

    }

    private static void writeRawDataFile(List<RelatedDataDTO> list, File rawDataTxtFile) {
        List<DataDownloadLinkVO> result = new ArrayList<>();
        for (RelatedDataDTO dto : list) {
            DataDownloadLinkVO vo = RelatedDataDTOMapper.INSTANCE.copyToData(dto);
            vo.setUrl(initUrl(dto.getSecurity(), dto.getDatNo()));
            vo.setFtpFilePath(initFtpFilePath(dto.getSecurity(), AuthorizeType.run.name(), dto.getRunNo(), dto.getName()));
            result.add(vo);
        }

        CsvWriteConfig config = CsvWriteConfig.defaultConfig();
        config.setFieldSeparator('\t');
        config.setHeaderAlias(new HashMap<String, String>() {
            {
                put("projNo", "project_id");
                put("expNo", "experiment_id");
                put("sapNo", "sample_id");
                put("runNo", "run_id");
                put("datNo", "data_id");
                put("security", "security");
                put("name", "fileName");
                put("url", "url");
                put("ftpFilePath", "ftp_file_path");
                put("md5", "MD5");
            }
        });
        writeCsv(rawDataTxtFile, config, result);
    }

    private static String initUrl(String security, String no) {
        if (SecurityEnum._public.getDesc().equals(security)) {
            return WebConstants.WEB_DOMAIN + "/download/node/data/public/" + no;
        } else {
            return WebConstants.WEB_DOMAIN + "/download/node/data/" + no;
        }
    }

    private static String initFtpFilePath(String security, String type, String no, String name) {
        String pathType = StrUtil.equals(AuthorizeType.run.name(), type) ? "byRun" : "byAnalysis";
        if (SecurityEnum._private.getDesc().equals(security)) {
            return "/Private/" + pathType + "/" + no + "/" + name;
        } else {
            return "/Public/" + pathType + MyFileUtils.noToFtpFilePath(no) + "/" + name;
        }
    }

    private static void writeAnalDataFile(List<RelatedDataDTO> list, File analDataTxtFile) {
        // 生成结果数据
        List<AnalDataDownloadLinkVO> analDataResult = new ArrayList<>();
        for (RelatedDataDTO dto : list) {
            AnalDataDownloadLinkVO vo = RelatedDataDTOMapper.INSTANCE.copyToAnal(dto);
            /*if (dto.getSecurity().equals(SecurityEnum._public.getDesc())) {
                vo.setUrl(WebConstants.WEB_DOMAIN + "/download/node/data/public/" + dto.getDatNo());
            } else {
                vo.setUrl(WebConstants.WEB_DOMAIN + "/download/node/data/" + dto.getDatNo());
            }*/
            vo.setFtpFilePath(initFtpFilePath(dto.getSecurity(), AuthorizeType.analysis.name(), dto.getAnalNo(), dto.getName()));
            vo.setUrl(initUrl(dto.getSecurity(), dto.getDatNo()));
            analDataResult.add(vo);
        }
        CsvWriteConfig config = CsvWriteConfig.defaultConfig();
        config.setFieldSeparator('\t');
        config.setHeaderAlias(new HashMap<String, String>() {
            {
                put("analNo", "analysis_id");
                put("datNo", "data_id");
                put("security", "security");
                put("name", "fileName");
                put("url", "url");
                put("ftpFilePath", "ftp_file_path");
                put("md5", "MD5");
            }
        });
        writeCsv(analDataTxtFile, config, analDataResult);
    }

    public List<RelatedDataDTO> filterUnAuthorizedData(List<RelatedDataDTO> dataDTOList) {
        if (CollUtil.isEmpty(dataDTOList)) {
            return new ArrayList<>();
        }
        // 筛选出未公开的数据
        Set<String> notPublicDataNos = dataDTOList.stream()
                .filter(x -> CollUtil.contains(SecurityEnum.notPublicSecurity(), x.getSecurity())).map(RelatedDataDTO::getDatNo).collect(Collectors.toSet());
        // 这些未公开的中被分享的DataNo
        List<String> sharedDataNos = shareRepository.sharedDataNo(Objects.requireNonNull(SecurityUtils.getMember()).getEmail(), notPublicDataNos);

        // 查询授权的数据
        List<ResourceAuthorize> authorizedList = resourceAuthorizeRepository.searchAuthorizeListByData(SecurityUtils.getMemberId(), notPublicDataNos);

        // 授权的DataNo
        Set<String> authorizedDataNos = new HashSet<>();

        if (CollUtil.isNotEmpty(authorizedList)) {
            for (ResourceAuthorize resourceAuthorize : authorizedList) {
                authorizedDataNos.addAll(resourceAuthorize.getData());
            }
        }

        List<RelatedDataDTO> result = new ArrayList<>();
        for (RelatedDataDTO dto : dataDTOList) {
            if (dto.getSecurity().equals(SecurityEnum._public.getDesc())) {
                result.add(dto);
            } else if (dto.getCreator().equals(SecurityUtils.getMemberId())) {
                result.add(dto);
            } else if (sharedDataNos.contains(dto.getDatNo()) || authorizedDataNos.contains(dto.getDatNo())) {
                result.add(dto);
            }
        }
        return result;
    }

    public void exportReviewData(String reviewNo, String code, String dataNo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        Review review = reviewRepository.getReviewByNo(reviewNo, code);
        if (review == null) {
            // 查询历史用过的ID
            review = reviewRepository.getReviewByUsedIds(reviewNo, code);
            if (review == null) {
                throw new ServiceException("The data does not exist!");
            }
        }
        if (ReviewStatusEnum.cancled.name().equals(review.getStatus()) || ReviewStatusEnum.expired.name().equals(review.getStatus()) || review.getExpireDate().before(new Date())) {
            throw new ServiceException("Download Error! Review expired or cancelled!");
        }

        Data data = dataRepository.findByDatNo(dataNo).orElseThrow(() -> new ServiceException("Download Error! data not exists! dataNo = " + dataNo));
        // 记录日志
        logService.addDownloadLog(AuthorizeType.data.name(), dataNo, data.getCreator(), IpUtils.getIpAddr(request));
        // 增加下载次数
        dataRepository.incDownloadNum(dataNo);
        File file = FileUtil.file(DirConstants.DATA_HOME, data.getFilePath());
        DownloadUtils.download(request, response, file, data.getFileName());
    }

    public void exportPublicDownloadLink(DataSearchDTO dto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (CollUtil.isEmpty(dto.getDataNos())) {
            throw new ServerException("Please select Data");
        }
        R<List<RelatedDataDTO>> r = remoteDataService.findAllByDataNos(dto, SecurityConstants.INNER);
        List<RelatedDataDTO> dataList = r.getData();
        List<RelatedDataDTO> rawDataList = new ArrayList<>();
        List<RelatedDataDTO> analDataList = new ArrayList<>();
        for (RelatedDataDTO dataDTO : dataList) {
            if (dataDTO.getSecurity().equals(SecurityEnum._public.getDesc())) {
                if (StrUtil.isNotBlank(dataDTO.getAnalNo())) {
                    analDataList.add(dataDTO);
                } else {
                    rawDataList.add(dataDTO);
                }
            }
        }

        // 临时文件夹
        File tempDir = getTempDir();
        File rawDataTxtFile = FileUtil.file(tempDir, "raw_data_download_link.txt");
        File analDataTxtFile = FileUtil.file(tempDir, "analysis_data_download_link.txt");

        if (CollUtil.isNotEmpty(rawDataList)) {
            // 写入rawData
            writeRawDataFile(rawDataList, rawDataTxtFile);
        }
        if (CollUtil.isNotEmpty(analDataList)) {
            // 写入analData
            writeAnalDataFile(analDataList, analDataTxtFile);
        }

        File zipFile = ZipUtil.zip(tempDir);
        DownloadUtils.download(request, response, zipFile, "batch_data_download_link.zip");
    }


}
