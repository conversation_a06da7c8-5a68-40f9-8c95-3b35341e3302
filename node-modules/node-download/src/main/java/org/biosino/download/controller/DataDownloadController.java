package org.biosino.download.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.controller.BaseController;
import org.biosino.download.dto.BatchExportParamsDTO;
import org.biosino.download.service.DataDownloadService;
import org.biosino.es.api.dto.DataSearchDTO;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/3/19
 */
@RestController
@RequiredArgsConstructor
public class DataDownloadController extends BaseController {


    private final DataDownloadService dataDownloadService;

    @RequestMapping("/node/data/public/{dataNo}")
    public void downloadPublicByDataNo(@PathVariable String dataNo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        dataDownloadService.downloadPublicData(dataNo, request, response);
    }

    @RequestMapping("/node/data/{dataNo}")
    public void downloadByDataNo(@PathVariable String dataNo, HttpServletRequest request, HttpServletResponse response) throws IOException {
        dataDownloadService.downloadDataByDataNo(dataNo, request, response);
    }

    @RequestMapping("/node/exportDownloadLink/{type}/{no}")
    public void exportDownloadLink(@PathVariable String type, @PathVariable String no, HttpServletRequest request, HttpServletResponse response) throws IOException {
        dataDownloadService.exportDownloadLink(type, no, request, response);
    }

    @RequestMapping("/node/batchExportDownloadLink")
    public void batchExportDownloadLink(BatchExportParamsDTO dto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        dataDownloadService.batchExportDownloadLink(dto.getParams(), request, response);
    }

    /**
     * 导出公共数据的下载链接
     */
    @RequestMapping("/node/exportPublicDownloadLink")
    public void exportPublicDownloadLink(DataSearchDTO dto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        dataDownloadService.exportPublicDownloadLink(dto, request, response);
    }

    /**
     * 下载review详情的data数据
     */
    @RequestMapping("/node/review/{reviewId}/{code}/{dataNo}")
    public void downloadByReview(@PathVariable String reviewId, @PathVariable String dataNo, @PathVariable String code, HttpServletRequest request, HttpServletResponse response) throws IOException {
        dataDownloadService.exportReviewData(reviewId, code, dataNo, request, response);
    }

}
