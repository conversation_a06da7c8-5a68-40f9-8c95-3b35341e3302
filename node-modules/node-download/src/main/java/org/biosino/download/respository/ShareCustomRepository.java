package org.biosino.download.respository;

import org.biosino.common.mongo.entity.Share;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface ShareCustomRepository {

    boolean isSharedDataTo(String email, String dataNo);

    List<String> sharedDataNo(String email, Collection<String> notPublicDataNos);

    Share findByShareId(String shareId);
}
