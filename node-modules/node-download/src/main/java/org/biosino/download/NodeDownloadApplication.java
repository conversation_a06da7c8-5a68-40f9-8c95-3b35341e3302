package org.biosino.download;

import org.biosino.common.security.annotation.EnableCustomConfig;
import org.biosino.common.security.annotation.EnableRyFeignClients;
import org.biosino.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * <AUTHOR> @date 2024/3/19
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@ComponentScan(basePackages = {"org.biosino.common.mongo.entity", "org.biosino.download"})
public class NodeDownloadApplication {
    public static void main(String[] args) {
        SpringApplication.run(NodeDownloadApplication.class, args);
        System.out.println("Node download模块启动成功");
    }
}
