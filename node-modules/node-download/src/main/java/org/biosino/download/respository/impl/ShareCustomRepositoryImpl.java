package org.biosino.download.respository.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.ShareStatusEnum;
import org.biosino.common.mongo.entity.Share;
import org.biosino.common.mongo.entity.other.ShareData;
import org.biosino.download.respository.ShareCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/18
 */
@RequiredArgsConstructor
public class ShareCustomRepositoryImpl implements ShareCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public boolean isSharedDataTo(String email, String dataNo) {
        Criteria criteria = Criteria.where("share_to")
                .in(CollUtil.newArrayList(email))
                .and("datas.data_no").in(CollUtil.newArrayList(dataNo))
                .and("status").is(ShareStatusEnum.sharing.name());
        return mongoTemplate.exists(Query.query(criteria), Share.class);
    }

    @Override
    public List<String> sharedDataNo(String email, Collection<String> dataNos) {
        Criteria criteria = Criteria.where("share_to")
                .in(CollUtil.newArrayList(email))
                .and("datas.data_no").in(dataNos)
                .and("status").is(ShareStatusEnum.sharing.name());
        Query query = new Query(criteria);
        query.fields().include("datas");
        List<Share> shares = mongoTemplate.find(query, Share.class);
        // 将datas取出来，放到一个List<ShareData>
        List<ShareData> shareData = new ArrayList<>();
        for (Share share : shares) {
            shareData.addAll(share.getDatas());
        }
        // 通过交集，判断哪些是被分享过的
        Set<String> collect = shareData.stream().map(ShareData::getDatNo).collect(Collectors.toSet());
        Set<String> sharedDataNo = CollUtil.intersectionDistinct(dataNos, collect);
        return new ArrayList<>(sharedDataNo);
    }

    @Override
    public Share findByShareId(String shareId) {
        Query query = new Query();
        Criteria criteria = Criteria.where("share_id").is(shareId);
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Share.class);
    }

}
