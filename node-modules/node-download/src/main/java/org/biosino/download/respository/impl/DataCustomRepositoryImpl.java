package org.biosino.download.respository.impl;

import com.mongodb.BasicDBObject;
import com.mongodb.client.model.UpdateOptions;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.download.respository.DataCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;

/**
 * <AUTHOR> @date 2024/1/4
 */
@RequiredArgsConstructor
public class DataCustomRepositoryImpl implements DataCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public void incDownloadNum(String dataNo) {
        BasicDBObject query = new BasicDBObject();
        query.put("dat_no", dataNo);
        BasicDBObject update = new BasicDBObject();
        update.put("download_num", 1);
        BasicDBObject updateSet = new BasicDBObject("$inc", update);
        mongoTemplate.getCollection("data").updateOne(query, updateSet, new UpdateOptions().upsert(true));
    }

    @Override
    public void incExportNum(String type, String no) {
        TypeInformation typeInformation = TypeInformation.typeInfoMap.get(type);
        if (typeInformation == null) {
            return;
        }
        BasicDBObject query = new BasicDBObject();
        query.put(typeInformation.getMongoField(), no);
        BasicDBObject update = new BasicDBObject();
        update.put("export_num", 1L);
        BasicDBObject updateSet = new BasicDBObject("$inc", update);
        mongoTemplate.getCollection(typeInformation.getAuthorizeType()).updateOne(query, updateSet, new UpdateOptions().upsert(true));
    }
}
