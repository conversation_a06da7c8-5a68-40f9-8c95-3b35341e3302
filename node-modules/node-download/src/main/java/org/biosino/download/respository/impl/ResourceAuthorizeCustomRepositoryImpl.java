package org.biosino.download.respository.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.biosino.common.core.enums.RequestStatusEnum;
import org.biosino.common.mongo.entity.ResourceAuthorize;
import org.biosino.download.respository.ResourceAuthorizeCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@RequiredArgsConstructor
public class ResourceAuthorizeCustomRepositoryImpl implements ResourceAuthorizeCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public boolean existsAuthorizedDataTo(String memberId, String dataNo) {
        List<Criteria> criteriaList = new ArrayList<>();

        criteriaList.add(Criteria.where("authorize_to").is(memberId));
        criteriaList.add(Criteria.where("data").in(CollUtil.newArrayList(dataNo)));
        criteriaList.add(Criteria.where("status").is(RequestStatusEnum.authorized.getDesc()));
        criteriaList.add(new Criteria().orOperator(Criteria.where("expire_date").gt(new Date()), Criteria.where("expire_date").exists(false)));
        Query query = new Query(new Criteria().andOperator(criteriaList));
        return mongoTemplate.exists(query, ResourceAuthorize.class);
    }

    @Override
    public List<ResourceAuthorize> searchAuthorizeListByData(String memberId, Collection<String> dataIds) {
        if (StringUtils.isBlank(memberId)) {
            return new ArrayList<>();
        }
        List<Criteria> criteriaList = new ArrayList<>();

        criteriaList.add(Criteria.where("authorize_to").is(memberId));

        if (CollUtil.isNotEmpty(dataIds)) {
            criteriaList.add(Criteria.where("data").in(dataIds));
        }

        criteriaList.add(Criteria.where("status").is(RequestStatusEnum.authorized.getDesc()));
        // criteriaList.add(Criteria.where("expire_date").gt(new Date()));

        // 未过期，或者 没有过期时间
        criteriaList.add(new Criteria().orOperator(Criteria.where("expire_date").gt(new Date()), Criteria.where("expire_date").exists(false)));

        Query query = new Query(new Criteria().andOperator(criteriaList));

        return mongoTemplate.find(query, ResourceAuthorize.class);
    }

}
