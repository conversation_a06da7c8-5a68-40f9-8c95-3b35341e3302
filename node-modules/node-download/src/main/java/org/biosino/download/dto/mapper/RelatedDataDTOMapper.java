package org.biosino.download.dto.mapper;

import org.biosino.download.vo.*;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface RelatedDataDTOMapper {
    RelatedDataDTOMapper INSTANCE = Mappers.getMapper(RelatedDataDTOMapper.class);

    ShareDataDownloadLinkVO copyToShare(RelatedDataDTO dataDTO);

    ShareAnalDataDownloadLinkVO copyToAnalysisShare(RelatedDataDTO dataDTO);

    ReviewRawDownloadLinkVO copyToRawReview(RelatedDataDTO dataDTO);

    ReviewAnalysisDownloadLinkVO copyToAnalysisReview(RelatedDataDTO dataDTO);

    DataDownloadLinkVO copyToData(RelatedDataDTO dataDTO);

    AnalDataDownloadLinkVO copyToAnal(RelatedDataDTO dataDTO);

}
